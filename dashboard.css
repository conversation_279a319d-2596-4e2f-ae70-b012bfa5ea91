/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', '<PERSON><PERSON>wal', system-ui, sans-serif;
    background: linear-gradient(135deg, #e0e5ec 0%, #f5f7fa 100%);
    min-height: 100vh;
    color: #2d3748;
    direction: rtl;
    overflow-x: hidden;
}

/* Header styles removed - using sidebar navigation */

/* Main Content */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 30px 20px;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Search Section */
.search-section {
    animation: fadeInDown 0.6s ease-out;
}

.search-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
    background: #e0e5ec;
    border-radius: 20px;
    box-shadow: inset 8px 8px 16px #a3b1c6, inset -8px -8px 16px #ffffff;
    padding: 5px;
}

.search-icon {
    position: absolute;
    right: 20px;
    width: 20px;
    height: 20px;
    color: #718096;
    z-index: 1;
}

.search-input {
    width: 100%;
    padding: 16px 60px 16px 50px;
    background: transparent;
    border: none;
    font-size: 1rem;
    color: #2d3748;
    font-family: inherit;
    outline: none;
}

.search-input::placeholder {
    color: #a0aec0;
}

.clear-search {
    position: absolute;
    left: 15px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
    opacity: 0;
    visibility: hidden;
}

.clear-search.visible {
    opacity: 1;
    visibility: visible;
}

.clear-search:hover {
    background: rgba(163, 177, 198, 0.1);
}

.clear-search svg {
    width: 16px;
    height: 16px;
    color: #718096;
}

.search-filters {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-select {
    background: #e0e5ec;
    border: none;
    padding: 12px 16px;
    border-radius: 12px;
    font-family: inherit;
    font-size: 0.9rem;
    color: #4a5568;
    cursor: pointer;
    box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
    transition: all 0.3s ease;
    min-width: 150px;
}

.filter-select:focus {
    outline: none;
    box-shadow: inset 2px 2px 4px #a3b1c6, inset -2px -2px 4px #ffffff;
}

/* Statistics Section */
.stats-section {
    animation: fadeInUp 0.6s ease-out 0.1s both;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    background: #e0e5ec;
    padding: 25px;
    border-radius: 20px;
    box-shadow: 12px 12px 24px #a3b1c6, -12px -12px 24px #ffffff;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    box-shadow: 8px 8px 16px #a3b1c6, -8px -8px 16px #ffffff;
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: inset 6px 6px 12px #a3b1c6, inset -6px -6px 12px #ffffff;
}

.stat-card.delivering .stat-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-card.delayed .stat-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-card.returned .stat-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.stat-card.pending .stat-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-icon svg {
    width: 28px;
    height: 28px;
    color: white;
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 2.2rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 1rem;
    color: #4a5568;
    font-weight: 500;
}

/* Tabs Section */
.tabs-section {
    animation: fadeInUp 0.6s ease-out 0.2s both;
}

.tabs-container {
    background: #e0e5ec;
    border-radius: 25px;
    box-shadow: 15px 15px 30px #a3b1c6, -15px -15px 30px #ffffff;
    overflow: hidden;
}

.tabs-header {
    display: flex;
    background: #e0e5ec;
    border-bottom: 1px solid rgba(163, 177, 198, 0.2);
}

.tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: 20px 15px;
    font-family: inherit;
    font-size: 1rem;
    font-weight: 600;
    color: #4a5568;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.tab-btn:hover {
    background: rgba(163, 177, 198, 0.1);
}

.tab-btn.active {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.tab-count {
    background: #a0aec0;
    color: white;
    font-size: 0.8rem;
    font-weight: 700;
    padding: 2px 8px;
    border-radius: 12px;
    min-width: 24px;
    text-align: center;
}

.tab-btn.active .tab-count {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

/* Tab Content */
.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

.orders-table-container {
    background: #e0e5ec;
    border-radius: 20px;
    box-shadow: inset 8px 8px 16px #a3b1c6, inset -8px -8px 16px #ffffff;
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(163, 177, 198, 0.2);
}

.table-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1a202c;
}

.refresh-btn {
    background: #e0e5ec;
    border: none;
    padding: 10px 15px;
    border-radius: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: inherit;
    font-size: 0.9rem;
    color: #4a5568;
    box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    box-shadow: 2px 2px 4px #a3b1c6, -2px -2px 4px #ffffff;
    transform: translateY(-1px);
}

.refresh-btn svg {
    width: 16px;
    height: 16px;
}

.table-wrapper {
    overflow-x: auto;
    max-height: 600px;
    overflow-y: auto;
}

.orders-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.orders-table th {
    background: rgba(163, 177, 198, 0.1);
    padding: 15px 12px;
    text-align: right;
    font-weight: 600;
    color: #2d3748;
    border-bottom: 1px solid rgba(163, 177, 198, 0.2);
    position: sticky;
    top: 0;
    z-index: 10;
}

.orders-table td {
    padding: 15px 12px;
    border-bottom: 1px solid rgba(163, 177, 198, 0.1);
    color: #4a5568;
}

.orders-table tbody tr {
    transition: all 0.2s ease;
}

.orders-table tbody tr:hover {
    background: rgba(163, 177, 198, 0.05);
}

.order-id {
    font-weight: 600;
    color: #3b82f6;
    cursor: pointer;
}

.order-id:hover {
    text-decoration: underline;
}

.amount {
    font-weight: 600;
    color: #059669;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
}

.status-delivering {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
    color: #065f46;
}

.status-delayed {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #92400e;
}

.status-returned {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
}

.status-pending {
    background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
    color: #3730a3;
}

.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.action-btn {
    background: #e0e5ec;
    border: none;
    padding: 6px 12px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.8rem;
    color: #4a5568;
    box-shadow: 2px 2px 4px #a3b1c6, -2px -2px 4px #ffffff;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.action-btn:hover {
    box-shadow: 1px 1px 2px #a3b1c6, -1px -1px 2px #ffffff;
    transform: translateY(-1px);
}

.action-btn.primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.action-btn.success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.action-btn.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.action-btn.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: #e0e5ec;
    border-radius: 25px;
    box-shadow: 20px 20px 40px #a3b1c6, -20px -20px 40px #ffffff;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid rgba(163, 177, 198, 0.2);
}

.modal-header h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: #1a202c;
}

.close-modal {
    background: #e0e5ec;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
    transition: all 0.2s ease;
}

.close-modal:hover {
    box-shadow: 2px 2px 4px #a3b1c6, -2px -2px 4px #ffffff;
}

.close-modal svg {
    width: 18px;
    height: 18px;
    color: #4a5568;
}

.modal-body {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-state svg {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: #4a5568;
}

.empty-state p {
    font-size: 0.9rem;
}

/* Loading State */
.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    gap: 15px;
    color: #4a5568;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(163, 177, 198, 0.3);
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

/* Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 12px 15px;
        flex-direction: column;
        gap: 15px;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .main-content {
        padding: 20px 15px;
        gap: 20px;
    }

    .search-filters {
        flex-direction: column;
    }

    .filter-select {
        min-width: auto;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stat-card {
        padding: 20px;
    }

    .tabs-header {
        flex-direction: column;
    }

    .tab-btn {
        padding: 15px;
    }

    .tab-content {
        padding: 20px 15px;
    }

    .table-wrapper {
        font-size: 0.8rem;
    }

    .orders-table th,
    .orders-table td {
        padding: 10px 8px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .modal-header,
    .modal-body {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .company-name {
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .search-input {
        padding: 14px 50px 14px 40px;
    }

    .orders-table {
        font-size: 0.75rem;
    }

    .orders-table th,
    .orders-table td {
        padding: 8px 6px;
    }
}

/* Interactive Elements Enhancement */
.order-row {
    transition: all 0.3s ease;
    cursor: pointer;
}

.order-row:hover {
    background: rgba(59, 130, 246, 0.08) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.order-row:active {
    transform: translateY(0);
}

.stat-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 15px 15px 30px #a3b1c6, -15px -15px 30px #ffffff;
}

.stat-card:active {
    transform: translateY(-1px);
}

.order-id {
    color: #3b82f6;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.order-id:hover {
    color: #2563eb;
    text-decoration: underline;
}

/* Click feedback */
.clickable {
    cursor: pointer;
    user-select: none;
    transition: transform 0.1s ease;
}

.clickable:active {
    transform: scale(0.98);
}

/* Enhanced button interactions */
.action-btn {
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 3px 3px 6px #a3b1c6, -3px -3px 6px #ffffff;
}

.action-btn:active {
    transform: translateY(0);
    box-shadow: 1px 1px 2px #a3b1c6, -1px -1px 2px #ffffff;
}

/* Mobile responsive interactions */
@media (max-width: 768px) {
    .order-row:hover {
        background: rgba(59, 130, 246, 0.1) !important;
        transform: none;
        box-shadow: none;
    }

    .stat-card:hover {
        transform: translateY(-1px);
        box-shadow: 12px 12px 24px #a3b1c6, -12px -12px 24px #ffffff;
    }
}
