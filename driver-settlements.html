<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصفية حسابات المندوب - شركة التوصيل السريع</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="sidebar.css">
    <link rel="stylesheet" href="orders.css">
</head>
<body>
    <main class="main-content">
        <section class="action-bar">
            <div class="action-bar-content">
                <div class="page-title">
                    <h2>تصفية حسابات المندوب</h2>
                    <p>تسوية وتصفية حسابات المندوبين النهائية</p>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="new-settlement-btn">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14,2 14,8 20,8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10,9 9,9 8,9"/>
                        </svg>
                        تصفية جديدة
                    </button>
                    <button class="btn btn-success" id="export-settlements-btn">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="7,10 12,15 17,10"/>
                            <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                        تصدير التقرير
                    </button>
                </div>
            </div>
        </section>

        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card delivering">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="1" x2="12" y2="23"/>
                            <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number" data-is-currency="true" data-original-amount="18750">18,750</h3>
                        <p class="stat-label" data-currency-label="true">إجمالي المستحقات (ر.س)</p>
                    </div>
                </div>
                
                <div class="stat-card delayed">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                            <polyline points="22,4 12,14.01 9,11.01"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number" data-is-currency="true" data-original-amount="12200">12,200</h3>
                        <p class="stat-label" data-currency-label="true">المدفوع (ر.س)</p>
                    </div>
                </div>
                
                <div class="stat-card pending">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <line x1="12" y1="8" x2="12" y2="12"/>
                            <line x1="12" y1="16" x2="12.01" y2="16"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number" data-is-currency="true" data-original-amount="6550">6,550</h3>
                        <p class="stat-label" data-currency-label="true">المتبقي للتصفية (ر.س)</p>
                    </div>
                </div>
                
                <div class="stat-card returned">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">5</h3>
                        <p class="stat-label">مندوبين يحتاجون تصفية</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="orders-section">
            <div class="orders-container">
                <div class="table-header">
                    <div class="table-info">
                        <h3>حسابات المندوبين للتصفية</h3>
                        <span class="orders-count">5 مندوبين</span>
                    </div>
                    <div class="table-actions">
                        <button class="refresh-btn" onclick="refreshSettlements()">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="23,4 23,10 17,10"/>
                                <polyline points="1,20 1,14 7,14"/>
                                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                            </svg>
                            تحديث
                        </button>
                    </div>
                </div>

                <div class="table-wrapper">
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>اسم المندوب</th>
                                <th>الطلبات المكتملة</th>
                                <th>إجمالي المستحقات</th>
                                <th>المدفوع</th>
                                <th>المتبقي</th>
                                <th>آخر تسوية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>أحمد محمد</td>
                                <td>35</td>
                                <td class="amount" data-original-amount="3500.00">3,500.00 ر.س</td>
                                <td class="amount" data-original-amount="2800.00">2,800.00 ر.س</td>
                                <td class="amount" data-original-amount="700.00">700.00 ر.س</td>
                                <td>2024-06-25</td>
                                <td><span class="status-badge status-pending">يحتاج تصفية</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn primary" onclick="startSettlement('أحمد محمد')">تصفية</button>
                                        <button class="action-btn" onclick="viewDriverAccount('أحمد محمد')">كشف حساب</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>سعد عبدالله</td>
                                <td>28</td>
                                <td class="amount" data-original-amount="2800.00">2,800.00 ر.س</td>
                                <td class="amount" data-original-amount="2100.00">2,100.00 ر.س</td>
                                <td class="amount" data-original-amount="700.00">700.00 ر.س</td>
                                <td>2024-06-20</td>
                                <td><span class="status-badge status-pending">يحتاج تصفية</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn primary" onclick="startSettlement('سعد عبدالله')">تصفية</button>
                                        <button class="action-btn" onclick="viewDriverAccount('سعد عبدالله')">كشف حساب</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>خالد أحمد</td>
                                <td>22</td>
                                <td class="amount" data-original-amount="2200.00">2,200.00 ر.س</td>
                                <td class="amount" data-original-amount="2200.00">2,200.00 ر.س</td>
                                <td class="amount" data-original-amount="0.00">0.00 ر.س</td>
                                <td>2024-06-30</td>
                                <td><span class="status-badge status-delivering">مصفى</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn" onclick="viewDriverAccount('خالد أحمد')">كشف حساب</button>
                                        <button class="action-btn success" onclick="printSettlement('خالد أحمد')">طباعة</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>محمد سالم</td>
                                <td>42</td>
                                <td class="amount" data-original-amount="4200.00">4,200.00 ر.س</td>
                                <td class="amount" data-original-amount="3000.00">3,000.00 ر.س</td>
                                <td class="amount" data-original-amount="1200.00">1,200.00 ر.س</td>
                                <td>2024-06-18</td>
                                <td><span class="status-badge status-delayed">متأخر</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn warning" onclick="startSettlement('محمد سالم')">تصفية عاجلة</button>
                                        <button class="action-btn" onclick="viewDriverAccount('محمد سالم')">كشف حساب</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>عبدالرحمن علي</td>
                                <td>31</td>
                                <td class="amount" data-original-amount="3100.00">3,100.00 ر.س</td>
                                <td class="amount" data-original-amount="2500.00">2,500.00 ر.س</td>
                                <td class="amount" data-original-amount="600.00">600.00 ر.س</td>
                                <td>2024-06-22</td>
                                <td><span class="status-badge status-pending">يحتاج تصفية</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn primary" onclick="startSettlement('عبدالرحمن علي')">تصفية</button>
                                        <button class="action-btn" onclick="viewDriverAccount('عبدالرحمن علي')">كشف حساب</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </main>

    <!-- Settlement Modal -->
    <div id="settlement-modal" class="modal hidden">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>تصفية حساب المندوب</h3>
                <button class="close-modal" onclick="closeSettlementModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">اسم المندوب</label>
                        <input type="text" id="settlement-driver" class="form-input" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">إجمالي المستحقات</label>
                        <input type="text" id="settlement-total" class="form-input" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">المدفوع سابقاً</label>
                        <input type="text" id="settlement-paid" class="form-input" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">المبلغ المتبقي</label>
                        <input type="text" id="settlement-remaining" class="form-input" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">مبلغ التصفية</label>
                        <input type="number" id="settlement-amount" class="form-input" step="0.01" min="0">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">طريقة الدفع</label>
                        <select id="settlement-method" class="form-select" required>
                            <option value="">اختر طريقة الدفع</option>
                            <option value="cash">نقداً</option>
                            <option value="bank">تحويل بنكي</option>
                            <option value="check">شيك</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">ملاحظات التصفية</label>
                    <textarea id="settlement-notes" class="form-textarea" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeSettlementModal()">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="confirmSettlement()">تأكيد التصفية</button>
                </div>
            </div>
        </div>
    </div>

    <script src="sidebar.js"></script>
    <script src="currency.js"></script>
    <script>
        let currentDriver = null;
        
        function startSettlement(driverName) {
            currentDriver = driverName;
            
            // Get driver details (in real app, fetch from server)
            const driverData = getDriverData(driverName);
            
            document.getElementById('settlement-driver').value = driverName;
            document.getElementById('settlement-total').value = formatCurrency(driverData.total);
            document.getElementById('settlement-paid').value = formatCurrency(driverData.paid);
            document.getElementById('settlement-remaining').value = formatCurrency(driverData.remaining);
            document.getElementById('settlement-amount').value = driverData.remaining;
            document.getElementById('settlement-method').value = '';
            document.getElementById('settlement-notes').value = '';
            
            document.getElementById('settlement-modal').classList.remove('hidden');
        }
        
        function closeSettlementModal() {
            document.getElementById('settlement-modal').classList.add('hidden');
            currentDriver = null;
        }
        
        function confirmSettlement() {
            const amount = parseFloat(document.getElementById('settlement-amount').value);
            const method = document.getElementById('settlement-method').value;
            const notes = document.getElementById('settlement-notes').value;
            
            if (!amount || amount <= 0) {
                alert('يرجى إدخال مبلغ صحيح');
                return;
            }
            
            if (!method) {
                alert('يرجى اختيار طريقة الدفع');
                return;
            }
            
            // Process settlement (in real app, send to server)
            showNotification(`تم تصفية حساب ${currentDriver} بمبلغ ${formatCurrency(amount)}`, 'success');
            
            // Update the row
            updateDriverStatus(currentDriver, amount);
            
            closeSettlementModal();
        }
        
        function updateDriverStatus(driverName, amount) {
            // Find and update the row
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const nameCell = row.querySelector('td:first-child');
                if (nameCell && nameCell.textContent === driverName) {
                    const remainingCell = row.querySelector('td:nth-child(5)');
                    const currentRemaining = parseFloat(remainingCell.textContent.replace(/[^\d.-]/g, ''));
                    const newRemaining = currentRemaining - amount;
                    
                    remainingCell.textContent = formatCurrency(newRemaining);
                    
                    if (newRemaining <= 0) {
                        const statusCell = row.querySelector('.status-badge');
                        statusCell.textContent = 'مصفى';
                        statusCell.className = 'status-badge status-delivering';
                    }
                }
            });
        }
        
        function getDriverData(driverName) {
            // Mock data - in real app, fetch from server
            const drivers = {
                'أحمد محمد': { total: 3500, paid: 2800, remaining: 700 },
                'سعد عبدالله': { total: 2800, paid: 2100, remaining: 700 },
                'محمد سالم': { total: 4200, paid: 3000, remaining: 1200 },
                'عبدالرحمن علي': { total: 3100, paid: 2500, remaining: 600 }
            };
            return drivers[driverName] || { total: 0, paid: 0, remaining: 0 };
        }
        
        function viewDriverAccount(driverName) {
            alert(`عرض كشف حساب ${driverName}`);
        }
        
        function printSettlement(driverName) {
            alert(`طباعة تصفية حساب ${driverName}`);
        }
        
        function refreshSettlements() {
            showNotification('تم تحديث بيانات التصفيات', 'info');
        }
        
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span>${message}</span>
                </div>
            `;
            
            const colors = {
                'success': 'linear-gradient(135deg, #10b981, #059669)',
                'info': 'linear-gradient(135deg, #3b82f6, #2563eb)',
                'warning': 'linear-gradient(135deg, #f59e0b, #d97706)'
            };
            
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type] || colors.info};
                color: white;
                padding: 15px 20px;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                z-index: 1001;
                animation: slideInRight 0.3s ease-out;
                max-width: 400px;
                font-weight: 500;
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
