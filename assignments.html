<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إسناد الطلبات - شركة التوصيل السريع</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="sidebar.css">
    <link rel="stylesheet" href="orders.css">
</head>
<body>
    <main class="main-content">
        <section class="action-bar">
            <div class="action-bar-content">
                <div class="page-title">
                    <h2>إسناد الطلبات</h2>
                    <p>توزيع الطلبات على المندوبين</p>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                            <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
                        </svg>
                        إسناد تلقائي
                    </button>
                    <button class="btn btn-secondary">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4"/>
                            <polyline points="9,11 12,14 15,11"/>
                            <line x1="12" y1="2" x2="12" y2="14"/>
                        </svg>
                        إسناد متعدد
                    </button>
                </div>
            </div>
        </section>

        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card pending">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <line x1="12" y1="8" x2="12" y2="12"/>
                            <line x1="12" y1="16" x2="12.01" y2="16"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">12</h3>
                        <p class="stat-label">طلبات في الانتظار</p>
                    </div>
                </div>
                
                <div class="stat-card delivering">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">28</h3>
                        <p class="stat-label">طلبات مسندة اليوم</p>
                    </div>
                </div>
                
                <div class="stat-card delayed">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">6</h3>
                        <p class="stat-label">مندوبين متاحين</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="orders-section">
            <div class="orders-container">
                <div class="table-header">
                    <div class="table-info">
                        <h3>الطلبات في الانتظار</h3>
                        <span class="orders-count">12 طلب</span>
                    </div>
                </div>

                <div class="table-wrapper">
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>رقم الوصل</th>
                                <th>المرسل إليه</th>
                                <th>المدينة</th>
                                <th>الأولوية</th>
                                <th>وقت الإنشاء</th>
                                <th>المندوب المقترح</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="order-id">DL009</span></td>
                                <td>أحمد محمد</td>
                                <td>الرياض</td>
                                <td><span class="priority-badge priority-urgent">مستعجل</span></td>
                                <td>10:30 ص</td>
                                <td>أحمد سالم</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn success">إسناد</button>
                                        <button class="action-btn">تفاصيل</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="order-id">DL010</span></td>
                                <td>فاطمة علي</td>
                                <td>جدة</td>
                                <td><span class="priority-badge priority-high">عالي</span></td>
                                <td>11:15 ص</td>
                                <td>سعد محمد</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn success">إسناد</button>
                                        <button class="action-btn">تفاصيل</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </main>

    <script src="sidebar.js"></script>
</body>
</html>
