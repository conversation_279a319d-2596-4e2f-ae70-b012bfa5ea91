{"version": 3, "sources": ["../../src/build/handle-externals.ts"], "names": ["WEBPACK_LAYERS", "defaultOverrides", "BARREL_OPTIMIZATION_PREFIX", "path", "NODE_BASE_ESM_RESOLVE_OPTIONS", "NODE_BASE_RESOLVE_OPTIONS", "NODE_ESM_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "isWebpackAppLayer", "isWebpackServerLayer", "reactPackagesRegex", "pathSeparators", "optionalEsmPart", "externalFileEnd", "nextDist", "externalPattern", "RegExp", "nodeModulesRegex", "isResourceInPackages", "resource", "packageNames", "packageDirMapping", "some", "p", "has", "startsWith", "get", "sep", "includes", "join", "replace", "resolveExternal", "dir", "esmExternalsConfig", "context", "request", "isEsmRequested", "optOutBundlingPackages", "getResolve", "isLocalCallback", "baseResolveCheck", "esmResolveOptions", "nodeResolveOptions", "baseEsmResolveOptions", "baseResolveOptions", "esmExternals", "looseEsmExternals", "res", "isEsm", "preferEsmOptions", "optOut", "preferEsm", "resolve", "err", "localRes", "baseRes", "baseIsEsm", "baseResolve", "makeExternalHandler", "config", "optOutBundlingPackageRegex", "resolvedExternalPackageDirs", "experimental", "handleExternals", "dependencyType", "layer", "isLocal", "posix", "isAbsolute", "process", "platform", "win32", "isApp<PERSON><PERSON>er", "test", "notExternalModules", "resolveNextExternal", "isExternal", "serverSideRendering", "isRelative", "fullRequest", "resolveResult", "undefined", "Error", "externalType", "transpilePackages", "Map", "pkg", "pkgRes", "set", "dirname", "shouldBeBundled", "bundlePagesExternals"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mBAAkB;AAEjD,SAASC,gBAAgB,QAAQ,yBAAwB;AACzD,SAASC,0BAA0B,QAAQ,0BAAyB;AACpE,OAAOC,UAAU,gCAA+B;AAChD,SACEC,6BAA6B,EAC7BC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,QACf,mBAAkB;AACzB,SAASC,iBAAiB,EAAEC,oBAAoB,QAAQ,WAAU;AAElE,MAAMC,qBAAqB;AAE3B,MAAMC,iBAAiB;AACvB,MAAMC,kBAAkB,CAAC,EAAE,EAAED,eAAe,KAAK,EAAEA,eAAe,CAAC,CAAC;AACpE,MAAME,kBAAkB;AACxB,MAAMC,WAAW,CAAC,IAAI,EAAEH,eAAe,IAAI,CAAC;AAE5C,MAAMI,kBAAkB,IAAIC,OAC1B,CAAC,EAAEF,SAAS,EAAEF,gBAAgB,EAAE,EAAEC,gBAAgB,CAAC;AAGrD,MAAMI,mBAAmB;AAEzB,OAAO,SAASC,qBACdC,QAAgB,EAChBC,YAAuB,EACvBC,iBAAuC;IAEvC,IAAI,CAACD,cAAc,OAAO;IAC1B,OAAOA,aAAaE,IAAI,CAAC,CAACC,IACxBF,qBAAqBA,kBAAkBG,GAAG,CAACD,KACvCJ,SAASM,UAAU,CAACJ,kBAAkBK,GAAG,CAACH,KAAMpB,KAAKwB,GAAG,IACxDR,SAASS,QAAQ,CACfzB,KAAKwB,GAAG,GACNxB,KAAK0B,IAAI,CAAC,gBAAgBN,EAAEO,OAAO,CAAC,OAAO3B,KAAKwB,GAAG,KACnDxB,KAAKwB,GAAG;AAGpB;AAEA,OAAO,eAAeI,gBACpBC,GAAW,EACXC,kBAAsE,EACtEC,OAAe,EACfC,OAAe,EACfC,cAAuB,EACvBC,sBAAgC,EAChCC,UAKsC,EACtCC,eAAsC,EACtCC,mBAAmB,IAAI,EACvBC,oBAAyBnC,wBAAwB,EACjDoC,qBAA0BnC,oBAAoB,EAC9CoC,wBAA6BvC,6BAA6B,EAC1DwC,qBAA0BvC,yBAAyB;IAEnD,MAAMwC,eAAe,CAAC,CAACZ;IACvB,MAAMa,oBAAoBb,uBAAuB;IAEjD,IAAIc,MAAqB;IACzB,IAAIC,QAAiB;IAErB,MAAMC,mBACJJ,gBACAT,kBACA,mEAAmE;IACnE,2EAA2E;IAC3E,2EAA2E;IAC3E,CAACC,uBAAuBf,IAAI,CAAC,CAAC4B,SAAWf,QAAQV,UAAU,CAACyB,WACxD;QAAC;QAAM;KAAM,GACb;QAAC;KAAM;IAEb,KAAK,MAAMC,aAAaF,iBAAkB;QACxC,MAAMG,UAAUd,WACda,YAAYV,oBAAoBC;QAGlC,6DAA6D;QAC7D,4DAA4D;QAC5D,SAAS;QACT,IAAI;YACD,CAACK,KAAKC,MAAM,GAAG,MAAMI,QAAQlB,SAASC;QACzC,EAAE,OAAOkB,KAAK;YACZN,MAAM;QACR;QAEA,IAAI,CAACA,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACX,kBAAkBY,SAAS,CAACF,mBAAmB;YAClD;QACF;QAEA,IAAIP,iBAAiB;YACnB,OAAO;gBAAEe,UAAUf,gBAAgBQ;YAAK;QAC1C;QAEA,mEAAmE;QACnE,mEAAmE;QACnE,kEAAkE;QAClE,gEAAgE;QAChE,IAAIP,kBAAkB;YACpB,IAAIe;YACJ,IAAIC;YACJ,IAAI;gBACF,MAAMC,cAAcnB,WAClBU,QAAQL,wBAAwBC;gBAEjC,CAACW,SAASC,UAAU,GAAG,MAAMC,YAAYzB,KAAKG;YACjD,EAAE,OAAOkB,KAAK;gBACZE,UAAU;gBACVC,YAAY;YACd;YAEA,8DAA8D;YAC9D,iEAAiE;YACjE,yBAAyB;YACzB,2EAA2E;YAC3E,wDAAwD;YACxD,IAAID,YAAYR,OAAOC,UAAUQ,WAAW;gBAC1CT,MAAM;gBACN;YACF;QACF;QACA;IACF;IACA,OAAO;QAAEA;QAAKC;IAAM;AACtB;AAEA,OAAO,SAASU,oBAAoB,EAClCC,MAAM,EACNtB,sBAAsB,EACtBuB,0BAA0B,EAC1B5B,GAAG,EAMJ;QAE2B2B;IAD1B,IAAIE;IACJ,MAAMf,oBAAoBa,EAAAA,uBAAAA,OAAOG,YAAY,qBAAnBH,qBAAqBd,YAAY,MAAK;IAEhE,OAAO,eAAekB,gBACpB7B,OAAe,EACfC,OAAe,EACf6B,cAAsB,EACtBC,KAA8B,EAC9B3B,UAKsC;QAEtC,iEAAiE;QACjE,kBAAkB;QAClB,MAAM4B,UACJ/B,QAAQV,UAAU,CAAC,QACnB,yDAAyD;QACzD,uBAAuB;QACvBtB,KAAKgE,KAAK,CAACC,UAAU,CAACjC,YACtB,8DAA8D;QAC9D,kBAAkB;QACjBkC,QAAQC,QAAQ,KAAK,WAAWnE,KAAKoE,KAAK,CAACH,UAAU,CAACjC;QAEzD,wDAAwD;QACxD,sBAAsB;QACtB,IAAIA,YAAY,QAAQ;YACtB,OAAO,CAAC,0CAA0C,CAAC;QACrD;QAEA,MAAMqC,aAAahE,kBAAkByD;QAErC,+DAA+D;QAC/D,wDAAwD;QACxD,kEAAkE;QAClE,mEAAmE;QACnE,IAAI,CAACC,SAAS;YACZ,IAAI,aAAaO,IAAI,CAACtC,UAAU;gBAC9B,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IAAIzB,mBAAmB+D,IAAI,CAACtC,YAAY,CAACqC,YAAY;gBACnD,OAAO,CAAC,SAAS,EAAErC,QAAQ,CAAC;YAC9B;YAEA,MAAMuC,qBACJ;YACF,IAAIA,mBAAmBD,IAAI,CAACtC,UAAU;gBACpC;YACF;QACF;QAEA,kDAAkD;QAClD,sDAAsD;QACtD,IAAIA,QAAQP,QAAQ,CAAC,iBAAiB;YACpC;QACF;QAEA,uEAAuE;QACvE,2EAA2E;QAC3E,IAAIO,QAAQV,UAAU,CAACvB,6BAA6B;YAClD;QACF;QAEA,gEAAgE;QAChE,yBAAyB;QACzB,kDAAkD;QAClD,MAAMkC,iBAAiB4B,mBAAmB;QAE1C;;;;;;KAMC,GACD,MAAMW,sBAAsB,CAACrB;YAC3B,MAAMsB,aAAa7D,gBAAgB0D,IAAI,CAACnB;YAExC,sFAAsF;YACtF,sGAAsG;YACtG,IAAIsB,YAAY;gBACd,oGAAoG;gBACpG,oCAAoC;gBACpC,OAAO,CAAC,SAAS,EAAEtB,SAASxB,OAAO,CAAC,oBAAoB,aAAa,CAAC;YACxE;QACF;QAEA,4DAA4D;QAC5D,yFAAyF;QACzF,IACErB,qBAAqBwD,UACrB9B,YAAY,+CACZ;YACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;QAC5B;QAEA,uDAAuD;QACvD,+CAA+C;QAC/C,IAAIA,QAAQV,UAAU,CAAC,eAAe;YACpC,2CAA2C;YAC3C,sCAAsC;YACtC,IAAI,qDAAqDgD,IAAI,CAACtC,UAAU;gBACtE;YACF;YAEA,IAAI,8CAA8CsC,IAAI,CAACtC,UAAU;gBAC/D,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,8DAA8DsC,IAAI,CAChEtC,YAEF,4CAA4CsC,IAAI,CAACtC,UACjD;gBACA,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,sEAAsEsC,IAAI,CACxEtC,YAEF,2CAA2CsC,IAAI,CAACtC,UAChD;gBACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;YAC5B;YAEA,OAAOwC,oBAAoBxC;QAC7B;QAEA,gFAAgF;QAChF,qEAAqE;QACrE,oDAAoD;QACpD,IAAI8B,UAAUjE,eAAe6E,mBAAmB,EAAE;YAChD,MAAMC,aAAa3C,QAAQV,UAAU,CAAC;YACtC,MAAMsD,cAAcD,aAChB3E,KAAK0B,IAAI,CAACK,SAASC,SAASL,OAAO,CAAC,OAAO,OAC3CK;YACJ,OAAOwC,oBAAoBI;QAC7B;QAEA,6FAA6F;QAC7F,MAAMC,gBAAgB,MAAMjD,gBAC1BC,KACA2B,OAAOG,YAAY,CAACjB,YAAY,EAChCX,SACAC,SACAC,gBACAC,wBACAC,YACA4B,UAAUS,sBAAsBM;QAGlC,IAAI,cAAcD,eAAe;YAC/B,OAAOA,cAAc1B,QAAQ;QAC/B;QAEA,wDAAwD;QACxD,mEAAmE;QACnE,IAAInB,YAAY,oBAAoB;YAClC6C,cAAcjC,GAAG,GAAG9C,gBAAgB,CAAC,mBAAmB;QAC1D;QAEA,MAAM,EAAE8C,GAAG,EAAEC,KAAK,EAAE,GAAGgC;QAEvB,oDAAoD;QACpD,0DAA0D;QAC1D,IAAI,CAACjC,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACX,kBAAkBY,SAAS,CAACF,mBAAmB;YAClD,MAAM,IAAIoC,MACR,CAAC,cAAc,EAAE/C,QAAQ,2HAA2H,CAAC;QAEzJ;QAEA,MAAMgD,eAAenC,QAAQ,WAAW;QAExC,sCAAsC;QACtC,IACE,qEAAqE;QACrE,2CAA2CyB,IAAI,CAAC1B,MAChD;YACA;QACF;QAEA,wFAAwF;QACxF,IACE,2BAA2B0B,IAAI,CAAC1B,QAChC,8BAA8B0B,IAAI,CAAC1B,MACnC;YACA;QACF;QAEA,4EAA4E;QAC5E,yEAAyE;QACzE,IAAIY,OAAOyB,iBAAiB,IAAI,CAACvB,6BAA6B;YAC5DA,8BAA8B,IAAIwB;YAClC,8DAA8D;YAC9D,KAAK,MAAMC,OAAO3B,OAAOyB,iBAAiB,CAAE;gBAC1C,MAAMG,SAAS,MAAMxD,gBACnBC,KACA2B,OAAOG,YAAY,CAACjB,YAAY,EAChCX,SACAoD,MAAM,iBACNlD,gBACAC,wBACAC,YACA4B,UAAUS,sBAAsBM;gBAElC,IAAIM,OAAOxC,GAAG,EAAE;oBACdc,4BAA4B2B,GAAG,CAACF,KAAKnF,KAAKsF,OAAO,CAACF,OAAOxC,GAAG;gBAC9D;YACF;QACF;QAEA,MAAM2C,kBACJxE,qBACE6B,KACAY,OAAOyB,iBAAiB,EACxBvB,gCAEDb,SAASwB,cACT,CAACA,cAAcb,OAAOG,YAAY,CAAC6B,oBAAoB;QAE1D,IAAI1E,iBAAiBwD,IAAI,CAAC1B,MAAM;YAC9B,IAAItC,qBAAqBwD,QAAQ;gBAC/B,IAAI,CAACL,2BAA2Ba,IAAI,CAAC1B,MAAM;oBACzC,QAAO,0BAA0B;gBACnC;gBACA,OAAO,CAAC,EAAEoC,aAAa,CAAC,EAAEhD,QAAQ,CAAC,CAAC,2BAA2B;;YACjE;YAEA,IAAI,CAACuD,mBAAmB9B,2BAA2Ba,IAAI,CAAC1B,MAAM;gBAC5D,OAAO,CAAC,EAAEoC,aAAa,CAAC,EAAEhD,QAAQ,CAAC,CAAC,0CAA0C;;YAChF;QACF;IAEA,2CAA2C;IAC7C;AACF"}