{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-cache-with-data-property.ts"], "names": ["fillCacheWithDataProperty", "newCache", "existingCache", "flightSegmentPath", "fetchResponse", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "existingChildCacheNode", "childCacheNode", "data", "status", "CacheStates", "DATA_FETCH", "subTreeData", "slice"], "mappings": ";;;;+BASgBA;;;eAAAA;;;+CAPY;sCAES;AAK9B,SAASA,0BACdC,QAAmB,EACnBC,aAAwB,EACxBC,iBAAoC,EACpCC,aAAuD;IAEvD,MAAMC,cAAcF,kBAAkBG,MAAM,IAAI;IAEhD,MAAM,CAACC,kBAAkBC,QAAQ,GAAGL;IACpC,MAAMM,WAAWC,IAAAA,0CAAoB,EAACF;IAEtC,MAAMG,0BACJT,cAAcU,cAAc,CAACC,GAAG,CAACN;IAEnC,IAAIO,kBAAkBb,SAASW,cAAc,CAACC,GAAG,CAACN;IAElD,IAAI,CAACO,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BV,SAASW,cAAc,CAACI,GAAG,CAACT,kBAAkBO;IAChD;IAEA,MAAMG,yBAAyBN,2CAAAA,wBAAyBE,GAAG,CAACJ;IAC5D,IAAIS,iBAAiBJ,gBAAgBD,GAAG,CAACJ;IAEzC,yFAAyF;IACzF,IAAIJ,aAAa;QACf,IACE,CAACa,kBACD,CAACA,eAAeC,IAAI,IACpBD,mBAAmBD,wBACnB;YACAH,gBAAgBE,GAAG,CAACP,UAAU;gBAC5BW,QAAQC,0CAAW,CAACC,UAAU;gBAC9BH,MAAMf;gBACNmB,aAAa;gBACbX,gBAAgB,IAAIG;YACtB;QACF;QACA;IACF;IAEA,IAAI,CAACG,kBAAkB,CAACD,wBAAwB;QAC9C,+EAA+E;QAC/E,IAAI,CAACC,gBAAgB;YACnBJ,gBAAgBE,GAAG,CAACP,UAAU;gBAC5BW,QAAQC,0CAAW,CAACC,UAAU;gBAC9BH,MAAMf;gBACNmB,aAAa;gBACbX,gBAAgB,IAAIG;YACtB;QACF;QACA;IACF;IAEA,IAAIG,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfE,QAAQF,eAAeE,MAAM;YAC7BD,MAAMD,eAAeC,IAAI;YACzBI,aAAaL,eAAeK,WAAW;YACvCX,gBAAgB,IAAIG,IAAIG,eAAeN,cAAc;QACvD;QACAE,gBAAgBE,GAAG,CAACP,UAAUS;IAChC;IAEA,OAAOlB,0BACLkB,gBACAD,wBACAd,kBAAkBqB,KAAK,CAAC,IACxBpB;AAEJ"}