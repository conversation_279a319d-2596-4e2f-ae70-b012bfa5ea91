{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/navigate-reducer.ts"], "names": ["handleExternalUrl", "navigateReducer", "state", "mutable", "url", "pendingPush", "mpaNavigation", "canonicalUrl", "scrollableSegments", "undefined", "handleMutable", "generateSegmentsFromPatch", "flightRouterPatch", "segments", "segment", "parallelRoutes", "Object", "keys", "length", "parallelRouteKey", "parallelRoute", "entries", "childSegment", "push", "addRefetchToLeafSegments", "newCache", "currentCache", "flightSegmentPath", "treePatch", "data", "appliedPatch", "status", "CacheStates", "READY", "subTreeData", "Map", "segmentPathsToFill", "map", "segmentPaths", "fillCacheWithDataProperty", "action", "isExternalUrl", "navigateType", "shouldScroll", "hash", "href", "createHrefFromUrl", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "prefetchCache", "preserveCustomHistoryState", "toString", "prefetchValues", "get", "fetchServerResponse", "tree", "nextUrl", "buildId", "process", "env", "NODE_ENV", "PrefetchKind", "AUTO", "newPrefetchValue", "kind", "TEMPORARY", "prefetchTime", "Date", "now", "treeAtTimeOfPrefetch", "lastUsedTime", "set", "prefetchEntryCacheStatus", "getPrefetchEntryCacheStatus", "prefetchQueue", "bump", "then", "flightData", "canonicalUrlOverride", "postponed", "currentTree", "cache", "flightDataPath", "slice", "flightSegmentPathWithLeadingEmpty", "newTree", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "createEmptyCacheNode", "applied", "applyFlightData", "PrefetchCacheEntryStatus", "reusable", "stale", "hardNavigate", "shouldHardNavigate", "invalidateCacheBelowFlightSegmentPath", "subSegment", "scrollableSegmentPath", "patchedTree", "hashFragment"], "mappings": ";;;;;;;;;;;;;;;IA+BgBA,iBAAiB;eAAjBA;;IAiEAC,eAAe;eAAfA;;;+CAhGY;qCAMQ;mCAEF;uDACoB;2CACZ;6CACE;oCACT;6CACS;oCAOf;+BACC;iCACE;6CAIzB;oCAC4B;iCACL;2BACO;AAE9B,SAASD,kBACdE,KAA2B,EAC3BC,OAAgB,EAChBC,GAAW,EACXC,WAAoB;IAEpBF,QAAQG,aAAa,GAAG;IACxBH,QAAQI,YAAY,GAAGH;IACvBD,QAAQE,WAAW,GAAGA;IACtBF,QAAQK,kBAAkB,GAAGC;IAE7B,OAAOC,IAAAA,4BAAa,EAACR,OAAOC;AAC9B;AAEA,SAASQ,0BACPC,iBAAoC;IAEpC,MAAMC,WAAgC,EAAE;IACxC,MAAM,CAACC,SAASC,eAAe,GAAGH;IAElC,IAAII,OAAOC,IAAI,CAACF,gBAAgBG,MAAM,KAAK,GAAG;QAC5C,OAAO;YAAC;gBAACJ;aAAQ;SAAC;IACpB;IAEA,KAAK,MAAM,CAACK,kBAAkBC,cAAc,IAAIJ,OAAOK,OAAO,CAC5DN,gBACC;QACD,KAAK,MAAMO,gBAAgBX,0BAA0BS,eAAgB;YACnE,mEAAmE;YACnE,IAAIN,YAAY,IAAI;gBAClBD,SAASU,IAAI,CAAC;oBAACJ;uBAAqBG;iBAAa;YACnD,OAAO;gBACLT,SAASU,IAAI,CAAC;oBAACT;oBAASK;uBAAqBG;iBAAa;YAC5D;QACF;IACF;IAEA,OAAOT;AACT;AAEA,SAASW,yBACPC,QAAmB,EACnBC,YAAuB,EACvBC,iBAAoC,EACpCC,SAA4B,EAC5BC,IAA8C;IAE9C,IAAIC,eAAe;IAEnBL,SAASM,MAAM,GAAGC,0CAAW,CAACC,KAAK;IACnCR,SAASS,WAAW,GAAGR,aAAaQ,WAAW;IAC/CT,SAASV,cAAc,GAAG,IAAIoB,IAAIT,aAAaX,cAAc;IAE7D,MAAMqB,qBAAqBzB,0BAA0BiB,WAAWS,GAAG,CACjE,CAACvB,UAAY;eAAIa;eAAsBb;SAAQ;IAGjD,KAAK,MAAMwB,gBAAgBF,mBAAoB;QAC7CG,IAAAA,oDAAyB,EAACd,UAAUC,cAAcY,cAAcT;QAEhEC,eAAe;IACjB;IAEA,OAAOA;AACT;AACO,SAAS7B,gBACdC,KAA2B,EAC3BsC,MAAsB;IAEtB,MAAM,EAAEpC,GAAG,EAAEqC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAE,GAAGH;IAC3D,MAAMrC,UAAmB,CAAC;IAC1B,MAAM,EAAEyC,IAAI,EAAE,GAAGxC;IACjB,MAAMyC,OAAOC,IAAAA,oCAAiB,EAAC1C;IAC/B,MAAMC,cAAcqC,iBAAiB;IACrC,wFAAwF;IACxFK,IAAAA,sCAAkB,EAAC7C,MAAM8C,aAAa;IAEtC7C,QAAQ8C,0BAA0B,GAAG;IAErC,IAAIR,eAAe;QACjB,OAAOzC,kBAAkBE,OAAOC,SAASC,IAAI8C,QAAQ,IAAI7C;IAC3D;IAEA,IAAI8C,iBAAiBjD,MAAM8C,aAAa,CAACI,GAAG,CAACN,IAAAA,oCAAiB,EAAC1C,KAAK;IAEpE,2DAA2D;IAC3D,IAAI,CAAC+C,gBAAgB;QACnB,MAAMtB,OAAOwB,IAAAA,wCAAmB,EAC9BjD,KACAF,MAAMoD,IAAI,EACVpD,MAAMqD,OAAO,EACbrD,MAAMsD,OAAO,EACb,8EAA8E;QAC9E,0DAA0D;QAC1DC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgBC,gCAAY,CAACC,IAAI,GAAGpD;QAG/D,MAAMqD,mBAAmB;YACvBjC;YACA,iEAAiE;YACjEkC,MACEN,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrBC,gCAAY,CAACC,IAAI,GACjBD,gCAAY,CAACI,SAAS;YAC5BC,cAAcC,KAAKC,GAAG;YACtBC,sBAAsBlE,MAAMoD,IAAI;YAChCe,cAAc;QAChB;QAEAnE,MAAM8C,aAAa,CAACsB,GAAG,CAACxB,IAAAA,oCAAiB,EAAC1C,KAAK,QAAQ0D;QACvDX,iBAAiBW;IACnB;IAEA,MAAMS,2BAA2BC,IAAAA,wDAA2B,EAACrB;IAE7D,0DAA0D;IAC1D,MAAM,EAAEiB,oBAAoB,EAAEvC,IAAI,EAAE,GAAGsB;IAEvCsB,8BAAa,CAACC,IAAI,CAAC7C;IAEnB,OAAOA,KAAM8C,IAAI,CACf;YAAC,CAACC,YAAYC,sBAAsBC,UAAU;QAC5C,iCAAiC;QACjC,IAAI3B,kBAAkB,CAACA,eAAekB,YAAY,EAAE;YAClD,gGAAgG;YAChGlB,eAAekB,YAAY,GAAGH,KAAKC,GAAG;QACxC;QAEA,4DAA4D;QAC5D,IAAI,OAAOS,eAAe,UAAU;YAClC,OAAO5E,kBAAkBE,OAAOC,SAASyE,YAAYvE;QACvD;QAEA,IAAI0E,cAAc7E,MAAMoD,IAAI;QAC5B,IAAI5B,eAAexB,MAAM8E,KAAK;QAC9B,IAAIxE,qBAA0C,EAAE;QAChD,KAAK,MAAMyE,kBAAkBL,WAAY;YACvC,MAAMjD,oBAAoBsD,eAAeC,KAAK,CAC5C,GACA,CAAC;YAEH,0DAA0D;YAC1D,MAAMtD,YAAYqD,eAAeC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;YAE7C,sBAAsB;YACtB,MAAMC,oCAAoC;gBAAC;mBAAOxD;aAAkB;YAEpE,wEAAwE;YACxE,IAAIyD,UAAUC,IAAAA,wDAA2B,EACvC,sBAAsB;YACtBF,mCACAJ,aACAnD;YAGF,kGAAkG;YAClG,6IAA6I;YAC7I,IAAIwD,YAAY,MAAM;gBACpBA,UAAUC,IAAAA,wDAA2B,EACnC,sBAAsB;gBACtBF,mCACAf,sBACAxC;YAEJ;YAEA,IAAIwD,YAAY,MAAM;gBACpB,IAAIE,IAAAA,wDAA2B,EAACP,aAAaK,UAAU;oBACrD,OAAOpF,kBAAkBE,OAAOC,SAAS0C,MAAMxC;gBACjD;gBAEA,MAAM2E,QAAmBO,IAAAA,+BAAoB;gBAC7C,IAAIC,UAAUC,IAAAA,gCAAe,EAC3B/D,cACAsD,OACAC,gBACA9B,CAAAA,kCAAAA,eAAgBY,IAAI,MAAK,UACvBQ,6BAA6BmB,qDAAwB,CAACC,QAAQ;gBAGlE,IACE,AAAC,CAACH,WACAjB,6BAA6BmB,qDAAwB,CAACE,KAAK,IAC7D,qEAAqE;gBACrE,6DAA6D;gBAC7Dd,WACA;oBACAU,UAAUhE,yBACRwD,OACAtD,cACAC,mBACAC,WACA,wCAAwC;oBACxC,IACEyB,IAAAA,wCAAmB,EACjBjD,KACA2E,aACA7E,MAAMqD,OAAO,EACbrD,MAAMsD,OAAO;gBAGrB;gBAEA,MAAMqC,eAAeC,IAAAA,sCAAkB,EACrC,sBAAsB;gBACtBX,mCACAJ;gBAGF,IAAIc,cAAc;oBAChBb,MAAMjD,MAAM,GAAGC,0CAAW,CAACC,KAAK;oBAChC,mDAAmD;oBACnD+C,MAAM9C,WAAW,GAAGR,aAAaQ,WAAW;oBAE5C6D,IAAAA,4EAAqC,EACnCf,OACAtD,cACAC;oBAEF,8EAA8E;oBAC9ExB,QAAQ6E,KAAK,GAAGA;gBAClB,OAAO,IAAIQ,SAAS;oBAClBrF,QAAQ6E,KAAK,GAAGA;gBAClB;gBAEAtD,eAAesD;gBACfD,cAAcK;gBAEd,KAAK,MAAMY,cAAcrF,0BAA0BiB,WAAY;oBAC7D,MAAMqE,wBAAwB;2BAAItE;2BAAsBqE;qBAAW;oBACnE,kFAAkF;oBAClF,IACEC,qBAAqB,CAACA,sBAAsB/E,MAAM,GAAG,EAAE,KACvD,eACA;wBACAV,mBAAmBe,IAAI,CAAC0E;oBAC1B;gBACF;YACF;QACF;QAEA9F,QAAQ+F,WAAW,GAAGnB;QACtB5E,QAAQI,YAAY,GAAGsE,uBACnB/B,IAAAA,oCAAiB,EAAC+B,wBAClBhC;QACJ1C,QAAQE,WAAW,GAAGA;QACtBF,QAAQK,kBAAkB,GAAGA;QAC7BL,QAAQgG,YAAY,GAAGvD;QACvBzC,QAAQwC,YAAY,GAAGA;QAEvB,OAAOjC,IAAAA,4BAAa,EAACR,OAAOC;IAC9B,GACA,IAAMD;AAEV"}