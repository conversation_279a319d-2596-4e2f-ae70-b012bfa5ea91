{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Terminal/EditorLink.tsx"], "names": ["EditorLink", "file", "isSourceFile", "location", "open", "useOpenInEditor", "lineNumber", "line", "column", "div", "data-with-open-in-editor-link", "data-with-open-in-editor-link-source-file", "undefined", "data-with-open-in-editor-link-import-trace", "tabIndex", "role", "onClick", "title", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "x1", "y1", "x2", "y2"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;;gEAXE;iCACc;AAUzB,SAASA,WAAW,KAAiD;IAAjD,IAAA,EAAEC,IAAI,EAAEC,YAAY,EAAEC,QAAQ,EAAmB,GAAjD;QAGXA,gBACJA;IAHV,MAAMC,OAAOC,IAAAA,gCAAe,EAAC;QAC3BJ;QACAK,YAAYH,CAAAA,iBAAAA,4BAAAA,SAAUI,IAAI,YAAdJ,iBAAkB;QAC9BK,QAAQL,CAAAA,mBAAAA,4BAAAA,SAAUK,MAAM,YAAhBL,mBAAoB;IAC9B;IAEA,qBACE,6BAACM;QACCC,iCAAAA;QACAC,6CACET,eAAe,OAAOU;QAExBC,8CACEX,eAAeU,YAAY;QAE7BE,UAAU;QACVC,MAAM;QACNC,SAASZ;QACTa,OAAO;OAENhB,MACAE,WAAW,AAAC,MAAGA,SAASI,IAAI,GAAC,MAAGJ,SAASK,MAAM,GAAK,oBACrD,6BAACU;QACCC,OAAM;QACNC,SAAQ;QACRC,MAAK;QACLC,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;qBAEf,6BAACC;QAAKC,GAAE;sBACR,6BAACC;QAASC,QAAO;sBACjB,6BAACtB;QAAKuB,IAAG;QAAKC,IAAG;QAAKC,IAAG;QAAKC,IAAG;;AAIzC"}