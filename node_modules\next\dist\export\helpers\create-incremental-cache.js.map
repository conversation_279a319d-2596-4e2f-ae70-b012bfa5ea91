{"version": 3, "sources": ["../../../src/export/helpers/create-incremental-cache.ts"], "names": ["createIncrementalCache", "incremental<PERSON>ache<PERSON>andlerPath", "isrMemoryCacheSize", "fetchCacheKeyPrefix", "distDir", "dir", "enabledDirectories", "experimental", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "path", "isAbsolute", "join", "default", "incrementalCache", "IncrementalCache", "dev", "requestHeaders", "fetchCache", "maxMemoryCacheSize", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "fs", "nodeFs", "pagesDir", "pages", "appDir", "app", "serverDistDir", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minimalMode", "hasNextSupport", "globalThis", "__incrementalCache"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;6DALC;kCACgB;wBACF;+BACR;;;;;;AAEhB,SAASA,uBAAuB,EACrCC,2BAA2B,EAC3BC,kBAAkB,EAClBC,mBAAmB,EACnBC,OAAO,EACPC,GAAG,EACHC,kBAAkB,EAClBC,YAAY,EACZC,WAAW,EAUZ;IACC,kCAAkC;IAClC,IAAIC;IACJ,IAAIR,6BAA6B;QAC/BQ,eAAeC,QAAQC,aAAI,CAACC,UAAU,CAACX,+BACnCA,8BACAU,aAAI,CAACE,IAAI,CAACR,KAAKJ;QACnBQ,eAAeA,aAAaK,OAAO,IAAIL;IACzC;IAEA,MAAMM,mBAAmB,IAAIC,kCAAgB,CAAC;QAC5CC,KAAK;QACLC,gBAAgB,CAAC;QACjBV;QACAW,YAAY;QACZC,oBAAoBlB;QACpBC;QACAkB,sBAAsB,IAAO,CAAA;gBAC3BC,SAAS;gBACTC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,SAAS;oBACPC,0BAA0B;oBAC1BC,eAAe;oBACfC,uBAAuB;gBACzB;gBACAC,gBAAgB,EAAE;YACpB,CAAA;QACAC,IAAIC,qBAAM;QACVC,UAAU1B,mBAAmB2B,KAAK;QAClCC,QAAQ5B,mBAAmB6B,GAAG;QAC9BC,eAAezB,aAAI,CAACE,IAAI,CAACT,SAAS;QAClCiC,iBAAiB5B;QACjB6B,aAAaC,sBAAc;QAC3BhC;IACF;IAEEiC,WAAmBC,kBAAkB,GAAG1B;IAE1C,OAAOA;AACT"}