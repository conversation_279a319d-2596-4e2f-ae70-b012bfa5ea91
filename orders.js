// Extended sample data for orders management
const ordersData = [
    {
        id: 'DL001',
        recipientName: 'أحمد محمد علي',
        senderName: 'متجر الإلكترونيات',
        phone: '0501234567',
        city: 'riyadh',
        cityName: 'الرياض',
        address: 'حي النرجس، شارع الأمير محمد بن عبدالعزيز',
        date: '2024-06-30',
        amount: 328000,
        status: 'delivering',
        priority: 'normal',
        type: 'electronics',
        notes: 'طلب عادي - يرجى التأكد من الهوية',
        deliveryDate: '2024-07-01',
        assignedDriver: 'محمد أحمد'
    },
    {
        id: 'DL002',
        recipientName: 'فاطمة أحمد',
        senderName: 'متجر الأزياء',
        phone: '0507654321',
        city: 'jeddah',
        cityName: 'جدة',
        address: 'حي الروضة، شارع التحلية',
        date: '2024-06-29',
        amount: 236816,
        status: 'delayed',
        priority: 'high',
        type: 'clothing',
        notes: 'عنوان غير واضح - يحتاج تأكيد',
        deliveryDate: '2024-07-02',
        assignedDriver: 'سعد محمد'
    },
    {
        id: 'DL003',
        recipientName: 'محمد سعد',
        senderName: 'مطعم البرجر',
        phone: '0551234567',
        city: 'dammam',
        cityName: 'الدمام',
        address: 'حي الفيصلية، شارع الملك فهد',
        date: '2024-06-28',
        amount: 421024,
        status: 'returned',
        priority: 'urgent',
        type: 'food',
        notes: 'رفض الاستلام - العميل غير متواجد',
        deliveryDate: '2024-06-30',
        assignedDriver: 'خالد عبدالله'
    },
    {
        id: 'DL004',
        recipientName: 'نورا خالد',
        senderName: 'صيدلية النور',
        phone: '**********',
        city: 'mecca',
        cityName: 'مكة',
        address: 'حي العزيزية، شارع إبراهيم الخليل',
        date: '2024-06-30',
        amount: 125008,
        status: 'pending',
        priority: 'high',
        type: 'medical',
        notes: 'في انتظار التأكيد من العميل',
        deliveryDate: '2024-07-01',
        assignedDriver: ''
    },
    {
        id: 'DL005',
        recipientName: 'عبدالله أحمد',
        senderName: 'متجر الكتب',
        phone: '**********',
        city: 'riyadh',
        cityName: 'الرياض',
        address: 'حي العليا، شارع العروبة',
        date: '2024-06-30',
        amount: 590400,
        status: 'delivering',
        priority: 'urgent',
        type: 'books',
        notes: 'طلب مستعجل - تسليم اليوم',
        deliveryDate: '2024-06-30',
        assignedDriver: 'أحمد سالم'
    }
];

// Global variables
let currentOrders = [...ordersData];
let filteredOrders = [...ordersData];
let selectedOrders = new Set();
let currentPage = 1;
let ordersPerPage = 10;
let currentView = 'table';
let editingOrderId = null;

// DOM Elements
const searchInput = document.getElementById('search-input');
const clearSearchBtn = document.getElementById('clear-search');
const statusFilter = document.getElementById('status-filter');
const cityFilter = document.getElementById('city-filter');
const dateFilter = document.getElementById('date-filter');
const amountFilter = document.getElementById('amount-filter');
const ordersTable = document.getElementById('orders-table');
const ordersGrid = document.getElementById('orders-grid');
const ordersTbody = document.getElementById('orders-tbody');
const selectAllCheckbox = document.getElementById('select-all');
const addOrderBtn = document.getElementById('add-order-btn');
const bulkActionsBtn = document.getElementById('bulk-actions-btn');
const exportBtn = document.getElementById('export-btn');
const orderModal = document.getElementById('order-modal');
const bulkModal = document.getElementById('bulk-modal');
const closeModalBtn = document.getElementById('close-modal');
// Logout button is now handled by sidebar
const viewBtns = document.querySelectorAll('.view-btn');

// Initialize the orders management system
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    if (!localStorage.getItem('isLoggedIn')) {
        window.location.href = 'index.html';
        return;
    }
    
    initializeOrdersSystem();
});

function initializeOrdersSystem() {
    // Load user info
    loadUserInfo();
    
    // Add event listeners
    addEventListeners();
    
    // Initial data load
    applyFilters();
    updateOrdersCount();
}

function loadUserInfo() {
    // User info is now handled by sidebar
}

function addEventListeners() {
    // Search and filters
    searchInput.addEventListener('input', handleSearch);
    clearSearchBtn.addEventListener('click', clearSearch);
    statusFilter.addEventListener('change', applyFilters);
    cityFilter.addEventListener('change', applyFilters);
    dateFilter.addEventListener('change', applyFilters);
    amountFilter.addEventListener('change', applyFilters);
    
    // View switching
    viewBtns.forEach(btn => {
        btn.addEventListener('click', () => switchView(btn.dataset.view));
    });
    
    // Select all checkbox
    selectAllCheckbox.addEventListener('change', handleSelectAll);
    
    // Action buttons
    addOrderBtn.addEventListener('click', () => showOrderModal());
    bulkActionsBtn.addEventListener('click', showBulkModal);
    exportBtn.addEventListener('click', exportOrders);
    
    // Modal controls
    closeModalBtn.addEventListener('click', closeModal);
    orderModal.addEventListener('click', (e) => {
        if (e.target === orderModal) closeModal();
    });
    
    // Logout is now handled by sidebar
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
}

function handleSearch() {
    const query = searchInput.value.trim();
    
    if (query) {
        clearSearchBtn.classList.add('visible');
    } else {
        clearSearchBtn.classList.remove('visible');
    }
    
    applyFilters();
}

function clearSearch() {
    searchInput.value = '';
    clearSearchBtn.classList.remove('visible');
    applyFilters();
}

function applyFilters() {
    const searchQuery = searchInput.value.trim().toLowerCase();
    const statusValue = statusFilter.value;
    const cityValue = cityFilter.value;
    const dateValue = dateFilter.value;
    const amountValue = amountFilter.value;
    
    filteredOrders = currentOrders.filter(order => {
        // Search filter
        const matchesSearch = !searchQuery || 
            order.id.toLowerCase().includes(searchQuery) ||
            order.recipientName.toLowerCase().includes(searchQuery) ||
            order.senderName.toLowerCase().includes(searchQuery) ||
            order.phone.includes(searchQuery);
        
        // Status filter
        const matchesStatus = !statusValue || order.status === statusValue;
        
        // City filter
        const matchesCity = !cityValue || order.city === cityValue;
        
        // Date filter
        const matchesDate = !dateValue || checkDateFilter(order.date, dateValue);
        
        // Amount filter
        const matchesAmount = !amountValue || checkAmountFilter(order.amount, amountValue);
        
        return matchesSearch && matchesStatus && matchesCity && matchesDate && matchesAmount;
    });
    
    currentPage = 1;
    selectedOrders.clear();
    updateSelectAllCheckbox();
    renderOrders();
    updatePagination();
    updateOrdersCount();
}

function checkDateFilter(orderDate, filterValue) {
    const today = new Date();
    const orderDateObj = new Date(orderDate);
    
    switch (filterValue) {
        case 'today':
            return orderDateObj.toDateString() === today.toDateString();
        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            return orderDateObj.toDateString() === yesterday.toDateString();
        case 'week':
            const weekAgo = new Date(today);
            weekAgo.setDate(weekAgo.getDate() - 7);
            return orderDateObj >= weekAgo;
        case 'month':
            const monthAgo = new Date(today);
            monthAgo.setMonth(monthAgo.getMonth() - 1);
            return orderDateObj >= monthAgo;
        default:
            return true;
    }
}

function checkAmountFilter(amount, filterValue) {
    switch (filterValue) {
        case '0-100':
            return amount < 100;
        case '100-300':
            return amount >= 100 && amount <= 300;
        case '300-500':
            return amount >= 300 && amount <= 500;
        case '500+':
            return amount > 500;
        default:
            return true;
    }
}

function switchView(view) {
    currentView = view;
    
    // Update view buttons
    viewBtns.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.view === view);
    });
    
    // Update view containers
    const tableView = document.getElementById('table-view');
    const gridView = document.getElementById('grid-view');
    
    if (view === 'table') {
        tableView.classList.add('active');
        gridView.classList.remove('active');
    } else {
        tableView.classList.remove('active');
        gridView.classList.add('active');
    }
    
    renderOrders();
}

function renderOrders() {
    const startIndex = (currentPage - 1) * ordersPerPage;
    const endIndex = startIndex + ordersPerPage;
    const pageOrders = filteredOrders.slice(startIndex, endIndex);
    
    if (currentView === 'table') {
        renderTableView(pageOrders);
    } else {
        renderGridView(pageOrders);
    }
}

function renderTableView(orders) {
    if (orders.length === 0) {
        ordersTbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center">
                    <div class="empty-state">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M16 16s-1.5-2-4-2-4 2-4 2"/>
                            <line x1="9" y1="9" x2="9.01" y2="9"/>
                            <line x1="15" y1="9" x2="15.01" y2="9"/>
                        </svg>
                        <h3>لا توجد طلبات</h3>
                        <p>لم يتم العثور على طلبات تطابق المعايير المحددة</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    ordersTbody.innerHTML = orders.map(order => createOrderRow(order)).join('');
}

function createOrderRow(order) {
    const isSelected = selectedOrders.has(order.id);
    
    return `
        <tr class="${isSelected ? 'selected' : ''}">
            <td>
                <input type="checkbox" class="checkbox order-checkbox"
                       data-order-id="${order.id}" ${isSelected ? 'checked' : ''}>
            </td>
            <td>
                <span class="order-id" onclick="showOrderDetails('${order.id}')">${order.id}</span>
            </td>
            <td>${order.recipientName}</td>
            <td>
                <a href="tel:${order.phone}" style="color: #10b981; text-decoration: none;">
                    ${order.phone}
                </a>
            </td>
            <td>${order.cityName}</td>
            <td>${formatDate(order.date)}</td>
            <td class="amount">${order.amount.toFixed(2)} ر.س</td>
            <td>
                <span class="status-badge status-${order.status}">
                    ${getStatusText(order.status)}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn primary" onclick="editOrder('${order.id}')">تعديل</button>
                    <button class="action-btn danger" onclick="deleteOrder('${order.id}')">حذف</button>
                    <button class="action-btn" onclick="showOrderDetails('${order.id}')">التفاصيل</button>
                </div>
            </td>
        </tr>
    `;
}

function renderGridView(orders) {
    if (orders.length === 0) {
        ordersGrid.innerHTML = `
            <div class="empty-state" style="grid-column: 1 / -1;">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="M16 16s-1.5-2-4-2-4 2-4 2"/>
                    <line x1="9" y1="9" x2="9.01" y2="9"/>
                    <line x1="15" y1="9" x2="15.01" y2="9"/>
                </svg>
                <h3>لا توجد طلبات</h3>
                <p>لم يتم العثور على طلبات تطابق المعايير المحددة</p>
            </div>
        `;
        return;
    }

    ordersGrid.innerHTML = orders.map(order => createOrderCard(order)).join('');
}

function createOrderCard(order) {
    const isSelected = selectedOrders.has(order.id);

    return `
        <div class="order-card ${isSelected ? 'selected' : ''}" onclick="toggleOrderSelection('${order.id}')">
            <div class="order-card-header">
                <span class="order-id-card">${order.id}</span>
                <span class="status-badge status-${order.status}">
                    ${getStatusText(order.status)}
                </span>
            </div>

            <div class="order-card-body">
                <div class="order-info-row">
                    <span class="order-info-label">المرسل إليه:</span>
                    <span class="order-info-value">${order.recipientName}</span>
                </div>
                <div class="order-info-row">
                    <span class="order-info-label">الهاتف:</span>
                    <span class="order-info-value">${order.phone}</span>
                </div>
                <div class="order-info-row">
                    <span class="order-info-label">المدينة:</span>
                    <span class="order-info-value">${order.cityName}</span>
                </div>
                <div class="order-info-row">
                    <span class="order-info-label">التاريخ:</span>
                    <span class="order-info-value">${formatDate(order.date)}</span>
                </div>
                <div class="order-info-row">
                    <span class="order-info-label">المبلغ:</span>
                    <span class="order-info-value amount">${order.amount.toFixed(2)} ر.س</span>
                </div>
            </div>

            <div class="order-card-actions">
                <button class="action-btn primary" onclick="event.stopPropagation(); editOrder('${order.id}')">تعديل</button>
                <button class="action-btn danger" onclick="event.stopPropagation(); deleteOrder('${order.id}')">حذف</button>
                <button class="action-btn" onclick="event.stopPropagation(); showOrderDetails('${order.id}')">التفاصيل</button>
            </div>
        </div>
    `;
}

// Order management functions
function showOrderModal(orderId = null) {
    editingOrderId = orderId;
    const isEditing = orderId !== null;
    const order = isEditing ? currentOrders.find(o => o.id === orderId) : null;

    document.getElementById('modal-title').textContent = isEditing ? 'تعديل الطلب' : 'إضافة طلب جديد';

    const modalBody = document.getElementById('modal-body');
    modalBody.innerHTML = `
        <form id="order-form" class="order-form">
            <div class="form-grid">
                <div class="form-group">
                    <label class="form-label">رقم الوصل</label>
                    <input type="text" id="order-id" class="form-input"
                           value="${isEditing ? order.id : generateOrderId()}"
                           ${isEditing ? 'readonly' : ''} required>
                </div>

                <div class="form-group">
                    <label class="form-label">اسم المرسل إليه</label>
                    <input type="text" id="recipient-name" class="form-input"
                           value="${isEditing ? order.recipientName : ''}" required>
                </div>

                <div class="form-group">
                    <label class="form-label">اسم المرسل</label>
                    <input type="text" id="sender-name" class="form-input"
                           value="${isEditing ? order.senderName : ''}" required>
                </div>

                <div class="form-group">
                    <label class="form-label">رقم الهاتف</label>
                    <input type="tel" id="phone" class="form-input"
                           value="${isEditing ? order.phone : ''}" required>
                </div>

                <div class="form-group">
                    <label class="form-label">المدينة</label>
                    <select id="city" class="form-select" required>
                        <option value="">اختر المدينة</option>
                        <option value="riyadh" ${isEditing && order.city === 'riyadh' ? 'selected' : ''}>الرياض</option>
                        <option value="jeddah" ${isEditing && order.city === 'jeddah' ? 'selected' : ''}>جدة</option>
                        <option value="dammam" ${isEditing && order.city === 'dammam' ? 'selected' : ''}>الدمام</option>
                        <option value="mecca" ${isEditing && order.city === 'mecca' ? 'selected' : ''}>مكة</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">المبلغ (ر.س)</label>
                    <input type="number" id="amount" class="form-input" step="0.01" min="0"
                           value="${isEditing ? order.amount : ''}" required>
                </div>

                <div class="form-group">
                    <label class="form-label">الحالة</label>
                    <select id="status" class="form-select" required>
                        <option value="pending" ${isEditing && order.status === 'pending' ? 'selected' : ''}>معلق</option>
                        <option value="delivering" ${isEditing && order.status === 'delivering' ? 'selected' : ''}>قيد التسليم</option>
                        <option value="delayed" ${isEditing && order.status === 'delayed' ? 'selected' : ''}>مؤجل</option>
                        <option value="returned" ${isEditing && order.status === 'returned' ? 'selected' : ''}>راجع</option>
                        <option value="delivered" ${isEditing && order.status === 'delivered' ? 'selected' : ''}>تم التسليم</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">الأولوية</label>
                    <select id="priority" class="form-select" required>
                        <option value="normal" ${isEditing && order.priority === 'normal' ? 'selected' : ''}>عادي</option>
                        <option value="high" ${isEditing && order.priority === 'high' ? 'selected' : ''}>عالي</option>
                        <option value="urgent" ${isEditing && order.priority === 'urgent' ? 'selected' : ''}>مستعجل</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">العنوان</label>
                <textarea id="address" class="form-textarea" rows="3" required>${isEditing ? order.address : ''}</textarea>
            </div>

            <div class="form-group">
                <label class="form-label">ملاحظات</label>
                <textarea id="notes" class="form-textarea" rows="3">${isEditing ? order.notes : ''}</textarea>
            </div>

            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                <button type="submit" class="btn btn-primary">${isEditing ? 'تحديث' : 'إضافة'}</button>
            </div>
        </form>
    `;

    // Add form submit handler
    document.getElementById('order-form').addEventListener('submit', handleOrderSubmit);

    orderModal.classList.remove('hidden');
}

function handleOrderSubmit(e) {
    e.preventDefault();

    const formData = {
        id: document.getElementById('order-id').value,
        recipientName: document.getElementById('recipient-name').value,
        senderName: document.getElementById('sender-name').value,
        phone: document.getElementById('phone').value,
        city: document.getElementById('city').value,
        cityName: getCityName(document.getElementById('city').value),
        address: document.getElementById('address').value,
        amount: parseFloat(document.getElementById('amount').value),
        status: document.getElementById('status').value,
        priority: document.getElementById('priority').value,
        notes: document.getElementById('notes').value,
        date: editingOrderId ? currentOrders.find(o => o.id === editingOrderId).date : new Date().toISOString().split('T')[0],
        deliveryDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        assignedDriver: editingOrderId ? currentOrders.find(o => o.id === editingOrderId).assignedDriver : '',
        type: 'general'
    };

    if (editingOrderId) {
        // Update existing order
        const orderIndex = currentOrders.findIndex(o => o.id === editingOrderId);
        currentOrders[orderIndex] = formData;
        showNotification('تم تحديث الطلب بنجاح', 'success');
    } else {
        // Add new order
        currentOrders.unshift(formData);
        showNotification('تم إضافة الطلب بنجاح', 'success');
    }

    applyFilters();
    closeModal();
}

function editOrder(orderId) {
    showOrderModal(orderId);
}

function deleteOrder(orderId) {
    if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
        const orderIndex = currentOrders.findIndex(order => order.id === orderId);
        if (orderIndex !== -1) {
            currentOrders.splice(orderIndex, 1);
            selectedOrders.delete(orderId);
            applyFilters();
            showNotification('تم حذف الطلب بنجاح', 'success');
        }
    }
}

function showOrderDetails(orderId) {
    const order = currentOrders.find(order => order.id === orderId);
    if (!order) return;

    const modalBody = document.getElementById('modal-body');
    modalBody.innerHTML = `
        <div style="display: grid; gap: 20px;">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div>
                    <strong>رقم الوصل:</strong><br>
                    <span style="color: #3b82f6; font-weight: 600;">${order.id}</span>
                </div>
                <div>
                    <strong>اسم المرسل إليه:</strong><br>
                    ${order.recipientName}
                </div>
                <div>
                    <strong>اسم المرسل:</strong><br>
                    ${order.senderName}
                </div>
                <div>
                    <strong>رقم الهاتف:</strong><br>
                    <a href="tel:${order.phone}" style="color: #10b981;">${order.phone}</a>
                </div>
                <div>
                    <strong>المدينة:</strong><br>
                    ${order.cityName}
                </div>
                <div>
                    <strong>تاريخ الإرسال:</strong><br>
                    ${formatDate(order.date)}
                </div>
                <div>
                    <strong>المبلغ:</strong><br>
                    <span style="color: #059669; font-weight: 600;">${order.amount.toFixed(2)} ر.س</span>
                </div>
                <div>
                    <strong>الأولوية:</strong><br>
                    <span class="priority-badge priority-${order.priority}">${getPriorityText(order.priority)}</span>
                </div>
            </div>

            <div>
                <strong>العنوان:</strong><br>
                <div style="background: rgba(163, 177, 198, 0.1); padding: 10px; border-radius: 8px; margin-top: 5px;">
                    ${order.address}
                </div>
            </div>

            <div>
                <strong>الحالة:</strong><br>
                <span class="status-badge status-${order.status}">${getStatusText(order.status)}</span>
            </div>

            ${order.notes ? `
                <div>
                    <strong>ملاحظات:</strong><br>
                    <div style="background: rgba(163, 177, 198, 0.1); padding: 10px; border-radius: 8px; margin-top: 5px;">
                        ${order.notes}
                    </div>
                </div>
            ` : ''}

            <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-top: 20px;">
                <button class="btn btn-primary" onclick="editOrder('${order.id}'); closeModal();">تعديل</button>
                <button class="btn btn-danger" onclick="deleteOrder('${order.id}'); closeModal();">حذف</button>
            </div>
        </div>
    `;

    document.getElementById('modal-title').textContent = 'تفاصيل الطلب';
    orderModal.classList.remove('hidden');
}

// Selection functions
function handleSelectAll() {
    const isChecked = selectAllCheckbox.checked;
    const startIndex = (currentPage - 1) * ordersPerPage;
    const endIndex = startIndex + ordersPerPage;
    const pageOrders = filteredOrders.slice(startIndex, endIndex);

    pageOrders.forEach(order => {
        if (isChecked) {
            selectedOrders.add(order.id);
        } else {
            selectedOrders.delete(order.id);
        }
    });

    renderOrders();
    updateBulkActionsButton();
}

function toggleOrderSelection(orderId) {
    if (selectedOrders.has(orderId)) {
        selectedOrders.delete(orderId);
    } else {
        selectedOrders.add(orderId);
    }

    updateSelectAllCheckbox();
    renderOrders();
    updateBulkActionsButton();
}

function updateSelectAllCheckbox() {
    const startIndex = (currentPage - 1) * ordersPerPage;
    const endIndex = startIndex + ordersPerPage;
    const pageOrders = filteredOrders.slice(startIndex, endIndex);

    const selectedPageOrders = pageOrders.filter(order => selectedOrders.has(order.id));

    if (selectedPageOrders.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (selectedPageOrders.length === pageOrders.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

function updateBulkActionsButton() {
    const selectedCount = selectedOrders.size;
    bulkActionsBtn.textContent = selectedCount > 0 ? `إجراءات متعددة (${selectedCount})` : 'إجراءات متعددة';
    bulkActionsBtn.disabled = selectedCount === 0;
}

// Bulk actions
function showBulkModal() {
    if (selectedOrders.size === 0) {
        showNotification('يرجى تحديد طلب واحد على الأقل', 'warning');
        return;
    }

    document.getElementById('selected-count').textContent = selectedOrders.size;
    bulkModal.classList.remove('hidden');
}

function closeBulkModal() {
    bulkModal.classList.add('hidden');
}

function bulkUpdateStatus(newStatus) {
    if (confirm(`هل أنت متأكد من تحديث حالة ${selectedOrders.size} طلب؟`)) {
        selectedOrders.forEach(orderId => {
            const order = currentOrders.find(o => o.id === orderId);
            if (order) {
                order.status = newStatus;
            }
        });

        selectedOrders.clear();
        applyFilters();
        closeBulkModal();
        showNotification(`تم تحديث حالة الطلبات إلى ${getStatusText(newStatus)}`, 'success');
    }
}

function bulkDelete() {
    if (confirm(`هل أنت متأكد من حذف ${selectedOrders.size} طلب؟`)) {
        const selectedArray = Array.from(selectedOrders);
        selectedArray.forEach(orderId => {
            const orderIndex = currentOrders.findIndex(o => o.id === orderId);
            if (orderIndex !== -1) {
                currentOrders.splice(orderIndex, 1);
            }
        });

        selectedOrders.clear();
        applyFilters();
        closeBulkModal();
        showNotification('تم حذف الطلبات المحددة', 'success');
    }
}

// Utility functions
function generateOrderId() {
    const prefix = 'DL';
    const timestamp = Date.now().toString().slice(-6);
    return `${prefix}${timestamp}`;
}

function getCityName(cityCode) {
    const cities = {
        'riyadh': 'الرياض',
        'jeddah': 'جدة',
        'dammam': 'الدمام',
        'mecca': 'مكة'
    };
    return cities[cityCode] || cityCode;
}

function getPriorityText(priority) {
    const priorities = {
        'normal': 'عادي',
        'high': 'عالي',
        'urgent': 'مستعجل'
    };
    return priorities[priority] || priority;
}

function getStatusText(status) {
    const statuses = {
        'pending': 'معلق',
        'delivering': 'قيد التسليم',
        'delayed': 'مؤجل',
        'returned': 'راجع',
        'delivered': 'تم التسليم'
    };
    return statuses[status] || status;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function updateOrdersCount() {
    document.getElementById('total-orders').textContent = filteredOrders.length;
}

function updatePagination() {
    const totalPages = Math.ceil(filteredOrders.length / ordersPerPage);
    const startIndex = (currentPage - 1) * ordersPerPage;
    const endIndex = Math.min(startIndex + ordersPerPage, filteredOrders.length);

    // Update pagination info
    document.getElementById('showing-from').textContent = filteredOrders.length > 0 ? startIndex + 1 : 0;
    document.getElementById('showing-to').textContent = endIndex;
    document.getElementById('total-count').textContent = filteredOrders.length;

    // Update pagination controls
    document.getElementById('prev-page').disabled = currentPage === 1;
    document.getElementById('next-page').disabled = currentPage === totalPages || totalPages === 0;

    // Update page numbers
    const pageNumbers = document.getElementById('page-numbers');
    pageNumbers.innerHTML = '';

    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-number ${i === currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => goToPage(i);
            pageNumbers.appendChild(pageBtn);
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            const dots = document.createElement('span');
            dots.textContent = '...';
            dots.style.padding = '8px 4px';
            dots.style.color = '#4a5568';
            pageNumbers.appendChild(dots);
        }
    }
}

function goToPage(page) {
    currentPage = page;
    renderOrders();
    updatePagination();
    selectedOrders.clear();
    updateSelectAllCheckbox();
    updateBulkActionsButton();
}

// Export function
function exportOrders() {
    const csvContent = generateCSV(filteredOrders);
    downloadCSV(csvContent, 'orders.csv');
    showNotification('تم تصدير البيانات بنجاح', 'success');
}

function generateCSV(orders) {
    const headers = ['رقم الوصل', 'اسم المرسل إليه', 'اسم المرسل', 'رقم الهاتف', 'المدينة', 'العنوان', 'تاريخ الإرسال', 'المبلغ', 'الحالة', 'الأولوية', 'ملاحظات'];
    const rows = orders.map(order => [
        order.id,
        order.recipientName,
        order.senderName,
        order.phone,
        order.cityName,
        order.address,
        order.date,
        order.amount,
        getStatusText(order.status),
        getPriorityText(order.priority),
        order.notes || ''
    ]);

    return [headers, ...rows].map(row => row.map(field => `"${field}"`).join(',')).join('\n');
}

function downloadCSV(content, filename) {
    const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
}

// Other functions
function refreshOrders() {
    showNotification('تم تحديث البيانات', 'info');
    applyFilters();
}

function closeModal() {
    orderModal.classList.add('hidden');
    editingOrderId = null;
}

function handleLogout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('isLoggedIn');
        window.location.href = 'index.html';
    }
}

function handleKeyboardShortcuts(e) {
    // Prevent shortcuts when typing in input fields
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
        // Only allow Escape to work in input fields
        if (e.key === 'Escape') {
            e.target.blur();
            closeModal();
            closeBulkModal();
        }
        return;
    }

    // Ctrl/Cmd + K to focus search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    // Ctrl/Cmd + R to refresh data
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        refreshOrders();
    }

    // Ctrl/Cmd + N to add new order
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        showOrderModal();
    }

    // Ctrl/Cmd + E to export
    if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
        e.preventDefault();
        exportOrders();
    }

    // Ctrl/Cmd + B for bulk actions
    if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault();
        if (selectedOrders.size > 0) {
            showBulkModal();
        } else {
            showNotification('يرجى تحديد طلب واحد على الأقل', 'warning');
        }
    }

    // Navigation shortcuts
    if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
        e.preventDefault();
        window.location.href = 'dashboard.html';
    }

    if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
        e.preventDefault();
        window.location.href = 'drivers.html';
    }

    if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
        e.preventDefault();
        window.location.href = 'customers.html';
    }

    if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
        e.preventDefault();
        window.location.href = 'assignments.html';
    }

    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        window.location.href = 'settings.html';
    }

    if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        window.location.href = 'reports.html';
    }

    if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
        e.preventDefault();
        handleLogout();
    }

    // Escape to close modals
    if (e.key === 'Escape') {
        closeModal();
        closeBulkModal();
    }

    // F1 to show help
    if (e.key === 'F1') {
        e.preventDefault();
        showKeyboardShortcutsHelp();
    }

    // F5 to refresh
    if (e.key === 'F5') {
        e.preventDefault();
        refreshOrders();
    }

    // View switching with V key
    if (e.key === 'v' || e.key === 'V') {
        const currentView = document.querySelector('.view-btn.active').dataset.view;
        const newView = currentView === 'table' ? 'grid' : 'table';
        switchView(newView);
    }

    // Select all with Ctrl+A
    if ((e.ctrlKey || e.metaKey) && e.key === 'a' && !e.shiftKey) {
        e.preventDefault();
        selectAllCheckbox.checked = !selectAllCheckbox.checked;
        handleSelectAll();
    }

    // Alt + Arrow keys for navigation
    if (e.altKey) {
        switch(e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                window.history.back();
                break;
            case 'ArrowRight':
                e.preventDefault();
                window.history.forward();
                break;
            case 'ArrowUp':
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
                break;
            case 'ArrowDown':
                e.preventDefault();
                window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                break;
        }
    }

    // Page navigation with Page Up/Down
    if (e.key === 'PageUp') {
        e.preventDefault();
        if (currentPage > 1) {
            goToPage(currentPage - 1);
        }
    }

    if (e.key === 'PageDown') {
        e.preventDefault();
        const totalPages = Math.ceil(filteredOrders.length / ordersPerPage);
        if (currentPage < totalPages) {
            goToPage(currentPage + 1);
        }
    }
}

// Add event listeners for checkboxes after rendering
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('order-checkbox')) {
        const orderId = e.target.dataset.orderId;
        toggleOrderSelection(orderId);
    }
});

// Notification function (reuse from dashboard.js)
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <svg style="width: 20px; height: 20px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                ${getNotificationIcon(type)}
            </svg>
            <span>${message}</span>
        </div>
    `;

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${getNotificationColor(type)};
        color: white;
        padding: 15px 20px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 1001;
        animation: slideInRight 0.3s ease-out;
        max-width: 400px;
        font-weight: 500;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 4000);
}

function getNotificationIcon(type) {
    const icons = {
        'success': '<path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/><polyline points="22,4 12,14.01 9,11.01"/>',
        'error': '<circle cx="12" cy="12" r="10"/><line x1="15" y1="9" x2="9" y2="15"/><line x1="9" y1="9" x2="15" y2="15"/>',
        'warning': '<path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/><line x1="12" y1="9" x2="12" y2="13"/><line x1="12" y1="17" x2="12.01" y2="17"/>',
        'info': '<circle cx="12" cy="12" r="10"/><line x1="12" y1="16" x2="12" y2="12"/><line x1="12" y1="8" x2="12.01" y2="8"/>'
    };
    return icons[type] || icons.info;
}

function getNotificationColor(type) {
    const colors = {
        'success': 'linear-gradient(135deg, #10b981, #059669)',
        'error': 'linear-gradient(135deg, #ef4444, #dc2626)',
        'warning': 'linear-gradient(135deg, #f59e0b, #d97706)',
        'info': 'linear-gradient(135deg, #3b82f6, #2563eb)'
    };
    return colors[type] || colors.info;
}

// Show keyboard shortcuts help
function showKeyboardShortcutsHelp() {
    const helpModal = document.createElement('div');
    helpModal.className = 'modal';
    helpModal.innerHTML = `
        <div class="modal-content large">
            <div class="modal-header">
                <h3>اختصارات لوحة المفاتيح - إدارة الطلبات</h3>
                <button class="close-modal" onclick="this.closest('.modal').remove()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div class="shortcuts-section">
                        <h4>🔍 البحث والتصفية</h4>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>K</kbd>
                            <span>البحث السريع</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>R</kbd>
                            <span>تحديث البيانات</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>F5</kbd>
                            <span>تحديث الصفحة</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Esc</kbd>
                            <span>إغلاق النوافذ</span>
                        </div>
                    </div>

                    <div class="shortcuts-section">
                        <h4>📝 إدارة الطلبات</h4>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>N</kbd>
                            <span>طلب جديد</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>E</kbd>
                            <span>تصدير البيانات</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>B</kbd>
                            <span>إجراءات متعددة</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>A</kbd>
                            <span>تحديد الكل</span>
                        </div>
                    </div>

                    <div class="shortcuts-section">
                        <h4>👁️ العرض والتنقل</h4>
                        <div class="shortcut-item">
                            <kbd>V</kbd>
                            <span>تبديل العرض (جدول/بطاقات)</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Page Up</kbd>
                            <span>الصفحة السابقة</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Page Down</kbd>
                            <span>الصفحة التالية</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Alt</kbd> + <kbd>↑</kbd>
                            <span>أعلى الصفحة</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Alt</kbd> + <kbd>↓</kbd>
                            <span>أسفل الصفحة</span>
                        </div>
                    </div>

                    <div class="shortcuts-section">
                        <h4>🔗 التنقل السريع</h4>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>D</kbd>
                            <span>لوحة التحكم</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>U</kbd>
                            <span>إدارة المندوبين</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>C</kbd>
                            <span>إدارة العملاء</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>S</kbd>
                            <span>الإعدادات</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>P</kbd>
                            <span>التقارير</span>
                        </div>
                    </div>

                    <div class="shortcuts-section">
                        <h4>🔐 النظام</h4>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>L</kbd>
                            <span>تسجيل الخروج</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>F1</kbd>
                            <span>عرض هذه المساعدة</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Alt</kbd> + <kbd>←</kbd>
                            <span>الصفحة السابقة</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Alt</kbd> + <kbd>→</kbd>
                            <span>الصفحة التالية</span>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 15px; background: rgba(59, 130, 246, 0.1); border-radius: 10px;">
                    <p style="margin: 0; color: #1e40af; font-weight: 500;">
                        💡 نصيحة: استخدم <kbd>Ctrl+K</kbd> للبحث السريع و <kbd>V</kbd> لتبديل العرض بين الجدول والبطاقات!
                    </p>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(helpModal);

    // Close on background click
    helpModal.addEventListener('click', (e) => {
        if (e.target === helpModal) {
            helpModal.remove();
        }
    });
}

// Add keyboard shortcuts styles if not already added
if (!document.getElementById('keyboard-shortcuts-styles')) {
    const keyboardStyles = document.createElement('style');
    keyboardStyles.id = 'keyboard-shortcuts-styles';
    keyboardStyles.textContent = `
        .shortcuts-section {
            background: rgba(163, 177, 198, 0.1);
            padding: 20px;
            border-radius: 15px;
        }

        .shortcuts-section h4 {
            margin: 0 0 15px 0;
            color: #1a202c;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .shortcut-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(163, 177, 198, 0.2);
        }

        .shortcut-item:last-child {
            border-bottom: none;
        }

        .shortcut-item kbd {
            background: #e0e5ec;
            border: 1px solid #a3b1c6;
            border-radius: 6px;
            padding: 4px 8px;
            font-family: 'Cairo', sans-serif;
            font-size: 0.8rem;
            font-weight: 600;
            color: #374151;
            box-shadow: 0 2px 4px rgba(163, 177, 198, 0.3);
            margin: 0 2px;
        }

        .shortcut-item span {
            color: #4a5568;
            font-size: 0.9rem;
        }
    `;
    document.head.appendChild(keyboardStyles);
}
