/* Sidebar Navigation Styles */
.app-layout {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 280px;
    background: #e0e5ec;
    box-shadow: 4px 0 20px rgba(163, 177, 198, 0.3);
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: all 0.3s ease;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(163, 177, 198, 0.2);
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-logo {
    width: 45px;
    height: 45px;
    background: #e0e5ec;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 6px 6px 12px #a3b1c6, -6px -6px 12px #ffffff;
    flex-shrink: 0;
}

.sidebar-logo svg {
    width: 24px;
    height: 24px;
    color: #3b82f6;
}

.sidebar-title {
    display: flex;
    flex-direction: column;
    gap: 2px;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .sidebar-title {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-title h1 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #1a202c;
    margin: 0;
}

.sidebar-title p {
    font-size: 0.8rem;
    color: #4a5568;
    margin: 0;
}

.sidebar-toggle {
    position: absolute;
    top: 25px;
    left: -15px;
    width: 30px;
    height: 30px;
    background: #e0e5ec;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
    transition: all 0.3s ease;
    z-index: 1001;
}

.sidebar-toggle:hover {
    box-shadow: 2px 2px 4px #a3b1c6, -2px -2px 4px #ffffff;
}

.sidebar-toggle svg {
    width: 16px;
    height: 16px;
    color: #4a5568;
    transition: transform 0.3s ease;
}

.sidebar.collapsed .sidebar-toggle svg {
    transform: rotate(180deg);
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-section {
    margin-bottom: 30px;
}

.nav-section-title {
    padding: 0 20px 10px;
    font-size: 0.8rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-section-title {
    opacity: 0;
    height: 0;
    padding: 0;
    margin: 0;
    overflow: hidden;
}

.nav-item {
    display: block;
    padding: 12px 20px;
    color: #4a5568;
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: 15px;
}

.nav-item:hover {
    background: rgba(163, 177, 198, 0.1);
    color: #3b82f6;
}

.nav-item.active {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border-left: 3px solid #3b82f6;
}

.nav-item.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.nav-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.nav-text {
    opacity: 1;
    transition: opacity 0.3s ease;
    white-space: nowrap;
}

.sidebar.collapsed .nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .nav-item {
    padding: 12px;
    justify-content: center;
}

/* Badge for notifications */
.nav-badge {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    margin-right: auto;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-badge {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* Main content adjustment */
.main-wrapper {
    flex: 1;
    margin-right: 280px;
    transition: margin-right 0.3s ease;
    min-height: 100vh;
}

.sidebar.collapsed + .main-wrapper {
    margin-right: 70px;
}

/* User info section */
.sidebar-user {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    border-top: 1px solid rgba(163, 177, 198, 0.2);
    background: #e0e5ec;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .user-info {
    opacity: 0;
    height: 0;
    padding: 0;
    overflow: hidden;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    flex-shrink: 0;
    box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
}

.user-details {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1a202c;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    font-size: 0.8rem;
    color: #4a5568;
    margin: 0;
}

.logout-sidebar {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.logout-sidebar:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.logout-sidebar svg {
    width: 18px;
    height: 18px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        width: 280px;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .sidebar.collapsed {
        width: 280px;
    }
    
    .main-wrapper {
        margin-right: 0;
    }
    
    .sidebar.collapsed + .main-wrapper {
        margin-right: 0;
    }
    
    .mobile-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .mobile-overlay.active {
        opacity: 1;
        visibility: visible;
    }
    
    .mobile-menu-btn {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        background: #e0e5ec;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 6px 6px 12px #a3b1c6, -6px -6px 12px #ffffff;
        z-index: 1002;
        transition: all 0.3s ease;
    }
    
    .mobile-menu-btn:hover {
        box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
    }
    
    .mobile-menu-btn svg {
        width: 24px;
        height: 24px;
        color: #4a5568;
    }
}

@media (min-width: 769px) {
    .mobile-menu-btn {
        display: none;
    }
}

/* Scrollbar styling for sidebar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(163, 177, 198, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(163, 177, 198, 0.5);
}

/* Animation for nav items */
.nav-item {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
