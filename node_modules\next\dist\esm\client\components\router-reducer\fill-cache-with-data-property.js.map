{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-cache-with-data-property.ts"], "names": ["CacheStates", "createRouterCache<PERSON>ey", "fillCacheWithDataProperty", "newCache", "existingCache", "flightSegmentPath", "fetchResponse", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "existingChildCacheNode", "childCacheNode", "data", "status", "DATA_FETCH", "subTreeData", "slice"], "mappings": "AAEA,SAASA,WAAW,QAAQ,wDAAuD;AAEnF,SAASC,oBAAoB,QAAQ,4BAA2B;AAEhE;;CAEC,GACD,OAAO,SAASC,0BACdC,QAAmB,EACnBC,aAAwB,EACxBC,iBAAoC,EACpCC,aAAuD;IAEvD,MAAMC,cAAcF,kBAAkBG,MAAM,IAAI;IAEhD,MAAM,CAACC,kBAAkBC,QAAQ,GAAGL;IACpC,MAAMM,WAAWV,qBAAqBS;IAEtC,MAAME,0BACJR,cAAcS,cAAc,CAACC,GAAG,CAACL;IAEnC,IAAIM,kBAAkBZ,SAASU,cAAc,CAACC,GAAG,CAACL;IAElD,IAAI,CAACM,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BT,SAASU,cAAc,CAACI,GAAG,CAACR,kBAAkBM;IAChD;IAEA,MAAMG,yBAAyBN,2CAAAA,wBAAyBE,GAAG,CAACH;IAC5D,IAAIQ,iBAAiBJ,gBAAgBD,GAAG,CAACH;IAEzC,yFAAyF;IACzF,IAAIJ,aAAa;QACf,IACE,CAACY,kBACD,CAACA,eAAeC,IAAI,IACpBD,mBAAmBD,wBACnB;YACAH,gBAAgBE,GAAG,CAACN,UAAU;gBAC5BU,QAAQrB,YAAYsB,UAAU;gBAC9BF,MAAMd;gBACNiB,aAAa;gBACbV,gBAAgB,IAAIG;YACtB;QACF;QACA;IACF;IAEA,IAAI,CAACG,kBAAkB,CAACD,wBAAwB;QAC9C,+EAA+E;QAC/E,IAAI,CAACC,gBAAgB;YACnBJ,gBAAgBE,GAAG,CAACN,UAAU;gBAC5BU,QAAQrB,YAAYsB,UAAU;gBAC9BF,MAAMd;gBACNiB,aAAa;gBACbV,gBAAgB,IAAIG;YACtB;QACF;QACA;IACF;IAEA,IAAIG,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfE,QAAQF,eAAeE,MAAM;YAC7BD,MAAMD,eAAeC,IAAI;YACzBG,aAAaJ,eAAeI,WAAW;YACvCV,gBAAgB,IAAIG,IAAIG,eAAeN,cAAc;QACvD;QACAE,gBAAgBE,GAAG,CAACN,UAAUQ;IAChC;IAEA,OAAOjB,0BACLiB,gBACAD,wBACAb,kBAAkBmB,KAAK,CAAC,IACxBlB;AAEJ"}