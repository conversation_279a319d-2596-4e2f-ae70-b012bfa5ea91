{"version": 3, "sources": ["../../../../src/client/components/react-dev-overlay/hot-reloader-client.tsx"], "names": ["React", "useCallback", "useEffect", "useReducer", "useMemo", "startTransition", "stripAnsi", "formatWebpackMessages", "useRouter", "ACTION_VERSION_INFO", "INITIAL_OVERLAY_STATE", "errorOverlayReducer", "ACTION_BUILD_OK", "ACTION_BUILD_ERROR", "ACTION_BEFORE_REFRESH", "ACTION_REFRESH", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "parseStack", "ReactDevOverlay", "RuntimeError<PERSON>andler", "useErrorHandler", "useSendMessage", "useTurbopack", "useWebsocket", "useWebsocketPing", "parseComponentStack", "HMR_ACTIONS_SENT_TO_BROWSER", "mostRecentCompilationHash", "__nextDevClientId", "Math", "round", "random", "Date", "now", "reloading", "onBeforeFastRefresh", "dispatcher", "hasUpdates", "onBeforeRefresh", "onFastRefresh", "onBuildOk", "onRefresh", "handleAvailableHash", "hash", "isUpdateAvailable", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "performFullReload", "err", "sendMessage", "stackTrace", "stack", "split", "slice", "join", "message", "JSON", "stringify", "event", "hadRuntimeError", "window", "location", "reload", "tryApplyUpdates", "onBeforeUpdate", "onHotUpdateSuccess", "handleApplyUpdates", "updatedModules", "console", "warn", "Boolean", "length", "process", "env", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "check", "then", "apply", "processMessage", "obj", "router", "handleErrors", "errors", "formatted", "warnings", "onBuildError", "i", "error", "handleHotUpdate", "TURBOPACK", "onBeforeHotUpdate", "onSuccessfulHotUpdate", "action", "BUILDING", "log", "FINISH_BUILDING", "BUILT", "SYNC", "onVersionInfo", "versionInfo", "hasErrors", "errorCount", "clientId", "hasWarnings", "warningCount", "isHotUpdate", "formattedMessages", "__NEXT_DATA__", "page", "SERVER_COMPONENT_CHANGES", "fastRefresh", "RELOAD_PAGE", "REMOVED_PAGE", "ADDED_PAGE", "SERVER_ERROR", "errorJSON", "parse", "Error", "DEV_PAGES_MANIFEST_UPDATE", "HotReload", "assetPrefix", "children", "state", "dispatch", "type", "handleOnUnhandledError", "componentStack", "_componentStack", "reason", "frames", "componentStackFrames", "handleOnUnhandledRejection", "handleOnReactError", "webSocketRef", "processTurbopackMessage", "data", "handledByTurbopack", "websocket", "current", "addEventListener", "removeEventListener", "onReactError"], "mappings": "AACA,OAAOA,SACLC,WAAW,EACXC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,eAAe,QACV,QAAO;AACd,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,2BAA2B,kDAAiD;AACnF,SAASC,SAAS,QAAQ,gBAAe;AACzC,SACEC,mBAAmB,EACnBC,qBAAqB,EACrBC,mBAAmB,QACd,mCAAkC;AACzC,SACEC,eAAe,EACfC,kBAAkB,EAClBC,qBAAqB,EACrBC,cAAc,EACdC,sBAAsB,EACtBC,0BAA0B,QACrB,mCAAkC;AACzC,SAASC,UAAU,QAAQ,gCAA+B;AAC1D,OAAOC,qBAAqB,6BAA4B;AACxD,SACEC,mBAAmB,EACnBC,eAAe,QACV,uCAAsC;AAC7C,SACEC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,gBAAgB,QACX,mCAAkC;AACzC,SAASC,mBAAmB,QAAQ,2CAA0C;AAE9E,SAASC,2BAA2B,QAAQ,yCAAwC;AAWpF,IAAIC,4BAAiC;AACrC,IAAIC,oBAAoBC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AACjE,IAAIC,YAAY;AAEhB,SAASC,oBAAoBC,UAAsB,EAAEC,UAAmB;IACtE,IAAIA,YAAY;QACdD,WAAWE,eAAe;IAC5B;AACF;AAEA,SAASC,cAAcH,UAAsB,EAAEC,UAAmB;IAChED,WAAWI,SAAS;IACpB,IAAIH,YAAY;QACdD,WAAWK,SAAS;IACtB;AACF;AAEA,kDAAkD;AAClD,SAASC,oBAAoBC,IAAY;IACvC,sCAAsC;IACtChB,4BAA4BgB;AAC9B;AAEA,mDAAmD;AACnD,SAASC;IACP,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOjB,8BAA8BkB;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,qCAAqC;IACrC,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAO;IAChC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASC,QAAQH,MAAW;YAC1B,IAAIA,WAAW,QAAQ;gBACrB,qCAAqC;gBACrCF,OAAOC,GAAG,CAACK,mBAAmB,CAACD;gBAC/BD;YACF;QACF;QACA,qCAAqC;QACrCJ,OAAOC,GAAG,CAACM,gBAAgB,CAACF;IAC9B;AACF;AAEA,SAASG,kBAAkBC,GAAQ,EAAEC,WAAgB;IACnD,MAAMC,aACJF,OACC,CAAA,AAACA,IAAIG,KAAK,IAAIH,IAAIG,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDN,IAAIO,OAAO,IACXP,MAAM,EAAC;IAEXC,YACEO,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPR;QACAS,iBAAiB,CAAC,CAAChD,oBAAoBgD,eAAe;IACxD;IAGF,IAAIjC,WAAW;IACfA,YAAY;IACZkC,OAAOC,QAAQ,CAACC,MAAM;AACxB;AAEA,iEAAiE;AACjE,SAASC,gBACPC,cAA6C,EAC7CC,kBAAiD,EACjDhB,WAAgB,EAChBrB,UAAsB;IAEtB,IAAI,CAACQ,uBAAuB,CAACE,mBAAmB;QAC9CV,WAAWI,SAAS;QACpB;IACF;IAEA,SAASkC,mBAAmBlB,GAAQ,EAAEmB,cAA4B;QAChE,IAAInB,OAAOrC,oBAAoBgD,eAAe,IAAI,CAACQ,gBAAgB;YACjE,IAAInB,KAAK;gBACPoB,QAAQC,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;YAEN,OAAO,IAAI1D,oBAAoBgD,eAAe,EAAE;gBAC9CS,QAAQC,IAAI,CACV;YAEJ;YACAtB,kBAAkBC,KAAKC;YACvB;QACF;QAEA,MAAMpB,aAAayC,QAAQH,eAAeI,MAAM;QAChD,IAAI,OAAON,uBAAuB,YAAY;YAC5C,iCAAiC;YACjCA,mBAAmBpC;QACrB;QAEA,IAAIO,qBAAqB;YACvB,+DAA+D;YAC/D2B,gBACElC,aAAa,KAAO,IAAImC,gBACxBnC,aAAa,IAAMD,WAAWI,SAAS,KAAKiC,oBAC5ChB,aACArB;QAEJ,OAAO;YACLA,WAAWI,SAAS;YACpB,IAAIwC,QAAQC,GAAG,CAACC,gBAAgB,EAAE;gBAChChC,kBAAkB;oBAChB,IAAIiC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,qCAAqC;IACrCrC,OAAOC,GAAG,CACPqC,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAACX;QACL,IAAI,CAACA,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,OAAOH,mBAAmB,YAAY;YACxC,MAAMnC,aAAayC,QAAQH,eAAeI,MAAM;YAChDP,eAAenC;QACjB;QACA,2DAA2D;QAC3D,qCAAqC;QACrC,OAAOU,OAAOC,GAAG,CAACuC,KAAK;IACzB,GACCD,IAAI,CACH,CAACX;QACCD,mBAAmB,MAAMC;IAC3B,GACA,CAACnB;QACCkB,mBAAmBlB,KAAK;IAC1B;AAEN;AAEA,SAASgC,eACPC,GAAqB,EACrBhC,WAAgB,EAChBiC,MAAoC,EACpCtD,UAAsB;IAEtB,IAAI,CAAE,CAAA,YAAYqD,GAAE,GAAI;QACtB;IACF;IAEA,SAASE,aAAaC,MAA8B;QAClD,8BAA8B;QAC9B,MAAMC,YAAYvF,sBAAsB;YACtCsF,QAAQA;YACRE,UAAU,EAAE;QACd;QAEA,6BAA6B;QAC7B1D,WAAW2D,YAAY,CAACF,UAAUD,MAAM,CAAC,EAAE;QAE3C,gCAAgC;QAChC,IAAK,IAAII,IAAI,GAAGA,IAAIH,UAAUD,MAAM,CAACb,MAAM,EAAEiB,IAAK;YAChDpB,QAAQqB,KAAK,CAAC5F,UAAUwF,UAAUD,MAAM,CAACI,EAAE;QAC7C;QAEA,gCAAgC;QAChC,0CAA0C;QAC1C,IAAIhB,QAAQC,GAAG,CAACC,gBAAgB,EAAE;YAChC,IAAIC,KAAKC,aAAa,EAAE;gBACtBD,KAAKC,aAAa,CAACS,UAAUD,MAAM,CAAC,EAAE;gBACtCT,KAAKC,aAAa,GAAG;YACvB;QACF;IACF;IAEA,SAASc;QACP,IAAIlB,QAAQC,GAAG,CAACkB,SAAS,EAAE;YACzB5D,cAAcH,YAAY;QAC5B,OAAO;YACLmC,gBACE,SAAS6B,kBAAkB/D,UAAmB;gBAC5CF,oBAAoBC,YAAYC;YAClC,GACA,SAASgE,sBAAsBhE,UAAe;gBAC5C,qDAAqD;gBACrD,sDAAsD;gBACtDE,cAAcH,YAAYC;YAC5B,GACAoB,aACArB;QAEJ;IACF;IAEA,OAAQqD,IAAIa,MAAM;QAChB,KAAK5E,4BAA4B6E,QAAQ;YAAE;gBACzC3B,QAAQ4B,GAAG,CAAC;gBACZ;YACF;QACA,KAAK9E,4BAA4B+E,eAAe;YAAE;gBAChD;YACF;QACA,KAAK/E,4BAA4BgF,KAAK;QACtC,KAAKhF,4BAA4BiF,IAAI;YAAE;gBACrC,IAAIlB,IAAI9C,IAAI,EAAE;oBACZD,oBAAoB+C,IAAI9C,IAAI;gBAC9B;gBAEA,MAAM,EAAEiD,MAAM,EAAEE,QAAQ,EAAE,GAAGL;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAK;oBACxBrD,WAAWwE,aAAa,CAACnB,IAAIoB,WAAW;gBAC1C;gBACA,MAAMC,YAAYhC,QAAQc,UAAUA,OAAOb,MAAM;gBACjD,kEAAkE;gBAClE,IAAI+B,WAAW;oBACbrD,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACP6C,YAAYnB,OAAOb,MAAM;wBACzBiC,UAAUpF;oBACZ;oBAGF+D,aAAaC;oBACb;gBACF;gBAEA,MAAMqB,cAAcnC,QAAQgB,YAAYA,SAASf,MAAM;gBACvD,IAAIkC,aAAa;oBACfxD,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPgD,cAAcpB,SAASf,MAAM;wBAC7BiC,UAAUpF;oBACZ;oBAGF,2CAA2C;oBAC3C,MAAMuF,cAAc1B,IAAIa,MAAM,KAAK5E,4BAA4BiF,IAAI;oBAEnE,iCAAiC;oBACjC,MAAMS,oBAAoB9G,sBAAsB;wBAC9CwF,UAAUA;wBACVF,QAAQ,EAAE;oBACZ;oBAEA,IAAK,IAAII,IAAI,GAAGA,IAAIoB,kBAAkBtB,QAAQ,CAACf,MAAM,EAAEiB,IAAK;wBAC1D,IAAIA,MAAM,GAAG;4BACXpB,QAAQC,IAAI,CACV,+CACE;4BAEJ;wBACF;wBACAD,QAAQC,IAAI,CAACxE,UAAU+G,kBAAkBtB,QAAQ,CAACE,EAAE;oBACtD;oBAEA,0CAA0C;oBAC1C,IAAImB,aAAa;wBACfjB;oBACF;oBACA;gBACF;gBAEAzC,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP8C,UAAUpF;gBACZ;gBAGF,MAAMuF,cACJ1B,IAAIa,MAAM,KAAK5E,4BAA4BiF,IAAI,IAC9C,CAAA,CAACvC,OAAOiD,aAAa,IAAIjD,OAAOiD,aAAa,CAACC,IAAI,KAAK,SAAQ,KAChE1E;gBAEF,0CAA0C;gBAC1C,IAAIuE,aAAa;oBACfjB;gBACF;gBACA;YACF;QACA,uDAAuD;QACvD,KAAKxE,4BAA4B6F,wBAAwB;YAAE;gBACzD9D,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP8C,UAAUpF;gBACZ;gBAEF,IAAIT,oBAAoBgD,eAAe,EAAE;oBACvC,IAAIjC,WAAW;oBACfA,YAAY;oBACZ,OAAOkC,OAAOC,QAAQ,CAACC,MAAM;gBAC/B;gBACAlE,gBAAgB;oBACd,yCAAyC;oBACzCsF,OAAO8B,WAAW;oBAClBpF,WAAWK,SAAS;gBACtB;gBAEA,IAAIuC,QAAQC,GAAG,CAACC,gBAAgB,EAAE;oBAChC,IAAIC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;gBAEA;YACF;QACA,KAAK1D,4BAA4B+F,WAAW;YAAE;gBAC5ChE,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP8C,UAAUpF;gBACZ;gBAEF,IAAIM,WAAW;gBACfA,YAAY;gBACZ,OAAOkC,OAAOC,QAAQ,CAACC,MAAM;YAC/B;QACA,KAAK5C,4BAA4BgG,YAAY;YAAE;gBAC7C,+EAA+E;gBAC/E,yCAAyC;gBACzChC,OAAO8B,WAAW;gBAClB;YACF;QACA,KAAK9F,4BAA4BiG,UAAU;YAAE;gBAC3C,6EAA6E;gBAC7E,yCAAyC;gBACzCjC,OAAO8B,WAAW;gBAClB;YACF;QACA,KAAK9F,4BAA4BkG,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGpC;gBACtB,IAAIoC,WAAW;oBACb,MAAM,EAAE9D,OAAO,EAAEJ,KAAK,EAAE,GAAGK,KAAK8D,KAAK,CAACD;oBACtC,MAAM5B,QAAQ,IAAI8B,MAAMhE;oBACxBkC,MAAMtC,KAAK,GAAGA;oBACdgC,aAAa;wBAACM;qBAAM;gBACtB;gBACA;YACF;QACA,KAAKvE,4BAA4BsG,yBAAyB;YAAE;gBAC1D;YACF;QACA;YAAS,CACT;IACF;AACF;AAEA,eAAe,SAASC,UAAU,KAMjC;IANiC,IAAA,EAChCC,WAAW,EACXC,QAAQ,EAIT,GANiC;IAOhC,MAAM,CAACC,OAAOC,SAAS,GAAGnI,WACxBQ,qBACAD;IAEF,MAAM2B,aAAajC,QAAQ;QACzB,OAAO;YACLqC;gBACE6F,SAAS;oBAAEC,MAAM3H;gBAAgB;YACnC;YACAoF,cAAahC,OAAO;gBAClBsE,SAAS;oBAAEC,MAAM1H;oBAAoBmD;gBAAQ;YAC/C;YACAzB;gBACE+F,SAAS;oBAAEC,MAAMzH;gBAAsB;YACzC;YACA4B;gBACE4F,SAAS;oBAAEC,MAAMxH;gBAAe;YAClC;YACA8F,eAAcC,WAAW;gBACvBwB,SAAS;oBAAEC,MAAM9H;oBAAqBqG;gBAAY;YACpD;QACF;IACF,GAAG;QAACwB;KAAS;IAEb,MAAME,yBAAyBvI,YAAY,CAACiG;QAC1C,kGAAkG;QAClG,MAAMuC,iBAAiB,AAACvC,MAAcwC,eAAe;QACrDJ,SAAS;YACPC,MAAMvH;YACN2H,QAAQzC;YACR0C,QAAQ1H,WAAWgF,MAAMtC,KAAK;YAC9BiF,sBACEJ,kBAAkB/G,oBAAoB+G;QAC1C;IACF,GAAG,EAAE;IACL,MAAMK,6BAA6B7I,YAAY,CAAC0I;QAC9CL,SAAS;YACPC,MAAMtH;YACN0H,QAAQA;YACRC,QAAQ1H,WAAWyH,OAAO/E,KAAK;QACjC;IACF,GAAG,EAAE;IACL,MAAMmF,qBAAqB9I,YAAY;QACrCmB,oBAAoBgD,eAAe,GAAG;IACxC,GAAG,EAAE;IACL/C,gBAAgBmH,wBAAwBM;IAExC,MAAME,eAAexH,aAAa2G;IAClC1G,iBAAiBuH;IACjB,MAAMtF,cAAcpC,eAAe0H;IACnC,MAAMC,0BAA0B1H,aAAamC;IAE7C,MAAMiC,SAASnF;IAEfN,UAAU;QACR,MAAMmD,UAAU,CAACc;YACf,IAAI;gBACF,MAAMuB,MAAMzB,KAAK8D,KAAK,CAAC5D,MAAM+E,IAAI;gBACjC,MAAMC,qBAAqBF,2CAAAA,wBAA0BvD;gBACrD,IAAI,CAACyD,oBAAoB;oBACvB1D,eAAeC,KAAKhC,aAAaiC,QAAQtD;gBAC3C;YACF,EAAE,OAAOoB,KAAU;oBAEkCA;gBADnDoB,QAAQC,IAAI,CACV,4BAA4BX,MAAM+E,IAAI,GAAG,OAAQzF,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKG,KAAK,YAAVH,aAAc,EAAC;YAEpE;QACF;QAEA,MAAM2F,YAAYJ,aAAaK,OAAO;QACtC,IAAID,WAAW;YACbA,UAAUE,gBAAgB,CAAC,WAAWjG;QACxC;QAEA,OAAO,IAAM+F,aAAaA,UAAUG,mBAAmB,CAAC,WAAWlG;IACrE,GAAG;QAACK;QAAaiC;QAAQqD;QAAc3G;QAAY4G;KAAwB;IAE3E,qBACE,oBAAC9H;QAAgBqI,cAAcT;QAAoBV,OAAOA;OACvDD;AAGP"}