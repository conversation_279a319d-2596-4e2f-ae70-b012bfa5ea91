{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.ts"], "names": ["fillLazyItemsTillLeafWithHead", "newCache", "existingCache", "routerState", "cacheNodeSeedData", "head", "wasPrefetched", "isLastSegment", "Object", "keys", "length", "key", "parallelRouteState", "segmentForParallelRoute", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "parallelSeedData", "undefined", "existingParallelRoutesCacheNode", "parallelRoutes", "get", "parallelRouteCacheNode", "Map", "existingCacheNode", "newCacheNode", "seedNode", "status", "CacheStates", "READY", "data", "subTreeData", "LAZY_INITIALIZED", "set", "existingParallelRoutes"], "mappings": ";;;;+BAQg<PERSON>;;;eAAAA;;;+CARY;sCAMS;AAE9B,SAASA,8BACdC,QAAmB,EACnBC,aAAoC,EACpCC,WAA8B,EAC9BC,iBAA2C,EAC3CC,IAAqB,EACrBC,aAAuB;IAEvB,MAAMC,gBAAgBC,OAAOC,IAAI,CAACN,WAAW,CAAC,EAAE,EAAEO,MAAM,KAAK;IAC7D,IAAIH,eAAe;QACjBN,SAASI,IAAI,GAAGA;QAChB;IACF;IACA,+FAA+F;IAC/F,IAAK,MAAMM,OAAOR,WAAW,CAAC,EAAE,CAAE;QAChC,MAAMS,qBAAqBT,WAAW,CAAC,EAAE,CAACQ,IAAI;QAC9C,MAAME,0BAA0BD,kBAAkB,CAAC,EAAE;QACrD,MAAME,WAAWC,IAAAA,0CAAoB,EAACF;QAEtC,4EAA4E;QAC5E,2EAA2E;QAC3E,wEAAwE;QACxE,wEAAwE;QACxE,0EAA0E;QAC1E,qBAAqB;QACrB,EAAE;QACF,0EAA0E;QAC1E,wEAAwE;QACxE,kEAAkE;QAClE,MAAMG,mBACJZ,sBAAsB,QACtBA,iBAAiB,CAAC,EAAE,KAAK,QACzBA,iBAAiB,CAAC,EAAE,CAACO,IAAI,KAAKM,YAC1Bb,iBAAiB,CAAC,EAAE,CAACO,IAAI,GACzB;QACN,IAAIT,eAAe;YACjB,MAAMgB,kCACJhB,cAAciB,cAAc,CAACC,GAAG,CAACT;YACnC,IAAIO,iCAAiC;gBACnC,IAAIG,yBAAyB,IAAIC,IAAIJ;gBACrC,MAAMK,oBAAoBF,uBAAuBD,GAAG,CAACN;gBACrD,IAAIU;gBACJ,IAAIR,qBAAqB,MAAM;oBAC7B,qCAAqC;oBACrC,MAAMS,WAAWT,gBAAgB,CAAC,EAAE;oBACpCQ,eAAe;wBACbE,QAAQC,0CAAW,CAACC,KAAK;wBACzBC,MAAM;wBACNC,aAAaL;wBACbN,gBAAgB,IAAIG,IAAIC,qCAAAA,kBAAmBJ,cAAc;oBAC3D;gBACF,OAAO,IAAIb,iBAAiBiB,mBAAmB;oBAC7C,oEAAoE;oBACpE,2CAA2C;oBAC3CC,eAAe;wBACbE,QAAQH,kBAAkBG,MAAM;wBAChCG,MAAMN,kBAAkBM,IAAI;wBAC5BC,aAAaP,kBAAkBO,WAAW;wBAC1CX,gBAAgB,IAAIG,IAAIC,kBAAkBJ,cAAc;oBAC1D;gBACF,OAAO;oBACL,kEAAkE;oBAClE,iBAAiB;oBACjBK,eAAe;wBACbE,QAAQC,0CAAW,CAACI,gBAAgB;wBACpCF,MAAM;wBACNC,aAAa;wBACbX,gBAAgB,IAAIG,IAAIC,qCAAAA,kBAAmBJ,cAAc;oBAC3D;gBACF;gBAEA,mDAAmD;gBACnDE,uBAAuBW,GAAG,CAAClB,UAAUU;gBACrC,qEAAqE;gBACrExB,8BACEwB,cACAD,mBACAX,oBACAI,mBAAmBA,mBAAmB,MACtCX,MACAC;gBAGFL,SAASkB,cAAc,CAACa,GAAG,CAACrB,KAAKU;gBACjC;YACF;QACF;QAEA,IAAIG;QACJ,IAAIR,qBAAqB,MAAM;YAC7B,qCAAqC;YACrC,MAAMS,WAAWT,gBAAgB,CAAC,EAAE;YACpCQ,eAAe;gBACbE,QAAQC,0CAAW,CAACC,KAAK;gBACzBC,MAAM;gBACNC,aAAaL;gBACbN,gBAAgB,IAAIG;YACtB;QACF,OAAO;YACL,kEAAkE;YAClE,iBAAiB;YACjBE,eAAe;gBACbE,QAAQC,0CAAW,CAACI,gBAAgB;gBACpCF,MAAM;gBACNC,aAAa;gBACbX,gBAAgB,IAAIG;YACtB;QACF;QAEA,MAAMW,yBAAyBhC,SAASkB,cAAc,CAACC,GAAG,CAACT;QAC3D,IAAIsB,wBAAwB;YAC1BA,uBAAuBD,GAAG,CAAClB,UAAUU;QACvC,OAAO;YACLvB,SAASkB,cAAc,CAACa,GAAG,CAACrB,KAAK,IAAIW,IAAI;gBAAC;oBAACR;oBAAUU;iBAAa;aAAC;QACrE;QAEAxB,8BACEwB,cACAP,WACAL,oBACAI,kBACAX,MACAC;IAEJ;AACF"}