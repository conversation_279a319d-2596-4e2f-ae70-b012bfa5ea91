{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "names": ["webpack", "needsExperimentalReact", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "getDefineEnv", "isTurbopack", "allowedRevalidateHeaderKeys", "clientRouterFilters", "dev", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "previewModeId", "__NEXT_DEFINE_ENV", "Object", "keys", "process", "env", "reduce", "prev", "startsWith", "JSON", "stringify", "acc", "EdgeRuntime", "NEXT_EDGE_RUNTIME_PROVIDER", "experimental", "windowHistorySupport", "ppr", "useDeploymentIdServerActions", "deploymentId", "manualClientBasePath", "clientRouterFilter", "staticFilter", "dynamicFilter", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "buildActivity", "buildActivityPosition", "reactStrictMode", "optimizeFonts", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "deviceSizes", "images", "imageSizes", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "output", "basePath", "strictNextHead", "i18n", "analyticsId", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "assetPrefix", "undefined", "getDefineEnvPlugin", "options", "DefinePlugin"], "mappings": "AAEA,SAASA,OAAO,QAAQ,qCAAoC;AAC5D,SAASC,sBAAsB,QAAQ,wCAAuC;AAE9E,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC;IAEnI;AACF;AA0BA,OAAO,SAASC,aAAa,EAC3BC,WAAW,EACXC,2BAA2B,EAC3BC,mBAAmB,EACnBV,MAAM,EACNW,GAAG,EACHC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EAClBC,aAAa,EACU;QA0HNpB,gBAKSA,iBAY0BA;IA1IpD,OAAO;QACL,+CAA+C;QAC/CqB,mBAAmB;QAEnB,GAAGC,OAAOC,IAAI,CAACC,QAAQC,GAAG,EAAEC,MAAM,CAChC,CAACC,MAAiC1B;YAChC,IAAIA,IAAI2B,UAAU,CAAC,iBAAiB;gBAClCD,IAAI,CAAC,CAAC,YAAY,EAAE1B,IAAI,CAAC,CAAC,GAAG4B,KAAKC,SAAS,CAACN,QAAQC,GAAG,CAACxB,IAAI;YAC9D;YACA,OAAO0B;QACT,GACA,CAAC,EACF;QACD,GAAGL,OAAOC,IAAI,CAACvB,OAAOyB,GAAG,EAAEC,MAAM,CAAC,CAACK,KAAK9B;YACtCF,qBAAqBC,QAAQC;YAE7B,OAAO;gBACL,GAAG8B,GAAG;gBACN,CAAC,CAAC,YAAY,EAAE9B,IAAI,CAAC,CAAC,EAAE4B,KAAKC,SAAS,CAAC9B,OAAOyB,GAAG,CAACxB,IAAI;YACxD;QACF,GAAG,CAAC,EAAE;QACN,GAAI,CAACe,eACD,CAAC,IACD;YACEgB,aAAaH,KAAKC,SAAS,CACzB;;;;aAIC,GACDN,QAAQC,GAAG,CAACQ,0BAA0B,IAAI;QAE9C,CAAC;QACL,qBAAqBJ,KAAKC,SAAS,CAACtB;QACpC,yBAAyBqB,KAAKC,SAAS,CAACtB;QACxC,6DAA6D;QAC7D,wBAAwBqB,KAAKC,SAAS,CAACnB,MAAM,gBAAgB;QAC7D,4BAA4BkB,KAAKC,SAAS,CACxCd,eAAe,SAASE,eAAe,WAAW;QAEpD,4BAA4BW,KAAKC,SAAS,CAAC;QAC3C,6CAA6CD,KAAKC,SAAS,CACzD9B,OAAOkC,YAAY,CAACC,oBAAoB;QAE1C,0BAA0BN,KAAKC,SAAS,CAAC9B,OAAOkC,YAAY,CAACE,GAAG,KAAK;QACrE,4CAA4CP,KAAKC,SAAS,CACxD9B,OAAOkC,YAAY,CAACG,4BAA4B;QAElD,kCAAkCR,KAAKC,SAAS,CAC9C9B,OAAOkC,YAAY,CAACI,YAAY,IAAI;QAEtC,6CACET,KAAKC,SAAS,CAACjB;QACjB,sCAAsCgB,KAAKC,SAAS,CAACV;QACrD,iDAAiDS,KAAKC,SAAS,CAC7DrB;QAEF,0CAA0CoB,KAAKC,SAAS,CACtDX,sBAAsB,EAAE;QAE1B,8CAA8CU,KAAKC,SAAS,CAC1D9B,OAAOkC,YAAY,CAACK,oBAAoB;QAE1C,mDAAmDV,KAAKC,SAAS,CAC/D9B,OAAOkC,YAAY,CAACM,kBAAkB;QAExC,6CAA6CX,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqB+B,YAAY;QAEnC,6CAA6CZ,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqBgC,aAAa;QAEpC,8CAA8Cb,KAAKC,SAAS,CAC1D9B,OAAOkC,YAAY,CAACS,qBAAqB;QAE3C,0CAA0Cd,KAAKC,SAAS,CACtD9B,OAAOkC,YAAY,CAACU,kBAAkB;QAExC,mCAAmCf,KAAKC,SAAS,CAAC9B,OAAO6C,WAAW;QACpE,mBAAmBhB,KAAKC,SAAS,CAACf;QAClC,gCAAgCc,KAAKC,SAAS,CAC5CN,QAAQC,GAAG,CAACqB,gBAAgB;QAE9B,2FAA2F;QAC3F,GAAInC,OAAQI,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+Ba,KAAKC,SAAS,CAAClB;QAChD,IACA,CAAC,CAAC;QACN,qCAAqCiB,KAAKC,SAAS,CAAC9B,OAAO+C,aAAa;QACxE,sCAAsClB,KAAKC,SAAS,CAClD9B,OAAOgD,aAAa,CAACC,aAAa;QAEpC,+CAA+CpB,KAAKC,SAAS,CAC3D9B,OAAOgD,aAAa,CAACE,qBAAqB;QAE5C,kCAAkCrB,KAAKC,SAAS,CAC9C9B,OAAOmD,eAAe,KAAK,OAAO,QAAQnD,OAAOmD,eAAe;QAElE,sCAAsCtB,KAAKC,SAAS,CAClD,6EAA6E;QAC7E9B,OAAOmD,eAAe,KAAK,OAAO,OAAOnD,OAAOmD,eAAe;QAEjE,qCAAqCtB,KAAKC,SAAS,CACjD,CAACnB,OAAOX,OAAOoD,aAAa;QAE9B,mCAAmCvB,KAAKC,SAAS,CAC/C9B,OAAOkC,YAAY,CAACmB,WAAW,IAAI,CAAC1C;QAEtC,qCAAqCkB,KAAKC,SAAS,CACjD9B,OAAOkC,YAAY,CAACoB,iBAAiB,IAAI,CAAC3C;QAE5C,yCAAyCkB,KAAKC,SAAS,CACrD9B,OAAOkC,YAAY,CAACqB,iBAAiB;QAEvC,iCAAiC1B,KAAKC,SAAS,CAAC;YAC9C0B,aAAaxD,OAAOyD,MAAM,CAACD,WAAW;YACtCE,YAAY1D,OAAOyD,MAAM,CAACC,UAAU;YACpCC,MAAM3D,OAAOyD,MAAM,CAACE,IAAI;YACxBC,QAAQ5D,OAAOyD,MAAM,CAACG,MAAM;YAC5BC,qBAAqB7D,OAAOyD,MAAM,CAACI,mBAAmB;YACtDC,WAAW,EAAE9D,2BAAAA,iBAAAA,OAAQyD,MAAM,qBAAdzD,eAAgB8D,WAAW;YACxC,GAAInD,MACA;gBACE,gEAAgE;gBAChEoD,SAAS/D,OAAOyD,MAAM,CAACM,OAAO;gBAC9BC,cAAc,GAAEhE,kBAAAA,OAAOyD,MAAM,qBAAbzD,gBAAegE,cAAc;gBAC7CC,QAAQjE,OAAOiE,MAAM;YACvB,IACA,CAAC,CAAC;QACR;QACA,sCAAsCpC,KAAKC,SAAS,CAAC9B,OAAOkE,QAAQ;QACpE,uCAAuCrC,KAAKC,SAAS,CACnD9B,OAAOkC,YAAY,CAACiC,cAAc;QAEpC,mCAAmCtC,KAAKC,SAAS,CAAChB;QAClD,oCAAoCe,KAAKC,SAAS,CAAC9B,OAAOiE,MAAM;QAChE,mCAAmCpC,KAAKC,SAAS,CAAC,CAAC,CAAC9B,OAAOoE,IAAI;QAC/D,mCAAmCvC,KAAKC,SAAS,EAAC9B,eAAAA,OAAOoE,IAAI,qBAAXpE,aAAa+D,OAAO;QACtE,mCAAmClC,KAAKC,SAAS,CAAC9B,OAAOqE,WAAW;QACpE,kDAAkDxC,KAAKC,SAAS,CAC9D9B,OAAOsE,0BAA0B;QAEnC,0DAA0DzC,KAAKC,SAAS,CACtE9B,OAAOkC,YAAY,CAACqC,iCAAiC;QAEvD,4CAA4C1C,KAAKC,SAAS,CACxD9B,OAAOwE,yBAAyB;QAElC,iDAAiD3C,KAAKC,SAAS,CAC7D9B,OAAOkC,YAAY,CAACuC,oBAAoB,IACtCzE,OAAOkC,YAAY,CAACuC,oBAAoB,CAACC,MAAM,GAAG;QAEtD,6CAA6C7C,KAAKC,SAAS,CACzD9B,OAAOkC,YAAY,CAACuC,oBAAoB;QAE1C,mCAAmC5C,KAAKC,SAAS,CAAC9B,OAAO2E,WAAW;QACpE,GAAI1D,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiBY,KAAKC,SAAS,CAAC;QAClC,IACA8C,SAAS;QACb,GAAI3D,0BACA;YACE,yCAAyCY,KAAKC,SAAS,CACrDhC,uBAAuBE;QAE3B,IACA4E,SAAS;IACf;AACF;AAEA,OAAO,SAASC,mBAAmBC,OAA+B;IAChE,OAAO,IAAIjF,QAAQkF,YAAY,CAACxE,aAAauE;AAC/C"}