{"version": 3, "sources": ["../../../src/server/app-render/make-get-server-inserted-html.tsx"], "names": ["React", "isNotFoundError", "getURLFromRedirectError", "isRedirectError", "getRedirectStatusCodeFromError", "renderToReadableStream", "streamToString", "RedirectStatusCode", "makeGetServerInsertedHTML", "polyfills", "renderServerInsertedHTML", "hasPostponed", "flushedErrorMetaTagsUntilIndex", "polyfillsFlushed", "getServerInsertedHTML", "serverCapturedErrors", "errorMetaTags", "length", "error", "push", "meta", "name", "content", "key", "digest", "process", "env", "NODE_ENV", "redirectUrl", "statusCode", "isPermanent", "PermanentRedirect", "httpEquiv", "stream", "map", "polyfill", "script", "src", "allReady"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,eAAe,QAAQ,oCAAmC;AACnE,SACEC,uBAAuB,EACvBC,eAAe,EACfC,8BAA8B,QACzB,mCAAkC;AACzC,SAASC,sBAAsB,QAAQ,wBAAuB;AAC9D,SAASC,cAAc,QAAQ,0CAAyC;AACxE,SAASC,kBAAkB,QAAQ,+CAA8C;AAEjF,OAAO,SAASC,0BAA0B,EACxCC,SAAS,EACTC,wBAAwB,EACxBC,YAAY,EAKb;IACC,IAAIC,iCAAiC;IACrC,2EAA2E;IAC3E,IAAIC,mBAAmBF;IAEvB,OAAO,eAAeG,sBAAsBC,oBAA6B;QACvE,kEAAkE;QAClE,WAAW;QACX,MAAMC,gBAAgB,EAAE;QACxB,MAAOJ,iCAAiCG,qBAAqBE,MAAM,CAAE;YACnE,MAAMC,QAAQH,oBAAoB,CAACH,+BAA+B;YAClEA;YAEA,IAAIX,gBAAgBiB,QAAQ;gBAC1BF,cAAcG,IAAI,eAChB,oBAACC;oBAAKC,MAAK;oBAASC,SAAQ;oBAAUC,KAAKL,MAAMM,MAAM;oBACvDC,QAAQC,GAAG,CAACC,QAAQ,KAAK,8BACvB,oBAACP;oBAAKC,MAAK;oBAAaC,SAAQ;oBAAYC,KAAI;qBAC9C;YAER,OAAO,IAAIpB,gBAAgBe,QAAQ;gBACjC,MAAMU,cAAc1B,wBAAwBgB;gBAC5C,MAAMW,aAAazB,+BAA+Bc;gBAClD,MAAMY,cACJD,eAAetB,mBAAmBwB,iBAAiB,GAAG,OAAO;gBAC/D,IAAIH,aAAa;oBACfZ,cAAcG,IAAI,eAChB,oBAACC;wBACCY,WAAU;wBACVV,SAAS,CAAC,EAAEQ,cAAc,IAAI,EAAE,KAAK,EAAEF,YAAY,CAAC;wBACpDL,KAAKL,MAAMM,MAAM;;gBAGvB;YACF;QACF;QAEA,MAAMS,SAAS,MAAM5B,qCACnB,0CAEG,CAACQ,qBACAJ,6BAAAA,UAAWyB,GAAG,CAAC,CAACC;YACd,qBAAO,oBAACC;gBAAOb,KAAKY,SAASE,GAAG;gBAAG,GAAGF,QAAQ;;QAChD,KACDzB,4BACAM;QAIL,6DAA6D;QAC7D,IAAI,CAACH,kBAAkBA,mBAAmB;QAE1C,mCAAmC;QACnC,MAAMoB,OAAOK,QAAQ;QAErB,OAAOhC,eAAe2B;IACxB;AACF"}