/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', '<PERSON><PERSON>wal', system-ui, sans-serif;
    background: linear-gradient(135deg, #e0e5ec 0%, #f5f7fa 100%);
    min-height: 100vh;
    color: #2d3748;
    direction: rtl;
    overflow-x: hidden;
}

/* Container */
.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    gap: 30px;
}

/* Logo Section */
.logo-section {
    text-align: center;
    animation: fadeInDown 0.8s ease-out;
}

.logo-container {
    width: 80px;
    height: 80px;
    background: #e0e5ec;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 12px 12px 24px #a3b1c6, -12px -12px 24px #ffffff;
    transition: all 0.3s ease;
}

.logo-container:hover {
    box-shadow: 8px 8px 16px #a3b1c6, -8px -8px 16px #ffffff;
    transform: translateY(-2px);
}

.logo-icon {
    width: 40px;
    height: 40px;
    color: #3b82f6;
}

.company-name {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 8px;
    text-shadow: 2px 2px 4px rgba(163, 177, 198, 0.3);
}

.company-subtitle {
    font-size: 1.1rem;
    color: #4a5568;
    font-weight: 400;
}

/* Login Card */
.login-card {
    background: #e0e5ec;
    padding: 40px;
    border-radius: 25px;
    box-shadow: 15px 15px 30px #a3b1c6, -15px -15px 30px #ffffff;
    width: 100%;
    max-width: 420px;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.login-title {
    font-size: 2rem;
    font-weight: 600;
    text-align: center;
    color: #1a202c;
    margin-bottom: 30px;
}

/* Error Message */
.error-message {
    background: linear-gradient(135deg, #fed7d7, #feb2b2);
    color: #c53030;
    padding: 12px 16px;
    border-radius: 12px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid #fc8181;
    box-shadow: 4px 4px 8px rgba(252, 129, 129, 0.2), -4px -4px 8px rgba(255, 255, 255, 0.8);
    animation: shake 0.5s ease-in-out;
}

.error-icon {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
}

/* Form Styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-label {
    font-size: 0.95rem;
    font-weight: 500;
    color: #2d3748;
    margin-right: 4px;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-field {
    width: 100%;
    padding: 16px 20px 16px 50px;
    background: #e0e5ec;
    border: none;
    border-radius: 15px;
    font-size: 1rem;
    color: #2d3748;
    box-shadow: inset 8px 8px 16px #a3b1c6, inset -8px -8px 16px #ffffff;
    transition: all 0.3s ease;
    font-family: inherit;
}

.input-field:focus {
    outline: none;
    box-shadow: inset 6px 6px 12px #a3b1c6, inset -6px -6px 12px #ffffff;
    transform: scale(1.02);
}

.input-field::placeholder {
    color: #a0aec0;
}

.input-icon {
    position: absolute;
    right: 16px;
    width: 20px;
    height: 20px;
    color: #718096;
    z-index: 1;
}

.password-toggle {
    position: absolute;
    left: 16px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.password-toggle:hover {
    background: rgba(163, 177, 198, 0.1);
}

.eye-icon {
    width: 18px;
    height: 18px;
    color: #718096;
}

/* Checkbox */
.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkbox {
    width: 18px;
    height: 18px;
    background: #e0e5ec;
    border: none;
    border-radius: 4px;
    box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
    cursor: pointer;
    appearance: none;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox:checked {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    box-shadow: 2px 2px 4px #a3b1c6, -2px -2px 4px #ffffff;
}

.checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-label {
    font-size: 0.9rem;
    color: #4a5568;
    cursor: pointer;
    user-select: none;
}

/* Login Button */
.login-button {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    border: none;
    padding: 16px 24px;
    border-radius: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 8px 8px 16px #a3b1c6, -8px -8px 16px #ffffff;
    transition: all 0.3s ease;
    font-family: inherit;
    position: relative;
    overflow: hidden;
}

.login-button:hover:not(:disabled) {
    box-shadow: 6px 6px 12px #a3b1c6, -6px -6px 12px #ffffff;
    transform: translateY(-2px);
}

.login-button:active:not(:disabled) {
    box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
    transform: translateY(0);
}

.login-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Demo Info */
.demo-info {
    margin-top: 25px;
    padding: 16px;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 12px;
    border: 1px solid #93c5fd;
    box-shadow: 4px 4px 8px rgba(147, 197, 253, 0.2), -4px -4px 8px rgba(255, 255, 255, 0.8);
}

.demo-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 8px;
}

.demo-text {
    font-size: 0.8rem;
    color: #1d4ed8;
    margin-bottom: 4px;
}

.demo-text:last-child {
    margin-bottom: 0;
}

/* Footer */
.footer {
    text-align: center;
    color: #718096;
    font-size: 0.9rem;
    animation: fadeIn 1s ease-out 0.4s both;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        gap: 20px;
    }
    
    .company-name {
        font-size: 2rem;
    }
    
    .login-card {
        padding: 30px 25px;
    }
    
    .login-title {
        font-size: 1.7rem;
    }
}

@media (max-width: 480px) {
    .company-name {
        font-size: 1.7rem;
    }
    
    .login-card {
        padding: 25px 20px;
    }
    
    .login-title {
        font-size: 1.5rem;
    }
    
    .input-field {
        padding: 14px 18px 14px 45px;
    }
}
