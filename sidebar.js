// Sidebar Navigation JavaScript

class SidebarManager {
    constructor() {
        this.sidebar = null;
        this.mainWrapper = null;
        this.mobileOverlay = null;
        this.mobileMenuBtn = null;
        this.sidebarToggle = null;
        this.isCollapsed = false;
        this.isMobile = window.innerWidth <= 768;
        
        this.init();
    }
    
    init() {
        this.createSidebar();
        this.addEventListeners();
        this.setActiveNavItem();
        
        // Load collapsed state from localStorage
        const savedState = localStorage.getItem('sidebarCollapsed');
        if (savedState === 'true') {
            this.toggleSidebar();
        }
    }
    
    createSidebar() {
        // Create sidebar HTML
        const sidebarHTML = `
            <div class="sidebar" id="sidebar">
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="15,18 9,12 15,6"></polyline>
                    </svg>
                </button>
                
                <div class="sidebar-header">
                    <div class="sidebar-logo">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2"/>
                            <path d="M15 18H9"/>
                            <path d="M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14"/>
                            <circle cx="17" cy="18" r="2"/>
                            <circle cx="7" cy="18" r="2"/>
                        </svg>
                    </div>
                    <div class="sidebar-title">
                        <h1>شركة التوصيل السريع</h1>
                        <p>نظام الإدارة</p>
                    </div>
                    <button class="logout-sidebar" id="logout-sidebar" title="تسجيل الخروج">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                            <polyline points="16,17 21,12 16,7"/>
                            <line x1="21" y1="12" x2="9" y2="12"/>
                        </svg>
                    </button>
                </div>
                
                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <div class="nav-section-title">الإدارة الرئيسية</div>
                        <a href="dashboard.html" class="nav-item" data-page="dashboard" data-shortcut="Ctrl+1">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                <polyline points="9,22 9,12 15,12 15,22"/>
                            </svg>
                            <span class="nav-text">لوحة التحكم</span>
                        </a>
                        
                        <a href="orders.html" class="nav-item" data-page="orders" data-shortcut="Ctrl+2">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4"/>
                                <polyline points="9,11 12,14 15,11"/>
                                <line x1="12" y1="2" x2="12" y2="14"/>
                            </svg>
                            <span class="nav-text">إدارة الطلبات</span>
                            <span class="nav-badge" id="orders-badge">5</span>
                        </a>
                        
                        <a href="drivers.html" class="nav-item" data-page="drivers" data-shortcut="Ctrl+3">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                <circle cx="12" cy="7" r="4"/>
                            </svg>
                            <span class="nav-text">إدارة المندوبين</span>
                        </a>

                        <a href="customers.html" class="nav-item" data-page="customers" data-shortcut="Ctrl+4">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                            <span class="nav-text">إدارة العملاء</span>
                        </a>
                    </div>
                    
                    <div class="nav-section">
                        <div class="nav-section-title">العمليات</div>
                        <a href="assignments.html" class="nav-item" data-page="assignments" data-shortcut="Ctrl+5">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                                <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
                                <path d="M12 11l2 2 4-4"/>
                            </svg>
                            <span class="nav-text">إسناد الطلبات</span>
                            <span class="nav-badge" id="assignments-badge">3</span>
                        </a>

                        <a href="center-payments.html" class="nav-item" data-page="center-payments" data-shortcut="Ctrl+6">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
                                <line x1="1" y1="10" x2="23" y2="10"/>
                            </svg>
                            <span class="nav-text">تسديد حسابات المركز</span>
                        </a>

                        <a href="center-returns.html" class="nav-item" data-page="center-returns" data-shortcut="Ctrl+7">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="1,4 1,10 7,10"/>
                                <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                            </svg>
                            <span class="nav-text">تصفية راجع المركز</span>
                            <span class="nav-badge" id="returns-badge">2</span>
                        </a>
                    </div>
                    
                    <div class="nav-section">
                        <div class="nav-section-title">المحاسبة</div>
                        <a href="driver-accounts.html" class="nav-item" data-page="driver-accounts" data-shortcut="Ctrl+8">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="12" y1="1" x2="12" y2="23"/>
                                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                            </svg>
                            <span class="nav-text">حسابات المندوب</span>
                        </a>

                        <a href="driver-returns.html" class="nav-item" data-page="driver-returns" data-shortcut="Ctrl+9">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M16 3h5v5"/>
                                <path d="M8 21H3v-5"/>
                                <path d="M21 8l-7 7-4-4-6 6"/>
                            </svg>
                            <span class="nav-text">استلام الراجع من المندوب</span>
                        </a>

                        <a href="driver-settlements.html" class="nav-item" data-page="driver-settlements" data-shortcut="Shift+Ctrl+9">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                                <line x1="16" y1="13" x2="8" y2="13"/>
                                <line x1="16" y1="17" x2="8" y2="17"/>
                                <polyline points="10,9 9,9 8,9"/>
                            </svg>
                            <span class="nav-text">تصفية حسابات المندوب</span>
                        </a>

                        <a href="reports.html" class="nav-item" data-page="reports" data-shortcut="Ctrl+P">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21.21 15.89A10 10 0 1 1 8 2.83"/>
                                <path d="M22 12A10 10 0 0 0 12 2v10z"/>
                            </svg>
                            <span class="nav-text">التقارير المالية</span>
                        </a>
                    </div>

                    <div class="nav-section">
                        <div class="nav-section-title">النظام</div>
                        <a href="settings.html" class="nav-item" data-page="settings" data-shortcut="Ctrl+0">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="3"/>
                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                            </svg>
                            <span class="nav-text">الإعدادات</span>
                        </a>
                    </div>
                </nav>
            </div>
            
            <div class="mobile-overlay" id="mobile-overlay"></div>
            <button class="mobile-menu-btn" id="mobile-menu-btn">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="3" y1="6" x2="21" y2="6"/>
                    <line x1="3" y1="12" x2="21" y2="12"/>
                    <line x1="3" y1="18" x2="21" y2="18"/>
                </svg>
            </button>
        `;
        
        // Insert sidebar into body
        document.body.insertAdjacentHTML('afterbegin', sidebarHTML);
        
        // Wrap existing content
        const existingContent = document.body.innerHTML.replace(sidebarHTML, '');
        document.body.innerHTML = sidebarHTML + `<div class="main-wrapper">${existingContent}</div>`;
        
        // Get references
        this.sidebar = document.getElementById('sidebar');
        this.mainWrapper = document.querySelector('.main-wrapper');
        this.mobileOverlay = document.getElementById('mobile-overlay');
        this.mobileMenuBtn = document.getElementById('mobile-menu-btn');
        this.sidebarToggle = document.getElementById('sidebar-toggle');
    }
    
    addEventListeners() {
        // Sidebar toggle
        this.sidebarToggle?.addEventListener('click', () => this.toggleSidebar());
        
        // Mobile menu
        this.mobileMenuBtn?.addEventListener('click', () => this.toggleMobileSidebar());
        this.mobileOverlay?.addEventListener('click', () => this.closeMobileSidebar());
        
        // Logout
        document.getElementById('logout-sidebar')?.addEventListener('click', () => this.handleLogout());
        
        // Window resize
        window.addEventListener('resize', () => this.handleResize());
        
        // Navigation items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => this.handleNavigation(e));
        });

        // Global keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleGlobalShortcuts(e));
    }
    
    toggleSidebar() {
        if (this.isMobile) return;
        
        this.isCollapsed = !this.isCollapsed;
        this.sidebar?.classList.toggle('collapsed', this.isCollapsed);
        
        // Save state
        localStorage.setItem('sidebarCollapsed', this.isCollapsed.toString());
        
        // Trigger resize event for charts/tables
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
        }, 300);
    }
    
    toggleMobileSidebar() {
        this.sidebar?.classList.toggle('open');
        this.mobileOverlay?.classList.toggle('active');
        document.body.style.overflow = this.sidebar?.classList.contains('open') ? 'hidden' : '';
    }
    
    closeMobileSidebar() {
        this.sidebar?.classList.remove('open');
        this.mobileOverlay?.classList.remove('active');
        document.body.style.overflow = '';
    }
    
    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;
        
        if (wasMobile !== this.isMobile) {
            if (!this.isMobile) {
                this.closeMobileSidebar();
                this.sidebar?.classList.toggle('collapsed', this.isCollapsed);
            } else {
                this.sidebar?.classList.remove('collapsed');
            }
        }
    }
    
    setActiveNavItem() {
        const currentPage = this.getCurrentPage();
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.page === currentPage) {
                item.classList.add('active');
            }
        });
    }
    
    getCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop().split('.')[0];
        
        // Map filenames to page identifiers
        const pageMap = {
            'dashboard': 'dashboard',
            'orders': 'orders',
            'drivers': 'drivers',
            'customers': 'customers',
            'assignments': 'assignments',
            'center-payments': 'center-payments',
            'center-returns': 'center-returns',
            'driver-accounts': 'driver-accounts',
            'driver-returns': 'driver-returns',
            'driver-settlements': 'driver-settlements',
            'reports': 'reports',
            'settings': 'settings'
        };
        
        return pageMap[filename] || 'dashboard';
    }
    
    handleNavigation(e) {
        // Close mobile sidebar on navigation
        if (this.isMobile) {
            this.closeMobileSidebar();
        }
        
        // Add loading state
        const navItem = e.currentTarget;
        navItem.style.opacity = '0.6';
        
        // Reset after navigation
        setTimeout(() => {
            navItem.style.opacity = '1';
        }, 500);
    }
    
    // User info section removed
    
    updateBadge(badgeId, count) {
        const badge = document.getElementById(badgeId);
        if (badge) {
            if (count > 0) {
                badge.textContent = count;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
    }
    
    handleLogout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            localStorage.removeItem('isLoggedIn');
            window.location.href = 'index.html';
        }
    }

    handleGlobalShortcuts(e) {
        // Don't interfere with page-specific shortcuts
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
            return;
        }

        // Only handle shortcuts that aren't handled by specific pages
        const currentPage = this.getCurrentPage();

        // Global navigation shortcuts (work on all pages)
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'h': // Home/Dashboard
                    e.preventDefault();
                    window.location.href = 'dashboard.html';
                    break;
                case '1':
                    e.preventDefault();
                    window.location.href = 'dashboard.html';
                    break;
                case '2':
                    e.preventDefault();
                    window.location.href = 'orders.html';
                    break;
                case '3':
                    e.preventDefault();
                    window.location.href = 'drivers.html';
                    break;
                case '4':
                    e.preventDefault();
                    window.location.href = 'customers.html';
                    break;
                case '5':
                    e.preventDefault();
                    window.location.href = 'assignments.html';
                    break;
                case '6':
                    e.preventDefault();
                    window.location.href = 'center-payments.html';
                    break;
                case '7':
                    e.preventDefault();
                    window.location.href = 'center-returns.html';
                    break;
                case '8':
                    e.preventDefault();
                    window.location.href = 'driver-accounts.html';
                    break;
                case '9':
                    e.preventDefault();
                    window.location.href = 'driver-returns.html';
                    break;
                case '0':
                    e.preventDefault();
                    window.location.href = 'settings.html';
                    break;
            }
        }

        // Sidebar toggle with Ctrl+Shift+S
        if (e.ctrlKey && e.shiftKey && e.key === 'S') {
            e.preventDefault();
            this.toggleSidebar();
        }

        // Quick help with ?
        if (e.key === '?' && !e.ctrlKey && !e.altKey && !e.shiftKey) {
            e.preventDefault();
            this.showGlobalHelp();
        }
    }

    showGlobalHelp() {
        const helpModal = document.createElement('div');
        helpModal.className = 'modal';
        helpModal.innerHTML = `
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>🚀 الاختصارات العامة للنظام</h3>
                    <button class="close-modal" onclick="this.closest('.modal').remove()">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"/>
                            <line x1="6" y1="6" x2="18" y2="18"/>
                        </svg>
                    </button>
                </div>
                <div class="modal-body">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div class="shortcuts-section">
                            <h4>🔢 التنقل السريع بالأرقام</h4>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>1</kbd>
                                <span>لوحة التحكم</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>2</kbd>
                                <span>إدارة الطلبات</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>3</kbd>
                                <span>إدارة المندوبين</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>4</kbd>
                                <span>إدارة العملاء</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>5</kbd>
                                <span>إسناد الطلبات</span>
                            </div>
                        </div>

                        <div class="shortcuts-section">
                            <h4>💰 المحاسبة والتقارير</h4>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>6</kbd>
                                <span>تسديد حسابات المركز</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>7</kbd>
                                <span>تصفية راجع المركز</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>8</kbd>
                                <span>حسابات المندوب</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>9</kbd>
                                <span>استلام الراجع من المندوب</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>P</kbd>
                                <span>التقارير المالية</span>
                            </div>
                        </div>

                        <div class="shortcuts-section">
                            <h4>⚙️ النظام والإعدادات</h4>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>0</kbd>
                                <span>الإعدادات</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>H</kbd>
                                <span>الصفحة الرئيسية</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>S</kbd>
                                <span>طي/توسيع الشريط الجانبي</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>L</kbd>
                                <span>تسجيل الخروج</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>?</kbd>
                                <span>عرض هذه المساعدة</span>
                            </div>
                        </div>

                        <div class="shortcuts-section">
                            <h4>📄 اختصارات خاصة بالصفحات</h4>
                            <div class="shortcut-item">
                                <kbd>F1</kbd>
                                <span>مساعدة الصفحة الحالية</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>K</kbd>
                                <span>البحث (في الصفحات المدعومة)</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>R</kbd>
                                <span>تحديث البيانات</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>N</kbd>
                                <span>إضافة جديد (حسب الصفحة)</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Esc</kbd>
                                <span>إغلاق النوافذ المنبثقة</span>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 20px; padding: 15px; background: rgba(16, 185, 129, 0.1); border-radius: 10px;">
                        <p style="margin: 0; color: #047857; font-weight: 500;">
                            🎯 <strong>نصائح للاستخدام الأمثل:</strong><br>
                            • استخدم <kbd>Ctrl + الرقم</kbd> للانتقال السريع بين الصفحات<br>
                            • اضغط <kbd>?</kbd> في أي وقت لعرض هذه المساعدة<br>
                            • استخدم <kbd>F1</kbd> للحصول على مساعدة خاصة بالصفحة الحالية
                        </p>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(helpModal);

        // Close on background click
        helpModal.addEventListener('click', (e) => {
            if (e.target === helpModal) {
                helpModal.remove();
            }
        });

        // Add styles if not already added
        this.addHelpStyles();
    }

    addHelpStyles() {
        if (!document.getElementById('global-shortcuts-styles')) {
            const styles = document.createElement('style');
            styles.id = 'global-shortcuts-styles';
            styles.textContent = `
                .shortcuts-section {
                    background: rgba(163, 177, 198, 0.1);
                    padding: 20px;
                    border-radius: 15px;
                }

                .shortcuts-section h4 {
                    margin: 0 0 15px 0;
                    color: #1a202c;
                    font-size: 1.1rem;
                    font-weight: 600;
                }

                .shortcut-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 0;
                    border-bottom: 1px solid rgba(163, 177, 198, 0.2);
                }

                .shortcut-item:last-child {
                    border-bottom: none;
                }

                .shortcut-item kbd {
                    background: #e0e5ec;
                    border: 1px solid #a3b1c6;
                    border-radius: 6px;
                    padding: 4px 8px;
                    font-family: 'Cairo', sans-serif;
                    font-size: 0.8rem;
                    font-weight: 600;
                    color: #374151;
                    box-shadow: 0 2px 4px rgba(163, 177, 198, 0.3);
                    margin: 0 2px;
                }

                .shortcut-item span {
                    color: #4a5568;
                    font-size: 0.9rem;
                }
            `;
            document.head.appendChild(styles);
        }
    }
}

// Initialize sidebar when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Check if user is logged in
    if (!localStorage.getItem('isLoggedIn')) {
        window.location.href = 'index.html';
        return;
    }
    
    window.sidebarManager = new SidebarManager();
});

// Export for use in other scripts
window.SidebarManager = SidebarManager;
