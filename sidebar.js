// Sidebar Navigation JavaScript

class SidebarManager {
    constructor() {
        this.sidebar = null;
        this.mainWrapper = null;
        this.mobileOverlay = null;
        this.mobileMenuBtn = null;
        this.sidebarToggle = null;
        this.isCollapsed = false;
        this.isMobile = window.innerWidth <= 768;
        
        this.init();
    }
    
    init() {
        this.createSidebar();
        this.addEventListeners();
        this.setActiveNavItem();
        
        // Load collapsed state from localStorage
        const savedState = localStorage.getItem('sidebarCollapsed');
        if (savedState === 'true') {
            this.toggleSidebar();
        }
    }
    
    createSidebar() {
        // Create sidebar HTML
        const sidebarHTML = `
            <div class="sidebar" id="sidebar">
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="15,18 9,12 15,6"></polyline>
                    </svg>
                </button>
                
                <div class="sidebar-header">
                    <div class="sidebar-logo">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2"/>
                            <path d="M15 18H9"/>
                            <path d="M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14"/>
                            <circle cx="17" cy="18" r="2"/>
                            <circle cx="7" cy="18" r="2"/>
                        </svg>
                    </div>
                    <div class="sidebar-title">
                        <h1>شركة التوصيل السريع</h1>
                        <p>نظام الإدارة</p>
                    </div>
                    <button class="logout-sidebar" id="logout-sidebar" title="تسجيل الخروج">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                            <polyline points="16,17 21,12 16,7"/>
                            <line x1="21" y1="12" x2="9" y2="12"/>
                        </svg>
                    </button>
                </div>
                
                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <div class="nav-section-title">الإدارة الرئيسية</div>
                        <a href="dashboard.html" class="nav-item" data-page="dashboard">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                <polyline points="9,22 9,12 15,12 15,22"/>
                            </svg>
                            <span class="nav-text">لوحة التحكم</span>
                        </a>
                        
                        <a href="orders.html" class="nav-item" data-page="orders">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4"/>
                                <polyline points="9,11 12,14 15,11"/>
                                <line x1="12" y1="2" x2="12" y2="14"/>
                            </svg>
                            <span class="nav-text">إدارة الطلبات</span>
                            <span class="nav-badge" id="orders-badge">5</span>
                        </a>
                        
                        <a href="drivers.html" class="nav-item" data-page="drivers">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                <circle cx="12" cy="7" r="4"/>
                            </svg>
                            <span class="nav-text">إدارة المندوبين</span>
                        </a>
                        
                        <a href="customers.html" class="nav-item" data-page="customers">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                            <span class="nav-text">إدارة العملاء</span>
                        </a>
                    </div>
                    
                    <div class="nav-section">
                        <div class="nav-section-title">العمليات</div>
                        <a href="assignments.html" class="nav-item" data-page="assignments">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                                <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
                                <path d="M12 11l2 2 4-4"/>
                            </svg>
                            <span class="nav-text">إسناد الطلبات</span>
                            <span class="nav-badge" id="assignments-badge">3</span>
                        </a>
                        
                        <a href="center-payments.html" class="nav-item" data-page="center-payments">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
                                <line x1="1" y1="10" x2="23" y2="10"/>
                            </svg>
                            <span class="nav-text">تسديد حسابات المركز</span>
                        </a>
                        
                        <a href="center-returns.html" class="nav-item" data-page="center-returns">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="1,4 1,10 7,10"/>
                                <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                            </svg>
                            <span class="nav-text">تصفية راجع المركز</span>
                            <span class="nav-badge" id="returns-badge">2</span>
                        </a>
                    </div>
                    
                    <div class="nav-section">
                        <div class="nav-section-title">المحاسبة</div>
                        <a href="driver-accounts.html" class="nav-item" data-page="driver-accounts">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="12" y1="1" x2="12" y2="23"/>
                                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                            </svg>
                            <span class="nav-text">حسابات المندوب</span>
                        </a>
                        
                        <a href="reports.html" class="nav-item" data-page="reports">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21.21 15.89A10 10 0 1 1 8 2.83"/>
                                <path d="M22 12A10 10 0 0 0 12 2v10z"/>
                            </svg>
                            <span class="nav-text">التقارير المالية</span>
                        </a>
                    </div>
                </nav>
            </div>
            
            <div class="mobile-overlay" id="mobile-overlay"></div>
            <button class="mobile-menu-btn" id="mobile-menu-btn">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="3" y1="6" x2="21" y2="6"/>
                    <line x1="3" y1="12" x2="21" y2="12"/>
                    <line x1="3" y1="18" x2="21" y2="18"/>
                </svg>
            </button>
        `;
        
        // Insert sidebar into body
        document.body.insertAdjacentHTML('afterbegin', sidebarHTML);
        
        // Wrap existing content
        const existingContent = document.body.innerHTML.replace(sidebarHTML, '');
        document.body.innerHTML = sidebarHTML + `<div class="main-wrapper">${existingContent}</div>`;
        
        // Get references
        this.sidebar = document.getElementById('sidebar');
        this.mainWrapper = document.querySelector('.main-wrapper');
        this.mobileOverlay = document.getElementById('mobile-overlay');
        this.mobileMenuBtn = document.getElementById('mobile-menu-btn');
        this.sidebarToggle = document.getElementById('sidebar-toggle');
    }
    
    addEventListeners() {
        // Sidebar toggle
        this.sidebarToggle?.addEventListener('click', () => this.toggleSidebar());
        
        // Mobile menu
        this.mobileMenuBtn?.addEventListener('click', () => this.toggleMobileSidebar());
        this.mobileOverlay?.addEventListener('click', () => this.closeMobileSidebar());
        
        // Logout
        document.getElementById('logout-sidebar')?.addEventListener('click', () => this.handleLogout());
        
        // Window resize
        window.addEventListener('resize', () => this.handleResize());
        
        // Navigation items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => this.handleNavigation(e));
        });
    }
    
    toggleSidebar() {
        if (this.isMobile) return;
        
        this.isCollapsed = !this.isCollapsed;
        this.sidebar?.classList.toggle('collapsed', this.isCollapsed);
        
        // Save state
        localStorage.setItem('sidebarCollapsed', this.isCollapsed.toString());
        
        // Trigger resize event for charts/tables
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
        }, 300);
    }
    
    toggleMobileSidebar() {
        this.sidebar?.classList.toggle('open');
        this.mobileOverlay?.classList.toggle('active');
        document.body.style.overflow = this.sidebar?.classList.contains('open') ? 'hidden' : '';
    }
    
    closeMobileSidebar() {
        this.sidebar?.classList.remove('open');
        this.mobileOverlay?.classList.remove('active');
        document.body.style.overflow = '';
    }
    
    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;
        
        if (wasMobile !== this.isMobile) {
            if (!this.isMobile) {
                this.closeMobileSidebar();
                this.sidebar?.classList.toggle('collapsed', this.isCollapsed);
            } else {
                this.sidebar?.classList.remove('collapsed');
            }
        }
    }
    
    setActiveNavItem() {
        const currentPage = this.getCurrentPage();
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.page === currentPage) {
                item.classList.add('active');
            }
        });
    }
    
    getCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop().split('.')[0];
        
        // Map filenames to page identifiers
        const pageMap = {
            'dashboard': 'dashboard',
            'orders': 'orders',
            'drivers': 'drivers',
            'customers': 'customers',
            'assignments': 'assignments',
            'center-payments': 'center-payments',
            'center-returns': 'center-returns',
            'driver-accounts': 'driver-accounts',
            'reports': 'reports'
        };
        
        return pageMap[filename] || 'dashboard';
    }
    
    handleNavigation(e) {
        // Close mobile sidebar on navigation
        if (this.isMobile) {
            this.closeMobileSidebar();
        }
        
        // Add loading state
        const navItem = e.currentTarget;
        navItem.style.opacity = '0.6';
        
        // Reset after navigation
        setTimeout(() => {
            navItem.style.opacity = '1';
        }, 500);
    }
    
    // User info section removed
    
    updateBadge(badgeId, count) {
        const badge = document.getElementById(badgeId);
        if (badge) {
            if (count > 0) {
                badge.textContent = count;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
    }
    
    handleLogout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            localStorage.removeItem('isLoggedIn');
            window.location.href = 'index.html';
        }
    }
}

// Initialize sidebar when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Check if user is logged in
    if (!localStorage.getItem('isLoggedIn')) {
        window.location.href = 'index.html';
        return;
    }
    
    window.sidebarManager = new SidebarManager();
});

// Export for use in other scripts
window.SidebarManager = SidebarManager;
