<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استلام الراجع من المندوب - شركة التوصيل السريع</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="sidebar.css">
    <link rel="stylesheet" href="orders.css">
</head>
<body>
    <main class="main-content">
        <section class="action-bar">
            <div class="action-bar-content">
                <div class="page-title">
                    <h2>استلام الراجع من المندوب</h2>
                    <p>استلام وتسجيل الطلبات المرجعة من المندوبين</p>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="receive-returns-btn">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 3h5v5"/>
                            <path d="M8 21H3v-5"/>
                            <path d="M21 8l-7 7-4-4-6 6"/>
                        </svg>
                        استلام راجع
                    </button>
                    <button class="btn btn-success" id="bulk-receive-btn">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4"/>
                            <polyline points="9,11 12,14 15,11"/>
                            <line x1="12" y1="2" x2="12" y2="14"/>
                        </svg>
                        استلام متعدد
                    </button>
                </div>
            </div>
        </section>

        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card returned">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="1,4 1,10 7,10"/>
                            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">24</h3>
                        <p class="stat-label">طلبات راجعة مع المندوبين</p>
                    </div>
                </div>
                
                <div class="stat-card pending">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <line x1="12" y1="8" x2="12" y2="12"/>
                            <line x1="12" y1="16" x2="12.01" y2="16"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">8</h3>
                        <p class="stat-label">في انتظار الاستلام</p>
                    </div>
                </div>
                
                <div class="stat-card delivering">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="1" x2="12" y2="23"/>
                            <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number" data-is-currency="true" data-original-amount="8856000">8,856,000</h3>
                        <p class="stat-label" data-currency-label="true">قيمة الراجع (د.ع)</p>
                    </div>
                </div>
                
                <div class="stat-card delayed">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">6</h3>
                        <p class="stat-label">مندوبين لديهم راجع</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="orders-section">
            <div class="orders-container">
                <div class="table-header">
                    <div class="table-info">
                        <h3>الطلبات الراجعة مع المندوبين</h3>
                        <span class="orders-count">24 طلب راجع</span>
                    </div>
                    <div class="table-actions">
                        <button class="refresh-btn" onclick="refreshReturns()">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="23,4 23,10 17,10"/>
                                <polyline points="1,20 1,14 7,14"/>
                                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                            </svg>
                            تحديث
                        </button>
                    </div>
                </div>

                <div class="table-wrapper">
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all" class="checkbox">
                                </th>
                                <th>رقم الوصل</th>
                                <th>المندوب</th>
                                <th>المرسل إليه</th>
                                <th>المبلغ</th>
                                <th>سبب الإرجاع</th>
                                <th>تاريخ الإرجاع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <input type="checkbox" class="checkbox order-checkbox" data-order-id="DL003">
                                </td>
                                <td><span class="order-id">DL003</span></td>
                                <td>خالد عبدالله</td>
                                <td>محمد سعد</td>
                                <td class="amount" data-original-amount="421024">421,024 د.ع</td>
                                <td>رفض الاستلام</td>
                                <td>2024-06-28</td>
                                <td><span class="status-badge status-pending">مع المندوب</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn success" onclick="receiveReturn('DL003')">استلام</button>
                                        <button class="action-btn" onclick="showReturnDetails('DL003')">تفاصيل</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="checkbox" class="checkbox order-checkbox" data-order-id="DL007">
                                </td>
                                <td><span class="order-id">DL007</span></td>
                                <td>أحمد سالم</td>
                                <td>خالد عبدالرحمن</td>
                                <td class="amount" data-original-amount="164656">164,656 د.ع</td>
                                <td>عنوان خاطئ</td>
                                <td>2024-06-27</td>
                                <td><span class="status-badge status-pending">مع المندوب</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn success" onclick="receiveReturn('DL007')">استلام</button>
                                        <button class="action-btn" onclick="showReturnDetails('DL007')">تفاصيل</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="checkbox" class="checkbox order-checkbox" data-order-id="DL012">
                                </td>
                                <td><span class="order-id">DL012</span></td>
                                <td>سعد محمد</td>
                                <td>فاطمة أحمد</td>
                                <td class="amount" data-original-amount="590400">590,400 د.ع</td>
                                <td>لم يتم العثور على العميل</td>
                                <td>2024-06-29</td>
                                <td><span class="status-badge status-pending">مع المندوب</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn success" onclick="receiveReturn('DL012')">استلام</button>
                                        <button class="action-btn" onclick="showReturnDetails('DL012')">تفاصيل</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="checkbox" class="checkbox order-checkbox" data-order-id="DL015">
                                </td>
                                <td><span class="order-id">DL015</span></td>
                                <td>محمد علي</td>
                                <td>نورا خالد</td>
                                <td class="amount" data-original-amount="361128">361,128 د.ع</td>
                                <td>طلب إلغاء من العميل</td>
                                <td>2024-06-30</td>
                                <td><span class="status-badge status-returned">تم الاستلام</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn" onclick="showReturnDetails('DL015')">تفاصيل</button>
                                        <button class="action-btn primary" onclick="processReturn('DL015')">معالجة</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </main>

    <!-- Receive Return Modal -->
    <div id="receive-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>استلام طلب راجع</h3>
                <button class="close-modal" onclick="closeReceiveModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">رقم الوصل</label>
                    <input type="text" id="return-order-id" class="form-input" readonly>
                </div>
                
                <div class="form-group">
                    <label class="form-label">المندوب</label>
                    <input type="text" id="return-driver" class="form-input" readonly>
                </div>
                
                <div class="form-group">
                    <label class="form-label">حالة الطلب عند الاستلام</label>
                    <select id="return-condition" class="form-select" required>
                        <option value="">اختر الحالة</option>
                        <option value="good">سليم</option>
                        <option value="damaged">تالف</option>
                        <option value="opened">مفتوح</option>
                        <option value="missing">ناقص</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">ملاحظات الاستلام</label>
                    <textarea id="return-notes" class="form-textarea" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeReceiveModal()">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="confirmReceiveReturn()">تأكيد الاستلام</button>
                </div>
            </div>
        </div>
    </div>

    <script src="sidebar.js"></script>
    <script src="currency.js"></script>
    <script>
        let currentReturnId = null;
        
        function receiveReturn(orderId) {
            currentReturnId = orderId;
            
            // Get order details (in real app, fetch from server)
            const orderDetails = getOrderDetails(orderId);
            
            document.getElementById('return-order-id').value = orderId;
            document.getElementById('return-driver').value = orderDetails.driver;
            document.getElementById('return-condition').value = '';
            document.getElementById('return-notes').value = '';
            
            document.getElementById('receive-modal').classList.remove('hidden');
        }
        
        function closeReceiveModal() {
            document.getElementById('receive-modal').classList.add('hidden');
            currentReturnId = null;
        }
        
        function confirmReceiveReturn() {
            const condition = document.getElementById('return-condition').value;
            const notes = document.getElementById('return-notes').value;
            
            if (!condition) {
                alert('يرجى اختيار حالة الطلب');
                return;
            }
            
            // Process the return (in real app, send to server)
            showNotification(`تم استلام الطلب ${currentReturnId} بنجاح`, 'success');
            
            // Update the row status
            updateReturnStatus(currentReturnId, 'received');
            
            closeReceiveModal();
        }
        
        function updateReturnStatus(orderId, status) {
            // Find and update the row
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const orderIdCell = row.querySelector('.order-id');
                if (orderIdCell && orderIdCell.textContent === orderId) {
                    const statusCell = row.querySelector('.status-badge');
                    statusCell.textContent = 'تم الاستلام';
                    statusCell.className = 'status-badge status-returned';
                    
                    // Update action buttons
                    const actionsCell = row.querySelector('.action-buttons');
                    actionsCell.innerHTML = `
                        <button class="action-btn" onclick="showReturnDetails('${orderId}')">تفاصيل</button>
                        <button class="action-btn primary" onclick="processReturn('${orderId}')">معالجة</button>
                    `;
                }
            });
        }
        
        function getOrderDetails(orderId) {
            // Mock data - in real app, fetch from server
            const orders = {
                'DL003': { driver: 'خالد عبدالله', recipient: 'محمد سعد' },
                'DL007': { driver: 'أحمد سالم', recipient: 'خالد عبدالرحمن' },
                'DL012': { driver: 'سعد محمد', recipient: 'فاطمة أحمد' }
            };
            return orders[orderId] || { driver: 'غير محدد', recipient: 'غير محدد' };
        }
        
        function showReturnDetails(orderId) {
            alert(`عرض تفاصيل الطلب ${orderId}`);
        }
        
        function processReturn(orderId) {
            if (confirm(`هل تريد معالجة الطلب الراجع ${orderId}؟`)) {
                showNotification(`تم إرسال الطلب ${orderId} للمعالجة`, 'info');
            }
        }
        
        function refreshReturns() {
            showNotification('تم تحديث بيانات الطلبات الراجعة', 'info');
        }
        
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span>${message}</span>
                </div>
            `;
            
            const colors = {
                'success': 'linear-gradient(135deg, #10b981, #059669)',
                'info': 'linear-gradient(135deg, #3b82f6, #2563eb)',
                'warning': 'linear-gradient(135deg, #f59e0b, #d97706)'
            };
            
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type] || colors.info};
                color: white;
                padding: 15px 20px;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                z-index: 1001;
                animation: slideInRight 0.3s ease-out;
                max-width: 400px;
                font-weight: 500;
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
