{"version": 3, "sources": ["../../src/cli/next-dev.ts"], "names": ["nextDev", "dir", "child", "config", "isTurboSession", "traceUploadUrl", "sessionStopHandled", "sessionStarted", "Date", "now", "handleSessionStop", "signal", "kill", "eventCliSessionStopped", "require", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "telemetry", "traceGlobals", "get", "Telemetry", "distDir", "path", "join", "pagesDir", "appDir", "pagesResult", "findPagesDir", "record", "cliCommand", "turboFlag", "durationMilliseconds", "flushDetached", "_", "uploadTrace", "mode", "projectDir", "process", "stdout", "write", "exit", "on", "args", "console", "log", "getProjectDir", "env", "NEXT_PRIVATE_DEV_DIR", "fileExists", "FileType", "Directory", "printAndExit", "preflight", "skipOnReb<PERSON>", "getPackageVersion", "getDependencies", "Promise", "resolve", "sassVersion", "nodeSassVersion", "all", "cwd", "name", "Log", "warn", "dependencies", "devDependencies", "command", "getNpxCommand", "port", "getPort", "isPortIsReserved", "getReservedPortExplanation", "allowRetry", "undefined", "PORT", "host", "isExperimentalTestProxy", "devServerOptions", "isDev", "hostname", "TURBOPACK", "setGlobal", "startServerPath", "startServer", "options", "resolved", "defaultEnv", "initialEnv", "NODE_OPTIONS", "getNodeOptionsWithoutInspect", "nodeDebugType", "checkNodeDebugType", "maxOldSpaceSize", "getMaxOldSpaceSize", "NEXT_DISABLE_MEM_OVERRIDE", "totalMem", "os", "totalmem", "totalMemInMB", "Math", "floor", "getDebugPort", "fork", "stdio", "NEXT_PRIVATE_WORKER", "NODE_EXTRA_CA_CERTS", "selfSignedCertificate", "rootCA", "msg", "nextWorkerReady", "send", "nextWorkerOptions", "nextServerReady", "code", "RESTART_EXIT_CODE", "sync", "runDevServer", "reboot", "certificate", "key", "cert", "createSelfSignedCertificate", "err", "error", "cleanup"], "mappings": ";;;;;+BA2VSA;;;eAAAA;;;QAzVF;uBAUA;6DACc;+BAES;2BACW;6DACxB;wBAEuB;yBACd;+DACH;8BACM;4BACQ;+BACP;wBACc;oEAEpB;qBACG;+BACN;iCAId;2DACQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEf,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC,iBAAiB;AACrB,IAAIC;AACJ,IAAIC,qBAAqB;AACzB,IAAIC,iBAAiBC,KAAKC,GAAG;AAE7B,MAAMC,oBAAoB,OAAOC;IAC/B,IAAIT,OAAO;QACTA,MAAMU,IAAI,CAAC,AAACD,UAAkB;IAChC;IACA,IAAIL,oBAAoB;IACxBA,qBAAqB;IAErB,IAAI;QACF,MAAM,EAAEO,sBAAsB,EAAE,GAC9BC,QAAQ;QAEVX,SAASA,UAAW,MAAMY,IAAAA,eAAU,EAACC,mCAAwB,EAAEf;QAE/D,IAAIgB,YACF,AAACC,oBAAY,CAACC,GAAG,CAAC,gBAGlB,IAAIC,kBAAS,CAAC;YACZC,SAASC,aAAI,CAACC,IAAI,CAACtB,KAAKE,OAAOkB,OAAO;QACxC;QAEF,IAAIG,WAAoB,CAAC,CAACN,oBAAY,CAACC,GAAG,CAAC;QAC3C,IAAIM,SAAkB,CAAC,CAACP,oBAAY,CAACC,GAAG,CAAC;QAEzC,IACE,OAAOD,oBAAY,CAACC,GAAG,CAAC,gBAAgB,eACxC,OAAOD,oBAAY,CAACC,GAAG,CAAC,cAAc,aACtC;YACA,MAAMO,cAAcC,IAAAA,0BAAY,EAAC1B;YACjCwB,SAAS,CAAC,CAACC,YAAYD,MAAM;YAC7BD,WAAW,CAAC,CAACE,YAAYF,QAAQ;QACnC;QAEAP,UAAUW,MAAM,CACdf,uBAAuB;YACrBgB,YAAY;YACZC,WAAW1B;YACX2B,sBAAsBvB,KAAKC,GAAG,KAAKF;YACnCiB;YACAC;QACF,IACA;QAEFR,UAAUe,aAAa,CAAC,OAAO/B;IACjC,EAAE,OAAOgC,GAAG;IACV,6CAA6C;IAC7C,sBAAsB;IACxB;IAEA,IAAI5B,gBAAgB;QAClB6B,IAAAA,oBAAW,EAAC;YACV7B;YACA8B,MAAM;YACN/B;YACAgC,YAAYnC;YACZoB,SAASlB,OAAOkB,OAAO;QACzB;IACF;IAEA,yDAAyD;IACzD,iDAAiD;IACjDgB,QAAQC,MAAM,CAACC,KAAK,CAAC;IACrBF,QAAQC,MAAM,CAACC,KAAK,CAAC;IACrBF,QAAQG,IAAI,CAAC;AACf;AAEAH,QAAQI,EAAE,CAAC,UAAU,IAAM/B,kBAAkB;AAC7C2B,QAAQI,EAAE,CAAC,WAAW,IAAM/B,kBAAkB;AAE9C,MAAMV,UAAsB,OAAO0C;IACjC,IAAIA,IAAI,CAAC,SAAS,EAAE;QAClBC,QAAQC,GAAG,CAAC,CAAC;;;;;;;;;;;;;;;;IAgBb,CAAC;QACDP,QAAQG,IAAI,CAAC;IACf;IACAvC,MAAM4C,IAAAA,4BAAa,EAACR,QAAQS,GAAG,CAACC,oBAAoB,IAAIL,KAAKT,CAAC,CAAC,EAAE;IAEjE,4CAA4C;IAC5C,IAAI,CAAE,MAAMe,IAAAA,sBAAU,EAAC/C,KAAKgD,oBAAQ,CAACC,SAAS,GAAI;QAChDC,IAAAA,mBAAY,EAAC,CAAC,gDAAgD,EAAElD,IAAI,CAAC;IACvE;IAEA,eAAemD,UAAUC,YAAqB;QAC5C,MAAM,EAAEC,iBAAiB,EAAEC,eAAe,EAAE,GAAI,MAAMC,QAAQC,OAAO,CACnE3C,QAAQ;QAGV,MAAM,CAAC4C,aAAaC,gBAAgB,GAAG,MAAMH,QAAQI,GAAG,CAAC;YACvDN,kBAAkB;gBAAEO,KAAK5D;gBAAK6D,MAAM;YAAO;YAC3CR,kBAAkB;gBAAEO,KAAK5D;gBAAK6D,MAAM;YAAY;SACjD;QACD,IAAIJ,eAAeC,iBAAiB;YAClCI,KAAIC,IAAI,CACN,mHACE,iEACA;QAEN;QAEA,IAAI,CAACX,cAAc;YACjB,MAAM,EAAEY,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAMX,gBAAgB;gBAC9DM,KAAK5D;YACP;YAEA,6GAA6G;YAC7G,IACEgE,YAAY,CAAC,aAAa,IACzBC,eAAe,CAAC,aAAa,IAC5BA,eAAe,CAAC,aAAa,KAAK,eACpC;gBACA,MAAMC,UAAUC,IAAAA,4BAAa,EAACnE;gBAC9B8D,KAAIC,IAAI,CACN,2GACE,6DACA,CAAC,6BAA6B,EAAEG,QAAQ,4GAA4G,CAAC;YAE3J;QACF;IACF;IAEA,MAAME,OAAOC,IAAAA,cAAO,EAAC5B;IAErB,IAAI6B,IAAAA,iCAAgB,EAACF,OAAO;QAC1BlB,IAAAA,mBAAY,EAACqB,IAAAA,2CAA0B,EAACH,OAAO;IACjD;IAEA,2EAA2E;IAC3E,MAAMI,aACJ/B,IAAI,CAAC,SAAS,KAAKgC,aAAarC,QAAQS,GAAG,CAAC6B,IAAI,KAAKD;IAEvD,8DAA8D;IAC9D,0DAA0D;IAC1D,MAAME,OAAOlC,IAAI,CAAC,aAAa;IAE/BvC,SAAS,MAAMY,IAAAA,eAAU,EAACC,mCAAwB,EAAEf;IAEpD,MAAM4E,0BAA0BnC,IAAI,CAAC,4BAA4B;IAEjE,IAAIA,IAAI,CAAC,8BAA8B,EAAE;QACvCrC,iBAAiBqC,IAAI,CAAC,8BAA8B;IACtD;IAEA,MAAMoC,mBAAuC;QAC3C7E;QACAoE;QACAI;QACAM,OAAO;QACPC,UAAUJ;QACVC;IACF;IAEA,IAAInC,IAAI,CAAC,UAAU,EAAE;QACnBL,QAAQS,GAAG,CAACmC,SAAS,GAAG;IAC1B;IAEA7E,iBAAiB,CAAC,CAACiC,QAAQS,GAAG,CAACmC,SAAS;IAExC,MAAM5D,UAAUC,aAAI,CAACC,IAAI,CAACtB,KAAKE,OAAOkB,OAAO,IAAI;IACjD6D,IAAAA,iBAAS,EAAC,SAASlE,mCAAwB;IAC3CkE,IAAAA,iBAAS,EAAC,WAAW7D;IAErB,MAAM8D,kBAAkBrE,QAAQ2C,OAAO,CAAC;IACxC,eAAe2B,YAAYC,OAA2B;QACpD,OAAO,IAAI7B,QAAc,CAACC;YACxB,IAAI6B,WAAW;YACf,MAAMC,aAAcC,eAAU,IAAInD,QAAQS,GAAG;YAE7C,IAAI2C,eAAeC,IAAAA,mCAA4B;YAC/C,IAAIC,gBAAgBC,IAAAA,yBAAkB;YAEtC,MAAMC,kBAAkBC,IAAAA,yBAAkB;YAE1C,IAAI,CAACD,mBAAmB,CAACxD,QAAQS,GAAG,CAACiD,yBAAyB,EAAE;gBAC9D,MAAMC,WAAWC,WAAE,CAACC,QAAQ;gBAC5B,MAAMC,eAAeC,KAAKC,KAAK,CAACL,WAAW,OAAO;gBAClDP,eAAe,CAAC,EAAEA,aAAa,sBAAsB,EAAEW,KAAKC,KAAK,CAC/DF,eAAe,KACf,CAAC;YACL;YAEA,IAAIR,eAAe;gBACjBF,eAAe,CAAC,EAAEA,aAAa,GAAG,EAAEE,cAAc,CAAC,EACjDW,IAAAA,mBAAY,MAAK,EAClB,CAAC;YACJ;YAEApG,QAAQqG,IAAAA,mBAAI,EAACpB,iBAAiB;gBAC5BqB,OAAO;gBACP1D,KAAK;oBACH,GAAGyC,UAAU;oBACbN,WAAW5C,QAAQS,GAAG,CAACmC,SAAS;oBAChCwB,qBAAqB;oBACrBC,qBAAqBrB,QAAQsB,qBAAqB,GAC9CtB,QAAQsB,qBAAqB,CAACC,MAAM,GACpCrB,WAAWmB,mBAAmB;oBAClCjB;gBACF;YACF;YAEAvF,MAAMuC,EAAE,CAAC,WAAW,CAACoE;gBACnB,IAAIA,OAAO,OAAOA,QAAQ,UAAU;oBAClC,IAAIA,IAAIC,eAAe,EAAE;wBACvB5G,yBAAAA,MAAO6G,IAAI,CAAC;4BAAEC,mBAAmB3B;wBAAQ;oBAC3C,OAAO,IAAIwB,IAAII,eAAe,IAAI,CAAC3B,UAAU;wBAC3CA,WAAW;wBACX7B;oBACF;gBACF;YACF;YAEAvD,MAAMuC,EAAE,CAAC,QAAQ,OAAOyE,MAAMvG;gBAC5B,IAAIL,sBAAsBK,QAAQ;oBAChC;gBACF;gBACA,IAAIuG,SAASC,wBAAiB,EAAE;oBAC9B,uEAAuE;oBACvE,oEAAoE;oBACpE,wBAAwB;oBACxB,IAAI9G,gBAAgB;wBAClB6B,IAAAA,oBAAW,EAAC;4BACV7B;4BACA8B,MAAM;4BACN/B;4BACAgC,YAAYnC;4BACZoB,SAASlB,OAAOkB,OAAO;4BACvB+F,MAAM;wBACR;oBACF;oBACA,OAAOhC,YAAYC;gBACrB;gBACA,MAAM3E,kBAAkBC;YAC1B;QACF;IACF;IAEA,MAAM0G,eAAe,OAAOC;QAC1B,IAAI;YACF,IAAI,CAAC,CAAC5E,IAAI,CAAC,uBAAuB,EAAE;gBAClCqB,KAAIC,IAAI,CACN;gBAGF,IAAIuD;gBAEJ,MAAMC,MAAM9E,IAAI,CAAC,2BAA2B;gBAC5C,MAAM+E,OAAO/E,IAAI,CAAC,4BAA4B;gBAC9C,MAAMkE,SAASlE,IAAI,CAAC,0BAA0B;gBAE9C,IAAI8E,OAAOC,MAAM;oBACfF,cAAc;wBACZC,KAAKlG,aAAI,CAACmC,OAAO,CAAC+D;wBAClBC,MAAMnG,aAAI,CAACmC,OAAO,CAACgE;wBACnBb,QAAQA,SAAStF,aAAI,CAACmC,OAAO,CAACmD,UAAUlC;oBAC1C;gBACF,OAAO;oBACL6C,cAAc,MAAMG,IAAAA,mCAA2B,EAAC9C;gBAClD;gBAEA,MAAMQ,YAAY;oBAChB,GAAGN,gBAAgB;oBACnB6B,uBAAuBY;gBACzB;YACF,OAAO;gBACL,MAAMnC,YAAYN;YACpB;YAEA,MAAM1B,UAAUkE;QAClB,EAAE,OAAOK,KAAK;YACZhF,QAAQiF,KAAK,CAACD;YACdtF,QAAQG,IAAI,CAAC;QACf;IACF;IAEA,MAAM6E,aAAa;AACrB;AAEA,SAASQ;IACP,IAAI,CAAC3H,OAAO;QACV;IACF;IAEAA,MAAMU,IAAI,CAAC;AACb;AAEAyB,QAAQI,EAAE,CAAC,QAAQoF;AACnBxF,QAAQI,EAAE,CAAC,UAAUoF;AACrBxF,QAAQI,EAAE,CAAC,WAAWoF"}