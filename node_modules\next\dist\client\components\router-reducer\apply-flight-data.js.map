{"version": 3, "sources": ["../../../../src/client/components/router-reducer/apply-flight-data.ts"], "names": ["applyFlightData", "existingCache", "cache", "flightDataPath", "wasPrefetched", "treePatch", "cacheNodeSeedData", "head", "slice", "length", "subTreeData", "status", "CacheStates", "READY", "fillLazyItemsTillLeafWithHead", "parallelRoutes", "Map", "fillCacheWithNewSubTreeData"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;+CANY;+CAGkB;6CACF;AAErC,SAASA,gBACdC,aAAwB,EACxBC,KAAgB,EAChBC,cAA8B,EAC9BC,aAA8B;IAA9BA,IAAAA,0BAAAA,gBAAyB;IAEzB,0DAA0D;IAC1D,MAAM,CAACC,WAAWC,mBAAmBC,KAAK,GAAGJ,eAAeK,KAAK,CAAC,CAAC;IAEnE,8FAA8F;IAC9F,IAAIF,sBAAsB,MAAM;QAC9B,OAAO;IACT;IAEA,IAAIH,eAAeM,MAAM,KAAK,GAAG;QAC/B,MAAMC,cAAcJ,iBAAiB,CAAC,EAAE;QACxCJ,MAAMS,MAAM,GAAGC,0CAAW,CAACC,KAAK;QAChCX,MAAMQ,WAAW,GAAGA;QACpBI,IAAAA,4DAA6B,EAC3BZ,OACAD,eACAI,WACAC,mBACAC,MACAH;IAEJ,OAAO;QACL,mDAAmD;QACnDF,MAAMS,MAAM,GAAGC,0CAAW,CAACC,KAAK;QAChCX,MAAMQ,WAAW,GAAGT,cAAcS,WAAW;QAC7CR,MAAMa,cAAc,GAAG,IAAIC,IAAIf,cAAcc,cAAc;QAC3D,oEAAoE;QACpEE,IAAAA,wDAA2B,EACzBf,OACAD,eACAE,gBACAC;IAEJ;IAEA,OAAO;AACT"}