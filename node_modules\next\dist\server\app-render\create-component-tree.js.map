{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "names": ["Postpone", "createComponentTree", "postpone", "createSegmentPath", "loaderTree", "tree", "parentParams", "firstItem", "rootLayoutIncluded", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "asNotFound", "metadataOutlet", "ctx", "renderOpts", "nextConfigOutput", "staticGenerationStore", "componentMod", "staticGenerationBailout", "NotFoundBoundary", "LayoutRouter", "RenderFromTemplateContext", "StaticGenerationSearchParamsBailoutProvider", "serverHooks", "DynamicServerError", "pagePath", "getDynamicParamFromSegment", "isPrefetch", "searchParamsProps", "page", "layoutOrPagePath", "segment", "components", "parallelRoutes", "parseLoaderTree", "layout", "template", "error", "loading", "notFound", "injectedCSSWithCurrentLayout", "Set", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "layerAssets", "getLayerAssets", "Template", "templateStyles", "templateScripts", "createComponentStylesAndScripts", "filePath", "getComponent", "React", "Fragment", "ErrorComponent", "errorStyles", "errorScripts", "Loading", "loadingStyles", "loadingScripts", "isLayout", "isPage", "layoutOrPageMod", "getLayoutOrPageModule", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "dynamic", "forceDynamic", "dynamicShouldError", "link", "forceStatic", "fetchCache", "revalidate", "defaultRevalidate", "isStaticGeneration", "dynamicUsageDescription", "dynamicUsageErr", "LayoutOrPage", "interopDefault", "undefined", "Component", "parallelKeys", "Object", "keys", "hasSlot<PERSON>ey", "length", "componentProps", "NotFoundComponent", "RootLayoutComponent", "process", "env", "NODE_ENV", "isValidElementType", "require", "Error", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "parallelRouteMap", "Promise", "all", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "currentSegmentPath", "parallelRoute", "notFoundComponent", "currentStyles", "childCacheNodeSeedData", "hasLoadingComponentInTree", "seedData", "styles", "childComponentStyles", "child", "parallel<PERSON><PERSON>er<PERSON>ey", "segmentPath", "hasLoading", "Boolean", "parallelRouteProps", "parallelRouteCacheNodeSeedData", "parallelRouteProp", "flightData", "children", "isClientComponent", "isClientReference", "meta", "name", "content", "props", "params", "propsForComponent"], "mappings": ";;;;;;;;;;;;;;;IAoBaA,QAAQ;eAARA;;IAYSC,mBAAmB;eAAnBA;;;8DA/BgB;iCACJ;8BACI;gCAEP;iCACC;iDAEgB;gCACjB;2CACW;;;;;;AAUnC,MAAMD,WAAW,CAAC,EACvBE,QAAQ,EAGT;IACC,oEAAoE;IACpE,OAAOA,SAAS;AAClB;AAKO,eAAeD,oBAAoB,EACxCE,iBAAiB,EACjBC,YAAYC,IAAI,EAChBC,YAAY,EACZC,SAAS,EACTC,kBAAkB,EAClBC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,UAAU,EACVC,cAAc,EACdC,GAAG,EAaJ;IACC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAE,EAChCC,qBAAqB,EACrBC,cAAc,EACZC,uBAAuB,EACvBC,gBAAgB,EAChBC,YAAY,EACZC,yBAAyB,EACzBC,2CAA2C,EAC3CC,aAAa,EAAEC,kBAAkB,EAAE,EACpC,EACDC,QAAQ,EACRC,0BAA0B,EAC1BC,UAAU,EACVC,iBAAiB,EAClB,GAAGf;IAEJ,MAAM,EAAEgB,IAAI,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,cAAc,EAAE,GACnEC,IAAAA,gCAAe,EAAC9B;IAElB,MAAM,EAAE+B,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE,aAAaC,QAAQ,EAAE,GAAGP;IAEpE,MAAMQ,+BAA+B,IAAIC,IAAIjC;IAC7C,MAAMkC,8BAA8B,IAAID,IAAIhC;IAC5C,MAAMkC,2CAA2C,IAAIF,IACnD/B;IAGF,MAAMkC,cAAcC,IAAAA,8BAAc,EAAC;QACjChC;QACAiB;QACAtB,aAAagC;QACb/B,YAAYiC;QACZhC,yBAAyBiC;IAC3B;IAEA,MAAM,CAACG,UAAUC,gBAAgBC,gBAAgB,GAAGZ,WAChD,MAAMa,IAAAA,gEAA+B,EAAC;QACpCpC;QACAqC,UAAUd,QAAQ,CAAC,EAAE;QACrBe,cAAcf,QAAQ,CAAC,EAAE;QACzB5B,aAAagC;QACb/B,YAAYiC;IACd,KACA;QAACU,cAAK,CAACC,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,aAAaC,aAAa,GAAGnB,QAChD,MAAMY,IAAAA,gEAA+B,EAAC;QACpCpC;QACAqC,UAAUb,KAAK,CAAC,EAAE;QAClBc,cAAcd,KAAK,CAAC,EAAE;QACtB7B,aAAagC;QACb/B,YAAYiC;IACd,KACA,EAAE;IAEN,MAAM,CAACe,SAASC,eAAeC,eAAe,GAAGrB,UAC7C,MAAMW,IAAAA,gEAA+B,EAAC;QACpCpC;QACAqC,UAAUZ,OAAO,CAAC,EAAE;QACpBa,cAAcb,OAAO,CAAC,EAAE;QACxB9B,aAAagC;QACb/B,YAAYiC;IACd,KACA,EAAE;IAEN,MAAMkB,WAAW,OAAOzB,WAAW;IACnC,MAAM0B,SAAS,OAAOhC,SAAS;IAC/B,MAAM,CAACiC,gBAAgB,GAAG,MAAMC,IAAAA,mCAAqB,EAAC3D;IAEtD;;GAEC,GACD,MAAM4D,wBAAwBJ,YAAY,CAACrD;IAC3C;;GAEC,GACD,MAAM0D,uCACJ1D,sBAAsByD;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAG5B,WAC/B,MAAMU,IAAAA,gEAA+B,EAAC;QACpCpC;QACAqC,UAAUX,QAAQ,CAAC,EAAE;QACrBY,cAAcZ,QAAQ,CAAC,EAAE;QACzB/B,aAAagC;QACb/B,YAAYiC;IACd,KACA,EAAE;IAEN,IAAI0B,UAAUN,mCAAAA,gBAAiBM,OAAO;IAEtC,IAAIrD,qBAAqB,UAAU;QACjC,IAAI,CAACqD,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtCpD,sBAAsBqD,YAAY,GAAG;YACrCrD,sBAAsBsD,kBAAkB,GAAG;YAC3CpD,wBAAwB,CAAC,cAAc,CAAC,EAAE;gBACxCkD;gBACAG,MAAM;YACR;QACF;IACF;IAEA,IAAI,OAAOH,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvBpD,sBAAsBsD,kBAAkB,GAAG;QAC7C,OAAO,IAAIF,YAAY,iBAAiB;YACtCpD,sBAAsBqD,YAAY,GAAG;YAErC,0DAA0D;YAC1D,IAAI,CAACrD,sBAAsBf,QAAQ,EAAE;gBACnC,wEAAwE;gBACxE,0CAA0C;gBAC1CiB,wBAAwB,CAAC,aAAa,CAAC,EAAE;oBAAEkD;gBAAQ;YACrD;QACF,OAAO;YACLpD,sBAAsBsD,kBAAkB,GAAG;YAC3C,IAAIF,YAAY,gBAAgB;gBAC9BpD,sBAAsBwD,WAAW,GAAG;YACtC,OAAO;gBACLxD,sBAAsBwD,WAAW,GAAG;YACtC;QACF;IACF;IAEA,IAAI,QAAOV,mCAAAA,gBAAiBW,UAAU,MAAK,UAAU;QACnDzD,sBAAsByD,UAAU,GAAGX,mCAAAA,gBAAiBW,UAAU;IAChE;IAEA,IAAI,QAAOX,mCAAAA,gBAAiBY,UAAU,MAAK,UAAU;QACnD7D,IAAI8D,iBAAiB,GAAGb,gBAAgBY,UAAU;QAElD,IACE,OAAO1D,sBAAsB0D,UAAU,KAAK,eAC3C,OAAO1D,sBAAsB0D,UAAU,KAAK,YAC3C1D,sBAAsB0D,UAAU,GAAG7D,IAAI8D,iBAAiB,EAC1D;YACA3D,sBAAsB0D,UAAU,GAAG7D,IAAI8D,iBAAiB;QAC1D;QAEA,IACE3D,sBAAsB4D,kBAAkB,IACxC/D,IAAI8D,iBAAiB,KAAK,KAC1B,wEAAwE;QACxE,0CAA0C;QAC1C,CAAC3D,sBAAsBf,QAAQ,EAC/B;YACA,MAAM4E,0BAA0B,CAAC,yBAAyB,EAAE9C,QAAQ,CAAC;YACrEf,sBAAsB6D,uBAAuB,GAAGA;YAEhD,MAAM,IAAIrD,mBAAmBqD;QAC/B;IACF;IAEA,oEAAoE;IACpE,IAAI7D,sBAAsB8D,eAAe,EAAE;QACzC,MAAM9D,sBAAsB8D,eAAe;IAC7C;IAEA,MAAMC,eAAejB,kBACjBkB,IAAAA,8BAAc,EAAClB,mBACfmB;IAEJ;;GAEC,GACD,IAAIC,YAAYH;IAChB,MAAMI,eAAeC,OAAOC,IAAI,CAACpD;IACjC,MAAMqD,aAAaH,aAAaI,MAAM,GAAG;IAEzC,IAAID,cAActB,uBAAuB;QACvCkB,YAAY,CAACM;YACX,MAAMC,oBAAoBvB;YAC1B,MAAMwB,sBAAsBX;YAC5B,qBACE,6BAAC5D;gBACCoB,wBACE,4DACGK,2BACD,6BAAC8C,2BACEvB,8BACD,6BAACsB;6BAKP,6BAACC,qBAAwBF;QAG/B;IACF;IAEA,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,QAAQ;QACvC,IACE,AAAClC,CAAAA,UAAU,OAAOqB,cAAc,WAAU,KAC1C,CAACY,mBAAmBZ,YACpB;YACA,MAAM,IAAIc,MACR,CAAC,sDAAsD,EAAEvE,SAAS,CAAC,CAAC;QAExE;QAEA,IACE,OAAO6B,mBAAmB,eAC1B,CAACwC,mBAAmBxC,iBACpB;YACA,MAAM,IAAI0C,MACR,CAAC,8DAA8D,EAAEjE,QAAQ,CAAC;QAE9E;QAEA,IAAI,OAAO0B,YAAY,eAAe,CAACqC,mBAAmBrC,UAAU;YAClE,MAAM,IAAIuC,MACR,CAAC,0DAA0D,EAAEjE,QAAQ,CAAC;QAE1E;QAEA,IAAI,OAAOmC,aAAa,eAAe,CAAC4B,mBAAmB5B,WAAW;YACpE,MAAM,IAAI8B,MACR,CAAC,2DAA2D,EAAEjE,QAAQ,CAAC;QAE3E;IACF;IAEA,iCAAiC;IACjC,MAAMkE,eAAevE,2BAA2BK;IAChD;;GAEC,GACD,MAAMmE,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAG9F,YAAY;QACf,CAAC4F,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IAEA9F;IACN,4BAA4B;IAC5B,MAAMgG,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAGvE;IAEhE,EAAE;IACF,8EAA8E;IAC9E,kBAAkB;IAClB,MAAMwE,mBAAmB,MAAMC,QAAQC,GAAG,CACxCrB,OAAOC,IAAI,CAACpD,gBAAgByE,GAAG,CAC7B,OACEC;QAEA,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,qBAAwCvG,YAC1C;YAACqG;SAAiB,GAClB;YAACN;YAAeM;SAAiB;QAErC,MAAMG,gBAAgB7E,cAAc,CAAC0E,iBAAiB;QAEtD,MAAMI,oBACJ7C,YAAY0C,mCAAqB,6BAAC1C,kBAAce;QAElD,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAI+B,gBAAgB/B;QACpB,IAAIgC,yBAAmD;QACvD,IACE,CACEtF,CAAAA,cACC8B,CAAAA,WAAW,CAACyD,IAAAA,oDAAyB,EAACJ,cAAa,CAAC,GAEvD;YACA,6BAA6B;YAC7B,MAAM,EAAEK,QAAQ,EAAEC,QAAQC,oBAAoB,EAAE,GAC9C,MAAMrH,oBAAoB;gBACxBE,mBAAmB,CAACoH;oBAClB,OAAOpH,kBAAkB;2BAAI2G;2BAAuBS;qBAAM;gBAC5D;gBACAnH,YAAY2G;gBACZzG,cAAc6F;gBACd3F,oBAAoB0D;gBACpBzD,aAAagC;gBACb/B,YAAYiC;gBACZhC,yBAAyBiC;gBACzBhC;gBACAC;gBACAC;YACF;YAEFmG,gBAAgBK;YAChBJ,yBAAyBE;QAC3B;QAEA,4CAA4C;QAC5C,OAAO;YACLR;0BACA,6BAACvF;gBACCmG,mBAAmBZ;gBACnBa,aAAatH,kBAAkB2G;gBAC/BvE,SAASmB,wBAAU,6BAACA,iBAAawB;gBACjCvB,eAAeA;gBACfC,gBAAgBA;gBAChB,sKAAsK;gBACtK8D,YAAYC,QAAQjE;gBACpBpB,OAAOiB;gBACPC,aAAaA;gBACbC,cAAcA;gBACdpB,wBACE,6BAACU,8BACC,6BAACzB;gBAGL0B,gBAAgBA;gBAChBC,iBAAiBA;gBACjBT,UAAUwE;gBACV5C,gBAAgBA;gBAChBiD,QAAQJ;;YAEVC;SACD;IACH;IAIJ,uFAAuF;IACvF,IAAIU,qBAAyD,CAAC;IAC9D,IAAIC,iCAEA,CAAC;IACL,KAAK,MAAMd,iBAAiBP,iBAAkB;QAC5C,MAAM,CAACI,kBAAkBkB,mBAAmBC,WAAW,GAAGhB;QAC1Da,kBAAkB,CAAChB,iBAAiB,GAAGkB;QACvCD,8BAA8B,CAACjB,iBAAiB,GAAGmB;IACrD;IAEA,wIAAwI;IACxI,IAAI,CAAC5C,WAAW;QACd,OAAO;YACLiC,UAAU;gBACRd;gBACAuB;gBACA,wEAAwE;gBACxE,sEAAsE;gBACtE,wEAAwE;gBACxE,uEAAuE;gBACvE,oBAAoB;8BACpB,4DAAGD,mBAAmBI,QAAQ;aAC/B;YACDX,QAAQxE;QACV;IACF;IAEA,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,gBAAgB;IAChB,IAAI5B,sBAAsBqD,YAAY,IAAIrD,sBAAsBf,QAAQ,EAAE;QACxE,OAAO;YACLkH,UAAU;gBACRd;gBACAuB;8BACA,6BAAC7H;oBAASE,UAAUe,sBAAsBf,QAAQ;;aACnD;YACDmH,QAAQxE;QACV;IACF;IAEA,MAAMoF,oBAAoBC,IAAAA,kCAAiB,EAACnE;IAE5C,oEAAoE;IACpE,iEAAiE;IACjE,IAAIiD,oBAAoB,CAAC;IACzB,IACE7C,YACAvD,cACA,2GAA2G;IAC3G,6DAA6D;IAC7D,CAAC4F,iBAAiBhB,MAAM,EACxB;QACAwB,oBAAoB;YAClBgB,wBACE,0EACE,6BAACG;gBAAKC,MAAK;gBAASC,SAAQ;gBAC3BzC,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,6BAACqC;gBAAKC,MAAK;gBAAaC,SAAQ;gBAEjCjE,8BACD,6BAACD;QAGP;IACF;IAEA,MAAMmE,QAAQ;QACZ,GAAGV,kBAAkB;QACrB,GAAGZ,iBAAiB;QACpB,8GAA8G;QAC9G,gEAAgE;QAChE,+GAA+G;QAC/GuB,QAAQpC;QACR,iCAAiC;QACjC,GAAG,AAAC,CAAA;YACF,IAAI8B,qBAAqBhH,sBAAsB4D,kBAAkB,EAAE;gBACjE,OAAO,CAAC;YACV;YAEA,IAAIf,QAAQ;gBACV,OAAOjC;YACT;QACF,CAAA,GAAI;IACN;IAEA,OAAO;QACLuF,UAAU;YACRd;YACAuB;0BACA,4DACG/D,SAASjD,iBAAiB,MAE1BiD,UAAUmE,kCACT,6BAAC1G;gBACCiH,mBAAmBF;gBACnBnD,WAAWA;gBACXN,oBAAoB5D,sBAAsB4D,kBAAkB;+BAG9D,6BAACM,WAAcmD,QAUhB;SAEJ;QACDjB,QAAQxE;IACV;AACF"}