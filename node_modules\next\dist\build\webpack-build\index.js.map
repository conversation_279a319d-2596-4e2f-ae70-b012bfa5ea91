{"version": 3, "sources": ["../../../src/build/webpack-build/index.ts"], "names": ["webpackBuild", "debug", "origDebug", "ORDERED_COMPILER_NAMES", "pluginState", "deepMerge", "target", "source", "result", "key", "Object", "keys", "Array", "isArray", "webpackBuildWithWorker", "compilerNames", "config", "telemetryPlugin", "buildSpinner", "nextBuildSpan", "prunedBuildContext", "NextBuildContext", "getWorker", "compilerName", "_worker", "Worker", "path", "join", "__dirname", "exposedMethods", "numWorkers", "maxRetries", "forkOptions", "env", "process", "NEXT_PRIVATE_BUILD_WORKER", "getStderr", "pipe", "stderr", "getStdout", "stdout", "worker", "_workerPool", "_workers", "_child", "on", "code", "signal", "console", "error", "combinedResult", "duration", "buildTraceContext", "curR<PERSON>ult", "worker<PERSON>ain", "buildContext", "traceState", "exportTraceState", "defaultParentSpanId", "getId", "shouldSaveTraceEvents", "debugTraceEvents", "recordTraceEvents", "end", "entriesTrace", "entryNameMap", "chunksTrace", "entryNameFilesMap", "length", "stopAndPersist", "Log", "event", "experimental", "webpackBuildWorker", "webpackBuildImpl", "require"], "mappings": ";;;;+BAwIsBA;;;eAAAA;;;6DAvID;8BACY;4BAEV;8DACD;6DAEL;uBACmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpD,MAAMC,QAAQC,IAAAA,cAAS,EAAC;AAExB,MAAMC,yBAAyB;IAC7B;IACA;IACA;CACD;AAED,IAAIC,cAAgC,CAAC;AAErC,SAASC,UAAUC,MAAW,EAAEC,MAAW;IACzC,MAAMC,SAAS;QAAE,GAAGF,MAAM;QAAE,GAAGC,MAAM;IAAC;IACtC,KAAK,MAAME,OAAOC,OAAOC,IAAI,CAACH,QAAS;QACrCA,MAAM,CAACC,IAAI,GAAGG,MAAMC,OAAO,CAACP,MAAM,CAACG,IAAI,IAClCH,MAAM,CAACG,IAAI,GAAG;eAAIH,MAAM,CAACG,IAAI;eAAMF,MAAM,CAACE,IAAI,IAAI,EAAE;SAAE,GACvD,OAAOH,MAAM,CAACG,IAAI,IAAI,YAAY,OAAOF,MAAM,CAACE,IAAI,IAAI,WACxDJ,UAAUC,MAAM,CAACG,IAAI,EAAEF,MAAM,CAACE,IAAI,IAClCD,MAAM,CAACC,IAAI;IACjB;IACA,OAAOD;AACT;AAEA,eAAeM,uBACbC,gBAA+CZ,sBAAsB;IAErE,MAAM,EACJa,MAAM,EACNC,eAAe,EACfC,YAAY,EACZC,aAAa,EACb,GAAGC,oBACJ,GAAGC,8BAAgB;IAEpBD,mBAAmBhB,WAAW,GAAGA;IAEjC,MAAMkB,YAAY,CAACC;YAeK;QAdtB,MAAMC,UAAU,IAAIC,kBAAM,CAACC,aAAI,CAACC,IAAI,CAACC,WAAW,YAAY;YAC1DC,gBAAgB;gBAAC;aAAa;YAC9BC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACdE,2BAA2B;gBAC7B;YACF;QACF;QACAX,QAAQY,SAAS,GAAGC,IAAI,CAACH,QAAQI,MAAM;QACvCd,QAAQe,SAAS,GAAGF,IAAI,CAACH,QAAQM,MAAM;QAEvC,KAAK,MAAMC,UAAW,EAAA,sBAAA,AAACjB,QAAgBkB,WAAW,qBAA5B,oBAA8BC,QAAQ,KAAI,EAAE,CAE7D;YACHF,OAAOG,MAAM,CAACC,EAAE,CAAC,QAAQ,CAACC,MAAMC;gBAC9B,IAAID,QAASC,UAAUA,WAAW,UAAW;oBAC3CC,QAAQC,KAAK,CACX,CAAC,SAAS,EAAE1B,aAAa,gCAAgC,EAAEuB,KAAK,aAAa,EAAEC,OAAO,CAAC;gBAE3F;YACF;QACF;QAEA,OAAOvB;IACT;IAEA,MAAM0B,iBAAiB;QACrBC,UAAU;QACVC,mBAAmB,CAAC;IACtB;IAEA,KAAK,MAAM7B,gBAAgBR,cAAe;YAwBpCsC;QAvBJ,MAAMZ,SAASnB,UAAUC;QAEzB,MAAM8B,YAAY,MAAMZ,OAAOa,UAAU,CAAC;YACxCC,cAAcnC;YACdG;YACAiC,YAAY;gBACV,GAAGC,IAAAA,uBAAgB,GAAE;gBACrBC,mBAAmB,EAAEvC,iCAAAA,cAAewC,KAAK;gBACzCC,uBAAuB;YACzB;QACF;QACA,IAAIzC,iBAAiBkC,UAAUQ,gBAAgB,EAAE;YAC/CC,IAAAA,wBAAiB,EAACT,UAAUQ,gBAAgB;QAC9C;QACA,0DAA0D;QAC1D,MAAMpB,OAAOsB,GAAG;QAEhB,sBAAsB;QACtB3D,cAAcC,UAAUD,aAAaiD,UAAUjD,WAAW;QAC1DgB,mBAAmBhB,WAAW,GAAGA;QAEjC8C,eAAeC,QAAQ,IAAIE,UAAUF,QAAQ;QAE7C,KAAIE,+BAAAA,UAAUD,iBAAiB,qBAA3BC,6BAA6BW,YAAY,EAAE;gBAUzCX;YATJ,MAAM,EAAEY,YAAY,EAAE,GAAGZ,UAAUD,iBAAiB,CAACY,YAAY;YAEjE,IAAIC,cAAc;gBAChBf,eAAeE,iBAAiB,CAACY,YAAY,GAC3CX,UAAUD,iBAAiB,CAACY,YAAY;gBAC1Cd,eAAeE,iBAAiB,CAACY,YAAY,CAAEC,YAAY,GACzDA;YACJ;YAEA,KAAIZ,gCAAAA,UAAUD,iBAAiB,qBAA3BC,8BAA6Ba,WAAW,EAAE;gBAC5C,MAAM,EAAEC,iBAAiB,EAAE,GAAGd,UAAUD,iBAAiB,CAACc,WAAW;gBAErE,IAAIC,mBAAmB;oBACrBjB,eAAeE,iBAAiB,CAACc,WAAW,GAC1Cb,UAAUD,iBAAiB,CAACc,WAAW;oBAEzChB,eAAeE,iBAAiB,CAACc,WAAW,CAAEC,iBAAiB,GAC7DA;gBACJ;YACF;QACF;IACF;IAEA,IAAIpD,cAAcqD,MAAM,KAAK,GAAG;QAC9BlD,gCAAAA,aAAcmD,cAAc;QAC5BC,KAAIC,KAAK,CAAC;IACZ;IAEA,OAAOrB;AACT;AAEO,eAAelD,aACpBe,aAA6C;IAE7C,MAAMC,SAASK,8BAAgB,CAACL,MAAM;IAEtC,IAAIA,OAAOwD,YAAY,CAACC,kBAAkB,EAAE;QAC1CxE,MAAM;QACN,OAAO,MAAMa,uBAAuBC;IACtC,OAAO;QACLd,MAAM;QACN,MAAMyE,mBAAmBC,QAAQ,UAAUD,gBAAgB;QAC3D,OAAO,MAAMA;IACf;AACF"}