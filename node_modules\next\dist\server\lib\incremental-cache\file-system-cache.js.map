{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/file-system-cache.ts"], "names": ["FileSystemCache", "memoryCache", "tagsManifest", "constructor", "ctx", "fs", "flushToDisk", "serverDistDir", "appDir", "_appDir", "pagesDir", "_pagesDir", "revalidatedTags", "experimental", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "maxMemoryCacheSize", "console", "log", "L<PERSON><PERSON><PERSON>", "max", "length", "value", "JSON", "kind", "stringify", "props", "Error", "data", "body", "html", "pageData", "tagsManifestPath", "path", "join", "loadTagsManifest", "parse", "readFileSync", "err", "version", "items", "revalidateTag", "tag", "revalidatedAt", "Date", "now", "mkdir", "dirname", "writeFile", "warn", "get", "args", "key", "tags", "softTags", "kindHint", "NEXT_RUNTIME", "filePath", "getFilePath", "fileData", "readFile", "mtime", "stat", "meta", "replace", "NEXT_META_SUFFIX", "cacheEntry", "lastModified", "getTime", "headers", "status", "_", "detectFileKind", "isAppPath", "parsedData", "storedTags", "every", "includes", "set", "ppr", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "NEXT_DATA_SUFFIX", "postponed", "cacheTags", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_CACHE_TAGS_HEADER", "split", "isStale", "some", "undefined", "combinedTags", "wasRevalidated", "htmlPath", "pathname", "existsSync"], "mappings": ";;;;+BA+BA;;;eAAqBA;;;iEA1BA;6DACJ;2BAOV;;;;;;AAeP,IAAIC;AACJ,IAAIC;AAEW,MAAMF;IAWnBG,YAAYC,GAA2B,CAAE;QACvC,IAAI,CAACC,EAAE,GAAGD,IAAIC,EAAE;QAChB,IAAI,CAACC,WAAW,GAAGF,IAAIE,WAAW;QAClC,IAAI,CAACC,aAAa,GAAGH,IAAIG,aAAa;QACtC,IAAI,CAACC,MAAM,GAAG,CAAC,CAACJ,IAAIK,OAAO;QAC3B,IAAI,CAACC,QAAQ,GAAG,CAAC,CAACN,IAAIO,SAAS;QAC/B,IAAI,CAACC,eAAe,GAAGR,IAAIQ,eAAe;QAC1C,IAAI,CAACC,YAAY,GAAGT,IAAIS,YAAY;QACpC,IAAI,CAACC,KAAK,GAAG,CAAC,CAACC,QAAQC,GAAG,CAACC,wBAAwB;QAEnD,IAAIb,IAAIc,kBAAkB,IAAI,CAACjB,aAAa;YAC1C,IAAI,IAAI,CAACa,KAAK,EAAE;gBACdK,QAAQC,GAAG,CAAC;YACd;YAEAnB,cAAc,IAAIoB,iBAAQ,CAAC;gBACzBC,KAAKlB,IAAIc,kBAAkB;gBAC3BK,QAAO,EAAEC,KAAK,EAAE;wBAcSC;oBAbvB,IAAI,CAACD,OAAO;wBACV,OAAO;oBACT,OAAO,IAAIA,MAAME,IAAI,KAAK,YAAY;wBACpC,OAAOD,KAAKE,SAAS,CAACH,MAAMI,KAAK,EAAEL,MAAM;oBAC3C,OAAO,IAAIC,MAAME,IAAI,KAAK,SAAS;wBACjC,MAAM,IAAIG,MAAM;oBAClB,OAAO,IAAIL,MAAME,IAAI,KAAK,SAAS;wBACjC,OAAOD,KAAKE,SAAS,CAACH,MAAMM,IAAI,IAAI,IAAIP,MAAM;oBAChD,OAAO,IAAIC,MAAME,IAAI,KAAK,SAAS;wBACjC,OAAOF,MAAMO,IAAI,CAACR,MAAM;oBAC1B;oBACA,wCAAwC;oBACxC,OACEC,MAAMQ,IAAI,CAACT,MAAM,GAAIE,CAAAA,EAAAA,kBAAAA,KAAKE,SAAS,CAACH,MAAMS,QAAQ,sBAA7BR,gBAAgCF,MAAM,KAAI,CAAA;gBAEnE;YACF;QACF,OAAO,IAAI,IAAI,CAACT,KAAK,EAAE;YACrBK,QAAQC,GAAG,CAAC;QACd;QAEA,IAAI,IAAI,CAACb,aAAa,IAAI,IAAI,CAACF,EAAE,EAAE;YACjC,IAAI,CAAC6B,gBAAgB,GAAGC,aAAI,CAACC,IAAI,CAC/B,IAAI,CAAC7B,aAAa,EAClB,MACA,SACA,eACA;YAEF,IAAI,CAAC8B,gBAAgB;QACvB;IACF;IAEQA,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAACH,gBAAgB,IAAI,CAAC,IAAI,CAAC7B,EAAE,IAAIH,cAAc;QACxD,IAAI;YACFA,eAAeuB,KAAKa,KAAK,CACvB,IAAI,CAACjC,EAAE,CAACkC,YAAY,CAAC,IAAI,CAACL,gBAAgB,EAAE;QAEhD,EAAE,OAAOM,KAAU;YACjBtC,eAAe;gBAAEuC,SAAS;gBAAGC,OAAO,CAAC;YAAE;QACzC;QACA,IAAI,IAAI,CAAC5B,KAAK,EAAEK,QAAQC,GAAG,CAAC,oBAAoBlB;IAClD;IAEA,MAAayC,cAAcC,GAAW,EAAE;QACtC,IAAI,IAAI,CAAC9B,KAAK,EAAE;YACdK,QAAQC,GAAG,CAAC,iBAAiBwB;QAC/B;QAEA,kDAAkD;QAClD,wDAAwD;QACxD,2CAA2C;QAC3C,IAAI,CAACP,gBAAgB;QACrB,IAAI,CAACnC,gBAAgB,CAAC,IAAI,CAACgC,gBAAgB,EAAE;YAC3C;QACF;QAEA,MAAMJ,OAAO5B,aAAawC,KAAK,CAACE,IAAI,IAAI,CAAC;QACzCd,KAAKe,aAAa,GAAGC,KAAKC,GAAG;QAC7B7C,aAAawC,KAAK,CAACE,IAAI,GAAGd;QAE1B,IAAI;YACF,MAAM,IAAI,CAACzB,EAAE,CAAC2C,KAAK,CAACb,aAAI,CAACc,OAAO,CAAC,IAAI,CAACf,gBAAgB;YACtD,MAAM,IAAI,CAAC7B,EAAE,CAAC6C,SAAS,CACrB,IAAI,CAAChB,gBAAgB,EACrBT,KAAKE,SAAS,CAACzB,gBAAgB,CAAC;YAElC,IAAI,IAAI,CAACY,KAAK,EAAE;gBACdK,QAAQC,GAAG,CAAC,yBAAyBlB;YACvC;QACF,EAAE,OAAOsC,KAAU;YACjBrB,QAAQgC,IAAI,CAAC,mCAAmCX;QAClD;IACF;IAEA,MAAaY,IAAI,GAAGC,IAAqC,EAAE;YA8HrDvB,aA4BQA;QAzJZ,MAAM,CAACwB,KAAKlD,MAAM,CAAC,CAAC,CAAC,GAAGiD;QACxB,MAAM,EAAEE,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,EAAE,GAAGrD;QACrC,IAAI0B,OAAO7B,+BAAAA,YAAamD,GAAG,CAACE;QAE5B,IAAI,IAAI,CAACxC,KAAK,EAAE;YACdK,QAAQC,GAAG,CAAC,OAAOkC,KAAKC,MAAME,UAAU,CAAC,CAAC3B;QAC5C;QAEA,qCAAqC;QACrC,IAAI,CAACA,QAAQf,QAAQC,GAAG,CAAC0C,YAAY,KAAK,QAAQ;YAChD,IAAI;gBACF,MAAMC,WAAW,IAAI,CAACC,WAAW,CAAC,CAAC,EAAEN,IAAI,KAAK,CAAC,EAAE;gBACjD,MAAMO,WAAW,MAAM,IAAI,CAACxD,EAAE,CAACyD,QAAQ,CAACH;gBACxC,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC1D,EAAE,CAAC2D,IAAI,CAACL;gBAErC,MAAMM,OAAOxC,KAAKa,KAAK,CACrB,MAAM,IAAI,CAACjC,EAAE,CAACyD,QAAQ,CACpBH,SAASO,OAAO,CAAC,WAAWC,2BAAgB,GAC5C;gBAIJ,MAAMC,aAAgC;oBACpCC,cAAcN,MAAMO,OAAO;oBAC3B9C,OAAO;wBACLE,MAAM;wBACNK,MAAM8B;wBACNU,SAASN,KAAKM,OAAO;wBACrBC,QAAQP,KAAKO,MAAM;oBACrB;gBACF;gBACA,OAAOJ;YACT,EAAE,OAAOK,GAAG;YACV,oCAAoC;YACtC;YAEA,IAAI;gBACF,wDAAwD;gBACxD,IAAI/C,OAAO+B;gBACX,IAAI,CAAC/B,MAAM;oBACTA,OAAO,IAAI,CAACgD,cAAc,CAAC,CAAC,EAAEpB,IAAI,KAAK,CAAC;gBAC1C;gBAEA,MAAMqB,YAAYjD,SAAS;gBAC3B,MAAMiC,WAAW,IAAI,CAACC,WAAW,CAC/BlC,SAAS,UAAU4B,MAAM,CAAC,EAAEA,IAAI,KAAK,CAAC,EACtC5B;gBAGF,MAAMmC,WAAW,MAAM,IAAI,CAACxD,EAAE,CAACyD,QAAQ,CAACH,UAAU;gBAClD,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC1D,EAAE,CAAC2D,IAAI,CAACL;gBAErC,IAAIjC,SAAS,WAAW,IAAI,CAACpB,WAAW,EAAE;wBAQpCwB;oBAPJ,MAAMuC,eAAeN,MAAMO,OAAO;oBAClC,MAAMM,aAA+BnD,KAAKa,KAAK,CAACuB;oBAChD/B,OAAO;wBACLuC;wBACA7C,OAAOoD;oBACT;oBAEA,IAAI9C,EAAAA,eAAAA,KAAKN,KAAK,qBAAVM,aAAYJ,IAAI,MAAK,SAAS;4BACbI;wBAAnB,MAAM+C,cAAa/C,eAAAA,KAAKN,KAAK,qBAAVM,aAAYyB,IAAI;wBAEnC,iDAAiD;wBACjD,8CAA8C;wBAC9C,gCAAgC;wBAChC,IAAI,EAACA,wBAAAA,KAAMuB,KAAK,CAAC,CAAClC,MAAQiC,8BAAAA,WAAYE,QAAQ,CAACnC,QAAO;4BACpD,IAAI,IAAI,CAAC9B,KAAK,EAAE;gCACdK,QAAQC,GAAG,CAAC,+BAA+BmC,MAAMsB;4BACnD;4BACA,MAAM,IAAI,CAACG,GAAG,CAAC1B,KAAKxB,KAAKN,KAAK,EAAE;gCAAE+B;4BAAK;wBACzC;oBACF;gBACF,OAAO;oBACL,MAAMtB,WAAW0C,YACb,MAAM,IAAI,CAACtE,EAAE,CAACyD,QAAQ,CACpB,IAAI,CAACF,WAAW,CACd,CAAC,EAAEN,IAAI,EACL,IAAI,CAACzC,YAAY,CAACoE,GAAG,GAAGC,8BAAmB,GAAGC,qBAAU,CACzD,CAAC,EACF,QAEF,UAEF1D,KAAKa,KAAK,CACR,MAAM,IAAI,CAACjC,EAAE,CAACyD,QAAQ,CACpB,IAAI,CAACF,WAAW,CAAC,CAAC,EAAEN,IAAI,EAAE8B,2BAAgB,CAAC,CAAC,EAAE,UAC9C;oBAIR,IAAInB;oBAEJ,IAAIU,WAAW;wBACb,IAAI;4BACFV,OAAOxC,KAAKa,KAAK,CACf,MAAM,IAAI,CAACjC,EAAE,CAACyD,QAAQ,CACpBH,SAASO,OAAO,CAAC,WAAWC,2BAAgB,GAC5C;wBAGN,EAAE,OAAM,CAAC;oBACX;oBAEArC,OAAO;wBACLuC,cAAcN,MAAMO,OAAO;wBAC3B9C,OAAO;4BACLE,MAAM;4BACNM,MAAM6B;4BACN5B;4BACAoD,SAAS,EAAEpB,wBAAAA,KAAMoB,SAAS;4BAC1Bd,OAAO,EAAEN,wBAAAA,KAAMM,OAAO;4BACtBC,MAAM,EAAEP,wBAAAA,KAAMO,MAAM;wBACtB;oBACF;gBACF;gBAEA,IAAI1C,MAAM;oBACR7B,+BAAAA,YAAa+E,GAAG,CAAC1B,KAAKxB;gBACxB;YACF,EAAE,OAAO2C,GAAG;YACV,+BAA+B;YACjC;QACF;QAEA,IAAI3C,CAAAA,yBAAAA,cAAAA,KAAMN,KAAK,qBAAXM,YAAaJ,IAAI,MAAK,QAAQ;gBAEbI;YADnB,IAAIwD;YACJ,MAAMC,cAAazD,sBAAAA,KAAKN,KAAK,CAAC+C,OAAO,qBAAlBzC,mBAAoB,CAAC0D,iCAAsB,CAAC;YAE/D,IAAI,OAAOD,eAAe,UAAU;gBAClCD,YAAYC,WAAWE,KAAK,CAAC;YAC/B;YAEA,IAAIH,6BAAAA,UAAW/D,MAAM,EAAE;gBACrB,IAAI,CAACc,gBAAgB;gBAErB,MAAMqD,UAAUJ,UAAUK,IAAI,CAAC,CAAC/C;wBAE5B1C;oBADF,OACEA,CAAAA,iCAAAA,0BAAAA,aAAcwC,KAAK,CAACE,IAAI,qBAAxB1C,wBAA0B2C,aAAa,KACvC3C,CAAAA,gCAAAA,aAAcwC,KAAK,CAACE,IAAI,CAACC,aAAa,KACnCf,CAAAA,CAAAA,wBAAAA,KAAMuC,YAAY,KAAIvB,KAAKC,GAAG,EAAC;gBAEtC;gBAEA,kDAAkD;gBAClD,uDAAuD;gBACvD,wDAAwD;gBACxD,IAAI2C,SAAS;oBACX5D,OAAO8D;gBACT;YACF;QACF;QAEA,IAAI9D,QAAQA,CAAAA,yBAAAA,eAAAA,KAAMN,KAAK,qBAAXM,aAAaJ,IAAI,MAAK,SAAS;YACzC,IAAI,CAACW,gBAAgB;YAErB,MAAMwD,eAAe;mBAAKtC,QAAQ,EAAE;mBAAOC,YAAY,EAAE;aAAE;YAE3D,MAAMsC,iBAAiBD,aAAaF,IAAI,CAAC,CAAC/C;oBAMtC1C;gBALF,IAAI,IAAI,CAACU,eAAe,CAACmE,QAAQ,CAACnC,MAAM;oBACtC,OAAO;gBACT;gBAEA,OACE1C,CAAAA,iCAAAA,0BAAAA,aAAcwC,KAAK,CAACE,IAAI,qBAAxB1C,wBAA0B2C,aAAa,KACvC3C,CAAAA,gCAAAA,aAAcwC,KAAK,CAACE,IAAI,CAACC,aAAa,KACnCf,CAAAA,CAAAA,wBAAAA,KAAMuC,YAAY,KAAIvB,KAAKC,GAAG,EAAC;YAEtC;YACA,gDAAgD;YAChD,wCAAwC;YACxC,IAAI+C,gBAAgB;gBAClBhE,OAAO8D;YACT;QACF;QAEA,OAAO9D,QAAQ;IACjB;IAEA,MAAakD,IAAI,GAAG3B,IAAqC,EAAE;QACzD,MAAM,CAACC,KAAKxB,MAAM1B,IAAI,GAAGiD;QACzBpD,+BAAAA,YAAa+E,GAAG,CAAC1B,KAAK;YACpB9B,OAAOM;YACPuC,cAAcvB,KAAKC,GAAG;QACxB;QACA,IAAI,IAAI,CAACjC,KAAK,EAAE;YACdK,QAAQC,GAAG,CAAC,OAAOkC;QACrB;QAEA,IAAI,CAAC,IAAI,CAAChD,WAAW,EAAE;QAEvB,IAAIwB,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,SAAS;YAC1B,MAAMiC,WAAW,IAAI,CAACC,WAAW,CAAC,CAAC,EAAEN,IAAI,KAAK,CAAC,EAAE;YACjD,MAAM,IAAI,CAACjD,EAAE,CAAC2C,KAAK,CAACb,aAAI,CAACc,OAAO,CAACU;YACjC,MAAM,IAAI,CAACtD,EAAE,CAAC6C,SAAS,CAACS,UAAU7B,KAAKC,IAAI;YAE3C,MAAMkC,OAAsB;gBAC1BM,SAASzC,KAAKyC,OAAO;gBACrBC,QAAQ1C,KAAK0C,MAAM;gBACnBa,WAAWO;YACb;YAEA,MAAM,IAAI,CAACvF,EAAE,CAAC6C,SAAS,CACrBS,SAASO,OAAO,CAAC,WAAWC,2BAAgB,GAC5C1C,KAAKE,SAAS,CAACsC,MAAM,MAAM;YAE7B;QACF;QAEA,IAAInC,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,QAAQ;YACzB,MAAMiD,YAAY,OAAO7C,KAAKG,QAAQ,KAAK;YAC3C,MAAM8D,WAAW,IAAI,CAACnC,WAAW,CAC/B,CAAC,EAAEN,IAAI,KAAK,CAAC,EACbqB,YAAY,QAAQ;YAEtB,MAAM,IAAI,CAACtE,EAAE,CAAC2C,KAAK,CAACb,aAAI,CAACc,OAAO,CAAC8C;YACjC,MAAM,IAAI,CAAC1F,EAAE,CAAC6C,SAAS,CAAC6C,UAAUjE,KAAKE,IAAI;YAE3C,MAAM,IAAI,CAAC3B,EAAE,CAAC6C,SAAS,CACrB,IAAI,CAACU,WAAW,CACd,CAAC,EAAEN,IAAI,EACLqB,YACI,IAAI,CAAC9D,YAAY,CAACoE,GAAG,GACnBC,8BAAmB,GACnBC,qBAAU,GACZC,2BAAgB,CACrB,CAAC,EACFT,YAAY,QAAQ,UAEtBA,YAAY7C,KAAKG,QAAQ,GAAGR,KAAKE,SAAS,CAACG,KAAKG,QAAQ;YAG1D,IAAIH,KAAKyC,OAAO,IAAIzC,KAAK0C,MAAM,EAAE;gBAC/B,MAAMP,OAAsB;oBAC1BM,SAASzC,KAAKyC,OAAO;oBACrBC,QAAQ1C,KAAK0C,MAAM;oBACnBa,WAAWvD,KAAKuD,SAAS;gBAC3B;gBAEA,MAAM,IAAI,CAAChF,EAAE,CAAC6C,SAAS,CACrB6C,SAAS7B,OAAO,CAAC,WAAWC,2BAAgB,GAC5C1C,KAAKE,SAAS,CAACsC;YAEnB;QACF,OAAO,IAAInC,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,SAAS;YACjC,MAAMiC,WAAW,IAAI,CAACC,WAAW,CAACN,KAAK;YACvC,MAAM,IAAI,CAACjD,EAAE,CAAC2C,KAAK,CAACb,aAAI,CAACc,OAAO,CAACU;YACjC,MAAM,IAAI,CAACtD,EAAE,CAAC6C,SAAS,CACrBS,UACAlC,KAAKE,SAAS,CAAC;gBACb,GAAGG,IAAI;gBACPyB,MAAMnD,IAAImD,IAAI;YAChB;QAEJ;IACF;IAEQmB,eAAesB,QAAgB,EAAE;QACvC,IAAI,CAAC,IAAI,CAACxF,MAAM,IAAI,CAAC,IAAI,CAACE,QAAQ,EAAE;YAClC,MAAM,IAAImB,MACR;QAEJ;QAEA,0EAA0E;QAC1E,OAAO;QACP,IAAI,CAAC,IAAI,CAACrB,MAAM,IAAI,IAAI,CAACE,QAAQ,EAAE;YACjC,OAAO;QACT,OAEK,IAAI,IAAI,CAACF,MAAM,IAAI,CAAC,IAAI,CAACE,QAAQ,EAAE;YACtC,OAAO;QACT;QAEA,oEAAoE;QACpE,WAAW;QACX,IAAIiD,WAAW,IAAI,CAACC,WAAW,CAACoC,UAAU;QAC1C,IAAI,IAAI,CAAC3F,EAAE,CAAC4F,UAAU,CAACtC,WAAW;YAChC,OAAO;QACT;QAEAA,WAAW,IAAI,CAACC,WAAW,CAACoC,UAAU;QACtC,IAAI,IAAI,CAAC3F,EAAE,CAAC4F,UAAU,CAACtC,WAAW;YAChC,OAAO;QACT;QAEA,MAAM,IAAI9B,MACR,CAAC,kDAAkD,EAAEmE,SAAS,CAAC;IAEnE;IAEQpC,YACNoC,QAAgB,EAChBtE,IAA+B,EACvB;QACR,OAAQA;YACN,KAAK;gBACH,6DAA6D;gBAC7D,iBAAiB;gBACjB,OAAOS,aAAI,CAACC,IAAI,CACd,IAAI,CAAC7B,aAAa,EAClB,MACA,SACA,eACAyF;YAEJ,KAAK;gBACH,OAAO7D,aAAI,CAACC,IAAI,CAAC,IAAI,CAAC7B,aAAa,EAAE,SAASyF;YAChD,KAAK;gBACH,OAAO7D,aAAI,CAACC,IAAI,CAAC,IAAI,CAAC7B,aAAa,EAAE,OAAOyF;YAC9C;gBACE,MAAM,IAAInE,MAAM;QACpB;IACF;AACF"}