<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - شركة التوصيل السريع</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="sidebar.css">
    <link rel="stylesheet" href="orders.css">
</head>
<body>
    <main class="main-content">
        <!-- Page Header -->
        <section class="action-bar">
            <div class="action-bar-content">
                <div class="page-title">
                    <h2>الإعدادات</h2>
                    <p>إعدادات النظام والتخصيص</p>
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-success" id="save-settings-btn">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                            <polyline points="17,21 17,13 7,13 7,21"/>
                            <polyline points="7,3 7,8 15,8"/>
                        </svg>
                        حفظ الإعدادات
                    </button>
                    
                    <button class="btn btn-secondary" id="reset-settings-btn">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="1,4 1,10 7,10"/>
                            <polyline points="23,20 23,14 17,14"/>
                            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                        </svg>
                        إعادة تعيين
                    </button>
                </div>
            </div>
        </section>

        <!-- Settings Sections -->
        <section class="orders-section">
            <div class="orders-container">
                <div class="table-header">
                    <div class="table-info">
                        <h3>إعدادات النظام</h3>
                        <span class="orders-count">تخصيص النظام حسب احتياجاتك</span>
                    </div>
                </div>

                <div style="padding: 30px;">
                    <!-- Currency Settings -->
                    <div class="settings-section">
                        <div class="settings-header">
                            <div class="settings-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="12" y1="1" x2="12" y2="23"/>
                                    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                                </svg>
                            </div>
                            <div>
                                <h3>إعدادات العملة</h3>
                                <p>اختر العملة المستخدمة في النظام</p>
                            </div>
                        </div>
                        
                        <div class="settings-content">
                            <div class="form-group">
                                <label class="form-label">العملة الأساسية</label>
                                <select id="currency-select" class="form-select">
                                    <option value="IQD">الدينار العراقي (د.ع)</option>
                                    <option value="SAR">الريال السعودي (ر.س)</option>
                                </select>
                                <small class="form-help">سيتم تحويل جميع المبالغ في النظام إلى العملة المختارة</small>
                            </div>
                            
                            <div class="currency-preview">
                                <h4>معاينة العملة:</h4>
                                <div class="preview-amounts">
                                    <div class="preview-item">
                                        <span>مثال: </span>
                                        <span class="amount" data-original-amount="328000">328,000 د.ع</span>
                                    </div>
                                    <div class="preview-item">
                                        <span>مثال: </span>
                                        <span class="amount" data-original-amount="1968984">1,968,984 د.ع</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Settings -->
                    <div class="settings-section">
                        <div class="settings-header">
                            <div class="settings-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                </svg>
                            </div>
                            <div>
                                <h3>إعدادات عامة</h3>
                                <p>إعدادات أساسية للنظام</p>
                            </div>
                        </div>
                        
                        <div class="settings-content">
                            <div class="form-group">
                                <label class="form-label">اسم الشركة</label>
                                <input type="text" id="company-name" class="form-input" value="شركة التوصيل السريع">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" id="company-phone" class="form-input" value="0501234567">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">العنوان</label>
                                <textarea id="company-address" class="form-textarea" rows="3">الرياض، المملكة العربية السعودية</textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Settings -->
                    <div class="settings-section">
                        <div class="settings-header">
                            <div class="settings-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
                                    <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
                                </svg>
                            </div>
                            <div>
                                <h3>إعدادات الإشعارات</h3>
                                <p>تخصيص الإشعارات والتنبيهات</p>
                            </div>
                        </div>
                        
                        <div class="settings-content">
                            <div class="checkbox-group">
                                <input type="checkbox" id="email-notifications" class="checkbox" checked>
                                <label for="email-notifications" class="checkbox-label">إشعارات البريد الإلكتروني</label>
                            </div>
                            
                            <div class="checkbox-group">
                                <input type="checkbox" id="sms-notifications" class="checkbox" checked>
                                <label for="sms-notifications" class="checkbox-label">إشعارات الرسائل النصية</label>
                            </div>
                            
                            <div class="checkbox-group">
                                <input type="checkbox" id="push-notifications" class="checkbox" checked>
                                <label for="push-notifications" class="checkbox-label">الإشعارات الفورية</label>
                            </div>
                        </div>
                    </div>

                    <!-- Display Settings -->
                    <div class="settings-section">
                        <div class="settings-header">
                            <div class="settings-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                                    <line x1="8" y1="21" x2="16" y2="21"/>
                                    <line x1="12" y1="17" x2="12" y2="21"/>
                                </svg>
                            </div>
                            <div>
                                <h3>إعدادات العرض</h3>
                                <p>تخصيص واجهة المستخدم</p>
                            </div>
                        </div>
                        
                        <div class="settings-content">
                            <div class="form-group">
                                <label class="form-label">عدد العناصر في الصفحة</label>
                                <select id="items-per-page" class="form-select">
                                    <option value="10">10 عناصر</option>
                                    <option value="25">25 عنصر</option>
                                    <option value="50">50 عنصر</option>
                                    <option value="100">100 عنصر</option>
                                </select>
                            </div>
                            
                            <div class="checkbox-group">
                                <input type="checkbox" id="auto-refresh" class="checkbox" checked>
                                <label for="auto-refresh" class="checkbox-label">التحديث التلقائي للبيانات</label>
                            </div>
                            
                            <div class="checkbox-group">
                                <input type="checkbox" id="compact-view" class="checkbox">
                                <label for="compact-view" class="checkbox-label">العرض المضغوط</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="sidebar.js"></script>
    <script src="currency.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeSettings();
        });
        
        function initializeSettings() {
            // Load saved settings
            loadSettings();
            
            // Add event listeners
            document.getElementById('save-settings-btn').addEventListener('click', saveSettings);
            document.getElementById('reset-settings-btn').addEventListener('click', resetSettings);
            document.getElementById('currency-select').addEventListener('change', handleCurrencyChange);
        }
        
        function loadSettings() {
            // Load currency setting
            const savedCurrency = localStorage.getItem('selectedCurrency') || 'IQD';
            document.getElementById('currency-select').value = savedCurrency;
            
            // Load other settings
            const companyName = localStorage.getItem('companyName') || 'شركة التوصيل السريع';
            const companyPhone = localStorage.getItem('companyPhone') || '0501234567';
            const companyAddress = localStorage.getItem('companyAddress') || 'الرياض، المملكة العربية السعودية';
            
            document.getElementById('company-name').value = companyName;
            document.getElementById('company-phone').value = companyPhone;
            document.getElementById('company-address').value = companyAddress;
            
            // Load notification settings
            document.getElementById('email-notifications').checked = localStorage.getItem('emailNotifications') !== 'false';
            document.getElementById('sms-notifications').checked = localStorage.getItem('smsNotifications') !== 'false';
            document.getElementById('push-notifications').checked = localStorage.getItem('pushNotifications') !== 'false';
            
            // Load display settings
            document.getElementById('items-per-page').value = localStorage.getItem('itemsPerPage') || '10';
            document.getElementById('auto-refresh').checked = localStorage.getItem('autoRefresh') !== 'false';
            document.getElementById('compact-view').checked = localStorage.getItem('compactView') === 'true';
        }
        
        function saveSettings() {
            // Save company settings
            localStorage.setItem('companyName', document.getElementById('company-name').value);
            localStorage.setItem('companyPhone', document.getElementById('company-phone').value);
            localStorage.setItem('companyAddress', document.getElementById('company-address').value);
            
            // Save notification settings
            localStorage.setItem('emailNotifications', document.getElementById('email-notifications').checked);
            localStorage.setItem('smsNotifications', document.getElementById('sms-notifications').checked);
            localStorage.setItem('pushNotifications', document.getElementById('push-notifications').checked);
            
            // Save display settings
            localStorage.setItem('itemsPerPage', document.getElementById('items-per-page').value);
            localStorage.setItem('autoRefresh', document.getElementById('auto-refresh').checked);
            localStorage.setItem('compactView', document.getElementById('compact-view').checked);
            
            showNotification('تم حفظ الإعدادات بنجاح', 'success');
        }
        
        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
                // Clear all settings
                const settingsKeys = [
                    'selectedCurrency', 'companyName', 'companyPhone', 'companyAddress',
                    'emailNotifications', 'smsNotifications', 'pushNotifications',
                    'itemsPerPage', 'autoRefresh', 'compactView'
                ];
                
                settingsKeys.forEach(key => localStorage.removeItem(key));
                
                // Reload settings
                loadSettings();
                
                // Reset currency
                if (window.currencyManager) {
                    window.currencyManager.setCurrency('IQD');
                }
                
                showNotification('تم إعادة تعيين الإعدادات', 'info');
            }
        }
        
        function handleCurrencyChange() {
            const selectedCurrency = document.getElementById('currency-select').value;
            if (window.currencyManager) {
                window.currencyManager.setCurrency(selectedCurrency);
            }
        }
        
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span>${message}</span>
                </div>
            `;
            
            const colors = {
                'success': 'linear-gradient(135deg, #10b981, #059669)',
                'info': 'linear-gradient(135deg, #3b82f6, #2563eb)',
                'warning': 'linear-gradient(135deg, #f59e0b, #d97706)'
            };
            
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type] || colors.info};
                color: white;
                padding: 15px 20px;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                z-index: 1001;
                animation: slideInRight 0.3s ease-out;
                max-width: 400px;
                font-weight: 500;
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
