/* Orders Page Specific Styles */

/* Action Bar */
.action-bar {
    background: #e0e5ec;
    border-radius: 20px;
    box-shadow: 8px 8px 16px #a3b1c6, -8px -8px 16px #ffffff;
    padding: 25px;
    margin-bottom: 30px;
    animation: fadeInDown 0.6s ease-out;
}

.action-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.page-title h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 5px;
}

.page-title p {
    color: #4a5568;
    font-size: 0.95rem;
}

.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.btn {
    background: #e0e5ec;
    border: none;
    padding: 12px 20px;
    border-radius: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: inherit;
    font-size: 0.9rem;
    font-weight: 500;
    color: #4a5568;
    box-shadow: 6px 6px 12px #a3b1c6, -6px -6px 12px #ffffff;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn:hover {
    box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
    transform: translateY(-1px);
}

.btn svg {
    width: 18px;
    height: 18px;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.nav-btn {
    background: #e0e5ec;
    border: none;
    padding: 10px 15px;
    border-radius: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: inherit;
    font-size: 0.9rem;
    color: #4a5568;
    box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    box-shadow: 2px 2px 4px #a3b1c6, -2px -2px 4px #ffffff;
    transform: translateY(-1px);
}

.nav-btn svg {
    width: 18px;
    height: 18px;
}

/* Orders Section */
.orders-section {
    animation: fadeInUp 0.6s ease-out 0.2s both;
}

.orders-container {
    background: #e0e5ec;
    border-radius: 25px;
    box-shadow: 15px 15px 30px #a3b1c6, -15px -15px 30px #ffffff;
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid rgba(163, 177, 198, 0.2);
}

.table-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.table-info h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: #1a202c;
}

.orders-count {
    font-size: 0.9rem;
    color: #4a5568;
}

.table-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.view-options {
    display: flex;
    background: rgba(163, 177, 198, 0.1);
    border-radius: 10px;
    padding: 4px;
}

.view-btn {
    background: none;
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #4a5568;
}

.view-btn.active {
    background: #e0e5ec;
    box-shadow: 2px 2px 4px #a3b1c6, -2px -2px 4px #ffffff;
    color: #3b82f6;
}

.view-btn svg {
    width: 16px;
    height: 16px;
}

/* Table View */
.table-view {
    display: block;
}

.table-view.active {
    display: block;
}

.grid-view {
    display: none;
    padding: 30px;
}

.grid-view.active {
    display: block;
}

.orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.order-card {
    background: #e0e5ec;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 8px 8px 16px #a3b1c6, -8px -8px 16px #ffffff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.order-card:hover {
    box-shadow: 6px 6px 12px #a3b1c6, -6px -6px 12px #ffffff;
    transform: translateY(-2px);
}

.order-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.order-id-card {
    font-size: 1.1rem;
    font-weight: 600;
    color: #3b82f6;
}

.order-card-body {
    display: grid;
    gap: 10px;
    margin-bottom: 15px;
}

.order-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.order-info-label {
    color: #4a5568;
    font-weight: 500;
}

.order-info-value {
    color: #1a202c;
    font-weight: 600;
}

.order-card-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* Checkbox Styles */
.checkbox {
    width: 18px;
    height: 18px;
    background: #e0e5ec;
    border: none;
    border-radius: 4px;
    box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
    cursor: pointer;
    appearance: none;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox:checked {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    box-shadow: 2px 2px 4px #a3b1c6, -2px -2px 4px #ffffff;
}

.checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-top: 1px solid rgba(163, 177, 198, 0.2);
}

.pagination-info {
    font-size: 0.9rem;
    color: #4a5568;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-btn {
    background: #e0e5ec;
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    font-family: inherit;
    font-size: 0.85rem;
    color: #4a5568;
    box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    box-shadow: 2px 2px 4px #a3b1c6, -2px -2px 4px #ffffff;
    transform: translateY(-1px);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn svg {
    width: 14px;
    height: 14px;
}

.page-numbers {
    display: flex;
    gap: 5px;
}

.page-number {
    background: #e0e5ec;
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    font-family: inherit;
    font-size: 0.85rem;
    color: #4a5568;
    box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff;
    transition: all 0.2s ease;
    min-width: 36px;
}

.page-number:hover {
    box-shadow: 2px 2px 4px #a3b1c6, -2px -2px 4px #ffffff;
}

.page-number.active {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    box-shadow: inset 2px 2px 4px #2563eb, inset -2px -2px 4px #60a5fa;
}

/* Modal Enhancements */
.modal-content.large {
    max-width: 800px;
    width: 95%;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-label {
    font-size: 0.95rem;
    font-weight: 500;
    color: #2d3748;
}

.form-input, .form-select, .form-textarea {
    background: #e0e5ec;
    border: none;
    padding: 12px 16px;
    border-radius: 12px;
    font-family: inherit;
    font-size: 0.95rem;
    color: #2d3748;
    box-shadow: inset 6px 6px 12px #a3b1c6, inset -6px -6px 12px #ffffff;
    transition: all 0.3s ease;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
    outline: none;
    box-shadow: inset 4px 4px 8px #a3b1c6, inset -4px -4px 8px #ffffff;
    transform: scale(1.02);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(163, 177, 198, 0.2);
}

/* Bulk Actions */
.bulk-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .action-bar-content {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .action-buttons {
        justify-content: center;
    }
    
    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .table-actions {
        justify-content: space-between;
    }
    
    .pagination-container {
        flex-direction: column;
        gap: 15px;
    }
    
    .orders-grid {
        grid-template-columns: 1fr;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .bulk-actions {
        grid-template-columns: 1fr;
    }
}

/* Priority Badge Styles */
.priority-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
}

.priority-normal {
    background: linear-gradient(135deg, #e5e7eb, #d1d5db);
    color: #374151;
}

.priority-high {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #92400e;
}

.priority-urgent {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
}

/* Selected row/card styles */
.orders-table tbody tr.selected {
    background: rgba(59, 130, 246, 0.1);
    border-left: 3px solid #3b82f6;
}

.order-card.selected {
    border: 2px solid #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

/* Loading state */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(224, 229, 236, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 25px;
}

.loading-spinner-large {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(163, 177, 198, 0.3);
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced form styles */
.form-input:invalid {
    box-shadow: inset 6px 6px 12px #ff9999, inset -6px -6px 12px #ffffff;
}

.form-input:valid {
    box-shadow: inset 6px 6px 12px #99ff99, inset -6px -6px 12px #ffffff;
}

/* Tooltip styles */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1a202c;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
    bottom: calc(100% + 5px);
}

/* Animation enhancements */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.order-card {
    animation: fadeInScale 0.3s ease-out;
}

.orders-table tbody tr {
    animation: fadeInScale 0.2s ease-out;
}

/* Print styles */
@media print {
    .header,
    .action-bar,
    .search-section,
    .pagination-container,
    .action-buttons {
        display: none !important;
    }

    .orders-container {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .orders-table {
        font-size: 12px;
    }

    .orders-table th,
    .orders-table td {
        padding: 8px 4px;
        border: 1px solid #ccc;
    }
}
