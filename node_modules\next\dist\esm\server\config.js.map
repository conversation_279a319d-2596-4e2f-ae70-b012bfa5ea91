{"version": 3, "sources": ["../../src/server/config.ts"], "names": ["existsSync", "basename", "extname", "join", "relative", "isAbsolute", "resolve", "pathToFileURL", "findUp", "Log", "CONFIG_FILES", "PHASE_DEVELOPMENT_SERVER", "defaultConfig", "normalizeConfig", "loadWebpackHook", "imageConfigDefault", "loadEnvConfig", "updateInitialEnv", "flushAndExit", "findRootDir", "setHttpClientAndAgentOptions", "pathHasPrefix", "matchRemotePattern", "ZodParsedType", "util", "<PERSON><PERSON><PERSON><PERSON>", "hasNextSupport", "processZodErrorMessage", "issue", "message", "path", "length", "identifier", "reduce", "acc", "cur", "includes", "replaceAll", "separator", "code", "received", "undefined", "expected", "joinValues", "options", "normalizeZodErrors", "error", "shouldExit", "issues", "flatMap", "messages", "unionErrors", "map", "for<PERSON>ach", "unionMessages", "unionShouldExit", "push", "warnOptionHasBeenDeprecated", "config", "nested<PERSON><PERSON><PERSON><PERSON><PERSON>", "reason", "silent", "current", "found", "nestedPropertyKeys", "split", "key", "warn", "warnOptionHasBeenMovedOutOfExperimental", "<PERSON><PERSON><PERSON>", "new<PERSON>ey", "configFileName", "experimental", "newKeys", "shift", "assignDefaults", "dir", "userConfig", "result", "exportTrailingSlash", "trailingSlash", "Object", "keys", "currentConfig", "value", "Error", "userDistDir", "trim", "Array", "isArray", "ext", "constructor", "c", "k", "v", "ppr", "process", "env", "__NEXT_VERSION", "__NEXT_TEST_MODE", "output", "i18n", "rewrites", "redirects", "headers", "assetPrefix", "basePath", "outputFileTracingIgnores", "outputFileTracingExcludes", "startsWith", "endsWith", "amp", "canonicalBase", "images", "remotePatterns", "url", "URL", "hasMatchForAssetPrefix", "some", "pattern", "hostname", "protocol", "replace", "port", "domains", "loader", "loaderFile", "absolutePath", "serverActions", "swcMinify", "outputFileTracing", "outputStandalone", "bodySizeLimit", "parseInt", "toString", "isNaN", "outputFileTracingRoot", "useDeploymentId", "NEXT_DEPLOYMENT_ID", "deploymentId", "useDeploymentIdServerActions", "rootDir", "i18nType", "locales", "defaultLocaleType", "defaultLocale", "invalidDomainItems", "filter", "item", "domain", "console", "defaultLocaleDuplicate", "find", "altItem", "hasInvalidLocale", "locale", "domainItem", "JSON", "stringify", "invalidLocales", "String", "normalizedLocales", "Set", "duplicateLocales", "localeLower", "toLowerCase", "has", "add", "size", "localeDetectionType", "localeDetection", "devIndicators", "buildActivityPosition", "<PERSON><PERSON><PERSON><PERSON>", "userProvidedModularizeImports", "modularizeImports", "transform", "lodash", "userProvidedOptimizePackageImports", "optimizePackageImports", "loadConfig", "phase", "customConfig", "rawConfig", "onLoadUserConfig", "__NEXT_PRIVATE_RENDER_WORKER", "err", "__NEXT_PRIVATE_STANDALONE_CONFIG", "parse", "__NEXT_PRIVATE_RENDER_WORKER_CONFIG", "curLog", "info", "config<PERSON><PERSON><PERSON>", "cwd", "userConfigModule", "envBefore", "assign", "require", "href", "newEnv", "default", "NEXT_MINIMAL", "configSchema", "state", "safeParse", "success", "errorMessages", "target", "slice", "turbo", "loaders", "rules", "entries", "completeConfig", "configFile", "configBaseName", "nonJsPath", "sync", "getEnabledExperimentalFeatures", "userNextConfigExperimental", "enabledExperiments", "featureName"], "mappings": "AAAA,SAASA,UAAU,QAAQ,KAAI;AAC/B,SAASC,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,OAAO,QAAQ,OAAM;AAC7E,SAASC,aAAa,QAAQ,MAAK;AACnC,OAAOC,YAAY,6BAA4B;AAC/C,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,YAAY,EAAEC,wBAAwB,QAAQ,0BAAyB;AAChF,SAASC,aAAa,EAAEC,eAAe,QAAQ,kBAAiB;AAQhE,SAASC,eAAe,QAAQ,iBAAgB;AAChD,SAASC,kBAAkB,QAAQ,6BAA4B;AAE/D,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,YAAW;AAC3D,SAASC,YAAY,QAAQ,8BAA6B;AAC1D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,4BAA4B,QAAQ,yBAAwB;AACrE,SAASC,aAAa,QAAQ,6CAA4C;AAC1E,SAASC,kBAAkB,QAAQ,qCAAoC;AAEvE,SAASC,aAAa,EAAEC,QAAQC,OAAO,QAAQ,yBAAwB;AAEvE,SAASC,cAAc,QAAQ,uBAAsB;AAErD,SAASb,eAAe,QAAQ,kBAAiB;AAGjD,SAASc,uBAAuBC,KAAe;IAC7C,IAAIC,UAAUD,MAAMC,OAAO;IAE3B,IAAIC,OAAO;IAEX,IAAIF,MAAME,IAAI,CAACC,MAAM,GAAG,GAAG;QACzB,IAAIH,MAAME,IAAI,CAACC,MAAM,KAAK,GAAG;YAC3B,MAAMC,aAAaJ,MAAME,IAAI,CAAC,EAAE;YAChC,IAAI,OAAOE,eAAe,UAAU;gBAClC,+CAA+C;gBAC/CF,OAAO,CAAC,MAAM,EAAEE,WAAW,CAAC;YAC9B,OAAO;gBACLF,OAAO,CAAC,CAAC,EAAEE,WAAW,CAAC,CAAC;YAC1B;QACF,OAAO;YACL,+CAA+C;YAC/CF,OAAO,CAAC,CAAC,EAAEF,MAAME,IAAI,CAACG,MAAM,CAAS,CAACC,KAAKC;gBACzC,IAAI,OAAOA,QAAQ,UAAU;oBAC3B,cAAc;oBACd,OAAO,CAAC,EAAED,IAAI,CAAC,EAAEC,IAAI,CAAC,CAAC;gBACzB;gBACA,IAAIA,IAAIC,QAAQ,CAAC,MAAM;oBACrB,gBAAgB;oBAChB,OAAO,CAAC,EAAEF,IAAI,EAAE,EAAEC,IAAIE,UAAU,CAAC,KAAK,OAAO,EAAE,CAAC;gBAClD;gBACA,eAAe;gBACf,MAAMC,YAAYJ,IAAIH,MAAM,KAAK,IAAI,KAAK;gBAC1C,OAAOG,MAAMI,YAAYH;YAC3B,GAAG,IAAI,CAAC,CAAC;QACX;IACF;IAEA,IACEP,MAAMW,IAAI,KAAK,kBACfX,MAAMY,QAAQ,KAAKjB,cAAckB,SAAS,EAC1C;QACA,wBAAwB;QACxB,OAAO,CAAC,EAAEX,KAAK,sBAAsB,EAAEF,MAAMc,QAAQ,CAAC,CAAC;IACzD;IACA,IAAId,MAAMW,IAAI,KAAK,sBAAsB;QACvC,oEAAoE;QACpE,OAAO,CAAC,SAAS,EAAEd,QAAQkB,UAAU,CAACf,MAAMgB,OAAO,EAAE,YAAY,EAC/DhB,MAAMY,QAAQ,CACf,KAAK,EAAEV,KAAK,CAAC;IAChB;IAEA,OAAOD,UAAWC,CAAAA,OAAO,CAAC,IAAI,EAAEA,KAAK,CAAC,GAAG,EAAC;AAC5C;AAEA,SAASe,mBACPC,KAA2B;IAE3B,IAAIC,aAAa;IACjB,OAAO;QACLD,MAAME,MAAM,CAACC,OAAO,CAAC,CAACrB;YACpB,MAAMsB,WAAW;gBAACvB,uBAAuBC;aAAO;YAChD,IAAIA,MAAME,IAAI,CAAC,EAAE,KAAK,UAAU;gBAC9B,oEAAoE;gBACpEiB,aAAa;YACf;YAEA,IAAI,iBAAiBnB,OAAO;gBAC1BA,MAAMuB,WAAW,CACdC,GAAG,CAACP,oBACJQ,OAAO,CAAC,CAAC,CAACC,eAAeC,gBAAgB;oBACxCL,SAASM,IAAI,IAAIF;oBACjB,sEAAsE;oBACtEP,aAAaA,cAAcQ;gBAC7B;YACJ;YAEA,OAAOL;QACT;QACAH;KACD;AACH;AAEA,OAAO,SAASU,4BACdC,MAAkB,EAClBC,iBAAyB,EACzBC,MAAc,EACdC,MAAe;IAEf,IAAI,CAACA,QAAQ;QACX,IAAIC,UAAUJ;QACd,IAAIK,QAAQ;QACZ,MAAMC,qBAAqBL,kBAAkBM,KAAK,CAAC;QACnD,KAAK,MAAMC,OAAOF,mBAAoB;YACpC,IAAIF,OAAO,CAACI,IAAI,KAAKzB,WAAW;gBAC9BqB,UAAUA,OAAO,CAACI,IAAI;YACxB,OAAO;gBACLH,QAAQ;gBACR;YACF;QACF;QACA,IAAIA,OAAO;YACTtD,IAAI0D,IAAI,CAACP;QACX;IACF;AACF;AAEA,OAAO,SAASQ,wCACdV,MAAkB,EAClBW,MAAc,EACdC,MAAc,EACdC,cAAsB,EACtBV,MAAe;IAEf,IAAIH,OAAOc,YAAY,IAAIH,UAAUX,OAAOc,YAAY,EAAE;QACxD,IAAI,CAACX,QAAQ;YACXpD,IAAI0D,IAAI,CACN,CAAC,EAAE,EAAEE,OAAO,yCAAyC,CAAC,GACnDC,CAAAA,OAAOlC,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAEkC,OAAO,EAAE,CAAC,GAAG,EAAC,IACrD,CAAC,qBAAqB,EAAEC,eAAe,kBAAkB,CAAC;QAEhE;QAEA,IAAIT,UAAUJ;QACd,MAAMe,UAAUH,OAAOL,KAAK,CAAC;QAC7B,MAAOQ,QAAQ1C,MAAM,GAAG,EAAG;YACzB,MAAMmC,MAAMO,QAAQC,KAAK;YACzBZ,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACI,IAAI,IAAI,CAAC;YAChCJ,UAAUA,OAAO,CAACI,IAAI;QACxB;QACAJ,OAAO,CAACW,QAAQC,KAAK,GAAI,GAAG,AAAChB,OAAOc,YAAY,AAAQ,CAACH,OAAO;IAClE;IAEA,OAAOX;AACT;AAEA,SAASiB,eACPC,GAAW,EACXC,UAAkC,EAClChB,MAAe;QA4FXjD,6BASFkE,sBAgDgBA,uBAiJPA,uBA4EFA,oCAAAA,uBAmCPA,uBAcEA,uBAQAA,uBAKCA,uBA2LDA,uBAoCFA;IA7oBF,MAAMP,iBAAiBM,WAAWN,cAAc;IAChD,IAAI,OAAOM,WAAWE,mBAAmB,KAAK,aAAa;QACzD,IAAI,CAAClB,QAAQ;YACXpD,IAAI0D,IAAI,CACN,CAAC,yFAAyF,EAAEI,eAAe,CAAC,CAAC;QAEjH;QACA,IAAI,OAAOM,WAAWG,aAAa,KAAK,aAAa;YACnDH,WAAWG,aAAa,GAAGH,WAAWE,mBAAmB;QAC3D;QACA,OAAOF,WAAWE,mBAAmB;IACvC;IAEA,MAAMrB,SAASuB,OAAOC,IAAI,CAACL,YAAY5C,MAAM,CAC3C,CAACkD,eAAejB;QACd,MAAMkB,QAAQP,UAAU,CAACX,IAAI;QAE7B,IAAIkB,UAAU3C,aAAa2C,UAAU,MAAM;YACzC,OAAOD;QACT;QAEA,IAAIjB,QAAQ,WAAW;YACrB,IAAI,OAAOkB,UAAU,UAAU;gBAC7B,MAAM,IAAIC,MACR,CAAC,+CAA+C,EAAE,OAAOD,MAAM,CAAC,CAAC;YAErE;YACA,MAAME,cAAcF,MAAMG,IAAI;YAE9B,qEAAqE;YACrE,eAAe;YACf,IAAID,gBAAgB,UAAU;gBAC5B,MAAM,IAAID,MACR,CAAC,4IAA4I,CAAC;YAElJ;YACA,2EAA2E;YAC3E,8CAA8C;YAC9C,IAAIC,YAAYvD,MAAM,KAAK,GAAG;gBAC5B,MAAM,IAAIsD,MACR,CAAC,8GAA8G,CAAC;YAEpH;QACF;QAEA,IAAInB,QAAQ,kBAAkB;YAC5B,IAAI,CAACsB,MAAMC,OAAO,CAACL,QAAQ;gBACzB,MAAM,IAAIC,MACR,CAAC,4DAA4D,EAAED,MAAM,0CAA0C,CAAC;YAEpH;YAEA,IAAI,CAACA,MAAMrD,MAAM,EAAE;gBACjB,MAAM,IAAIsD,MACR,CAAC,uGAAuG,CAAC;YAE7G;YAEAD,MAAM/B,OAAO,CAAC,CAACqC;gBACb,IAAI,OAAOA,QAAQ,UAAU;oBAC3B,MAAM,IAAIL,MACR,CAAC,4DAA4D,EAAEK,IAAI,WAAW,EAAE,OAAOA,IAAI,0CAA0C,CAAC;gBAE1I;YACF;QACF;QAEA,IAAI,CAAC,CAACN,SAASA,MAAMO,WAAW,KAAKV,QAAQ;YAC3CE,aAAa,CAACjB,IAAI,GAAG;gBACnB,GAAGtD,aAAa,CAACsD,IAAI;gBACrB,GAAGe,OAAOC,IAAI,CAACE,OAAOnD,MAAM,CAAM,CAAC2D,GAAGC;oBACpC,MAAMC,IAAIV,KAAK,CAACS,EAAE;oBAClB,IAAIC,MAAMrD,aAAaqD,MAAM,MAAM;wBACjCF,CAAC,CAACC,EAAE,GAAGC;oBACT;oBACA,OAAOF;gBACT,GAAG,CAAC,EAAE;YACR;QACF,OAAO;YACLT,aAAa,CAACjB,IAAI,GAAGkB;QACvB;QAEA,OAAOD;IACT,GACA,CAAC;IAGH,2CAA2C;IAC3C,uEAAuE;IACvE,6CAA6C;IAC7C,KAAIvE,8BAAAA,cAAc4D,YAAY,qBAA1B5D,4BAA4BmF,GAAG,EAAE;QACnCtF,IAAI0D,IAAI,CACN,CAAC,2HAA2H,CAAC;IAEjI;IAEA,MAAMW,SAAS;QAAE,GAAGlE,aAAa;QAAE,GAAG8C,MAAM;IAAC;IAE7C,IACEoB,EAAAA,uBAAAA,OAAON,YAAY,qBAAnBM,qBAAqBiB,GAAG,KACxB,CAACC,QAAQC,GAAG,CAACC,cAAc,CAAE9D,QAAQ,CAAC,aACtC,CAAC4D,QAAQC,GAAG,CAACE,gBAAgB,EAC7B;QACA,MAAM,IAAId,MACR,CAAC,0KAA0K,CAAC;IAEhL;IAEA,IAAIP,OAAOsB,MAAM,KAAK,UAAU;QAC9B,IAAItB,OAAOuB,IAAI,EAAE;YACf,MAAM,IAAIhB,MACR;QAEJ;QAEA,IAAI,CAAC3D,gBAAgB;YACnB,IAAIoD,OAAOwB,QAAQ,EAAE;gBACnB7F,IAAI0D,IAAI,CACN;YAEJ;YACA,IAAIW,OAAOyB,SAAS,EAAE;gBACpB9F,IAAI0D,IAAI,CACN;YAEJ;YACA,IAAIW,OAAO0B,OAAO,EAAE;gBAClB/F,IAAI0D,IAAI,CACN;YAEJ;QACF;IACF;IAEA,IAAI,OAAOW,OAAO2B,WAAW,KAAK,UAAU;QAC1C,MAAM,IAAIpB,MACR,CAAC,mDAAmD,EAAE,OAAOP,OAAO2B,WAAW,CAAC,sDAAsD,CAAC;IAE3I;IAEA,IAAI,OAAO3B,OAAO4B,QAAQ,KAAK,UAAU;QACvC,MAAM,IAAIrB,MACR,CAAC,gDAAgD,EAAE,OAAOP,OAAO4B,QAAQ,CAAC,CAAC,CAAC;IAEhF;IAEA,kDAAkD;IAClD,IAAIlB,MAAMC,OAAO,EAACX,wBAAAA,OAAON,YAAY,qBAAnBM,sBAAqB6B,wBAAwB,GAAG;QAChE,IAAI,CAAC7B,OAAON,YAAY,EAAE;YACxBM,OAAON,YAAY,GAAG,CAAC;QACzB;QACA,IAAI,CAACM,OAAON,YAAY,CAACoC,yBAAyB,EAAE;YAClD9B,OAAON,YAAY,CAACoC,yBAAyB,GAAG,CAAC;QACnD;QACA,IAAI,CAAC9B,OAAON,YAAY,CAACoC,yBAAyB,CAAC,OAAO,EAAE;YAC1D9B,OAAON,YAAY,CAACoC,yBAAyB,CAAC,OAAO,GAAG,EAAE;QAC5D;QACA9B,OAAON,YAAY,CAACoC,yBAAyB,CAAC,OAAO,CAACpD,IAAI,IACpDsB,OAAON,YAAY,CAACmC,wBAAwB,IAAI,EAAE;QAExDlG,IAAI0D,IAAI,CACN,CAAC,8GAA8G,EAAEI,eAAe,kBAAkB,CAAC;IAEvJ;IAEA,IAAIO,OAAO4B,QAAQ,KAAK,IAAI;QAC1B,IAAI5B,OAAO4B,QAAQ,KAAK,KAAK;YAC3B,MAAM,IAAIrB,MACR,CAAC,iFAAiF,CAAC;QAEvF;QAEA,IAAI,CAACP,OAAO4B,QAAQ,CAACG,UAAU,CAAC,MAAM;YACpC,MAAM,IAAIxB,MACR,CAAC,iDAAiD,EAAEP,OAAO4B,QAAQ,CAAC,CAAC,CAAC;QAE1E;QAEA,IAAI5B,OAAO4B,QAAQ,KAAK,KAAK;gBAWvB5B;YAVJ,IAAIA,OAAO4B,QAAQ,CAACI,QAAQ,CAAC,MAAM;gBACjC,MAAM,IAAIzB,MACR,CAAC,iDAAiD,EAAEP,OAAO4B,QAAQ,CAAC,CAAC,CAAC;YAE1E;YAEA,IAAI5B,OAAO2B,WAAW,KAAK,IAAI;gBAC7B3B,OAAO2B,WAAW,GAAG3B,OAAO4B,QAAQ;YACtC;YAEA,IAAI5B,EAAAA,cAAAA,OAAOiC,GAAG,qBAAVjC,YAAYkC,aAAa,MAAK,IAAI;gBACpClC,OAAOiC,GAAG,CAACC,aAAa,GAAGlC,OAAO4B,QAAQ;YAC5C;QACF;IACF;IAEA,IAAI5B,0BAAAA,OAAQmC,MAAM,EAAE;QAClB,MAAMA,SAAsBnC,OAAOmC,MAAM;QAEzC,IAAI,OAAOA,WAAW,UAAU;YAC9B,MAAM,IAAI5B,MACR,CAAC,8CAA8C,EAAE,OAAO4B,OAAO,6EAA6E,CAAC;QAEjJ;QAEA,IAAIA,OAAOC,cAAc,EAAE;gBAUrBxD;YATJ,IAAI,CAAC8B,MAAMC,OAAO,CAACwB,OAAOC,cAAc,GAAG;gBACzC,MAAM,IAAI7B,MACR,CAAC,4DAA4D,EAAE,OAAO4B,OAAOC,cAAc,CAAC,6EAA6E,CAAC;YAE9K;YAEA,4DAA4D;YAC5D,2DAA2D;YAC3D,gBAAgB;YAChB,KAAIxD,sBAAAA,OAAO+C,WAAW,qBAAlB/C,oBAAoBmD,UAAU,CAAC,SAAS;gBAC1C,IAAI;oBACF,MAAMM,MAAM,IAAIC,IAAI1D,OAAO+C,WAAW;oBACtC,MAAMY,yBAAyBJ,OAAOC,cAAc,CAACI,IAAI,CAAC,CAACC,UACzDjG,mBAAmBiG,SAASJ;oBAG9B,oEAAoE;oBACpE,IAAI,CAACE,wBAAwB;4BAC3BJ;yBAAAA,yBAAAA,OAAOC,cAAc,qBAArBD,uBAAuBzD,IAAI,CAAC;4BAC1BgE,UAAUL,IAAIK,QAAQ;4BACtBC,UAAUN,IAAIM,QAAQ,CAACC,OAAO,CAAC,MAAM;4BACrCC,MAAMR,IAAIQ,IAAI;wBAChB;oBACF;gBACF,EAAE,OAAO7E,OAAO;oBACd,MAAM,IAAIuC,MACR,CAAC,8CAA8C,EAAEvC,MAAM,CAAC;gBAE5D;YACF;QACF;QAEA,IAAImE,OAAOW,OAAO,EAAE;YAClB,IAAI,CAACpC,MAAMC,OAAO,CAACwB,OAAOW,OAAO,GAAG;gBAClC,MAAM,IAAIvC,MACR,CAAC,qDAAqD,EAAE,OAAO4B,OAAOW,OAAO,CAAC,6EAA6E,CAAC;YAEhK;QACF;QAEA,IAAI,CAACX,OAAOY,MAAM,EAAE;YAClBZ,OAAOY,MAAM,GAAG;QAClB;QAEA,IACEZ,OAAOY,MAAM,KAAK,aAClBZ,OAAOY,MAAM,KAAK,YAClBZ,OAAOnF,IAAI,KAAKf,mBAAmBe,IAAI,EACvC;YACA,MAAM,IAAIuD,MACR,CAAC,kCAAkC,EAAE4B,OAAOY,MAAM,CAAC,sKAAsK,CAAC;QAE9N;QAEA,IACEZ,OAAOnF,IAAI,KAAKf,mBAAmBe,IAAI,IACvCgD,OAAO4B,QAAQ,IACf,CAACrF,cAAc4F,OAAOnF,IAAI,EAAEgD,OAAO4B,QAAQ,GAC3C;YACAO,OAAOnF,IAAI,GAAG,CAAC,EAAEgD,OAAO4B,QAAQ,CAAC,EAAEO,OAAOnF,IAAI,CAAC,CAAC;QAClD;QAEA,8EAA8E;QAC9E,IACEmF,OAAOnF,IAAI,IACX,CAACmF,OAAOnF,IAAI,CAACgF,QAAQ,CAAC,QACrBG,CAAAA,OAAOY,MAAM,KAAK,aAAa/C,OAAOE,aAAa,AAAD,GACnD;YACAiC,OAAOnF,IAAI,IAAI;QACjB;QAEA,IAAImF,OAAOa,UAAU,EAAE;YACrB,IAAIb,OAAOY,MAAM,KAAK,aAAaZ,OAAOY,MAAM,KAAK,UAAU;gBAC7D,MAAM,IAAIxC,MACR,CAAC,kCAAkC,EAAE4B,OAAOY,MAAM,CAAC,uFAAuF,CAAC;YAE/I;YACA,MAAME,eAAe5H,KAAKyE,KAAKqC,OAAOa,UAAU;YAChD,IAAI,CAAC9H,WAAW+H,eAAe;gBAC7B,MAAM,IAAI1C,MACR,CAAC,+CAA+C,EAAE0C,aAAa,EAAE,CAAC;YAEtE;YACAd,OAAOa,UAAU,GAAGC;QACtB;IACF;IAEA,IAAI,SAAOjD,wBAAAA,OAAON,YAAY,qBAAnBM,sBAAqBkD,aAAa,MAAK,WAAW;QAC3D,0CAA0C;QAC1CvE,4BACEqB,QACA,8BACA,2GACAjB;IAEJ;IAEA,IAAIiB,OAAOmD,SAAS,KAAK,OAAO;QAC9B,0CAA0C;QAC1CxE,4BACEqB,QACA,aACA,uKACAjB;IAEJ;IAEA,IAAIiB,OAAOoD,iBAAiB,KAAK,OAAO;QACtC,0CAA0C;QAC1CzE,4BACEqB,QACA,qBACA,6KACAjB;IAEJ;IAEAO,wCACEU,QACA,SACA,kBACAP,gBACAV;IAEFO,wCACEU,QACA,oBACA,6BACAP,gBACAV;IAEFO,wCACEU,QACA,WACA,oBACAP,gBACAV;IAEFO,wCACEU,QACA,yBACA,kCACAP,gBACAV;IAEFO,wCACEU,QACA,iBACA,0BACAP,gBACAV;IAGF,IAAI,AAACiB,OAAON,YAAY,CAAS2D,gBAAgB,EAAE;QACjD,IAAI,CAACtE,QAAQ;YACXpD,IAAI0D,IAAI,CACN,CAAC,iGAAiG,CAAC;QAEvG;QACAW,OAAOsB,MAAM,GAAG;IAClB;IAEA,IACE,SAAOtB,wBAAAA,OAAON,YAAY,sBAAnBM,qCAAAA,sBAAqBkD,aAAa,qBAAlClD,mCAAoCsD,aAAa,MAAK,aAC7D;YAEEtD;QADF,MAAMM,QAAQiD,UACZvD,sCAAAA,OAAON,YAAY,CAACwD,aAAa,qBAAjClD,oCAAmCsD,aAAa,CAACE,QAAQ;QAE3D,IAAIC,MAAMnD,UAAUA,QAAQ,GAAG;YAC7B,MAAM,IAAIC,MACR;QAEJ;IACF;IAEAjB,wCACEU,QACA,qBACA,qBACAP,gBACAV;IAEFO,wCACEU,QACA,8BACA,8BACAP,gBACAV;IAEFO,wCACEU,QACA,6BACA,6BACAP,gBACAV;IAGF,IACEiB,EAAAA,wBAAAA,OAAON,YAAY,qBAAnBM,sBAAqB0D,qBAAqB,KAC1C,CAACnI,WAAWyE,OAAON,YAAY,CAACgE,qBAAqB,GACrD;QACA1D,OAAON,YAAY,CAACgE,qBAAqB,GAAGlI,QAC1CwE,OAAON,YAAY,CAACgE,qBAAqB;QAE3C,IAAI,CAAC3E,QAAQ;YACXpD,IAAI0D,IAAI,CACN,CAAC,8DAA8D,EAAEW,OAAON,YAAY,CAACgE,qBAAqB,CAAC,CAAC;QAEhH;IACF;IAEA,6BAA6B;IAC7B,IAAI1D,EAAAA,wBAAAA,OAAON,YAAY,qBAAnBM,sBAAqB2D,eAAe,KAAIzC,QAAQC,GAAG,CAACyC,kBAAkB,EAAE;QAC1E,IAAI,CAAC5D,OAAON,YAAY,EAAE;YACxBM,OAAON,YAAY,GAAG,CAAC;QACzB;QACAM,OAAON,YAAY,CAACmE,YAAY,GAAG3C,QAAQC,GAAG,CAACyC,kBAAkB;IACnE;IAEA,uCAAuC;IACvC,KAAI5D,wBAAAA,OAAON,YAAY,qBAAnBM,sBAAqB8D,4BAA4B,EAAE;QACrD9D,OAAON,YAAY,CAACiE,eAAe,GAAG;IACxC;IAEA,2CAA2C;IAC3C,IAAI,GAAC3D,wBAAAA,OAAON,YAAY,qBAAnBM,sBAAqB0D,qBAAqB,GAAE;QAC/C,IAAIK,UAAU1H,YAAYyD;QAE1B,IAAIiE,SAAS;YACX,IAAI,CAAC/D,OAAON,YAAY,EAAE;gBACxBM,OAAON,YAAY,GAAG,CAAC;YACzB;YACA,IAAI,CAAC5D,cAAc4D,YAAY,EAAE;gBAC/B5D,cAAc4D,YAAY,GAAG,CAAC;YAChC;YACAM,OAAON,YAAY,CAACgE,qBAAqB,GAAGK;YAC5CjI,cAAc4D,YAAY,CAACgE,qBAAqB,GAC9C1D,OAAON,YAAY,CAACgE,qBAAqB;QAC7C;IACF;IAEA,IAAI1D,OAAOsB,MAAM,KAAK,gBAAgB,CAACtB,OAAOoD,iBAAiB,EAAE;QAC/D,IAAI,CAACrE,QAAQ;YACXpD,IAAI0D,IAAI,CACN,CAAC,mHAAmH,CAAC;QAEzH;QACAW,OAAOsB,MAAM,GAAG3D;IAClB;IAEArB,6BAA6B0D,UAAUlE;IAEvC,IAAIkE,OAAOuB,IAAI,EAAE;QACf,MAAM,EAAEA,IAAI,EAAE,GAAGvB;QACjB,MAAMgE,WAAW,OAAOzC;QAExB,IAAIyC,aAAa,UAAU;YACzB,MAAM,IAAIzD,MACR,CAAC,4CAA4C,EAAEyD,SAAS,2EAA2E,CAAC;QAExI;QAEA,IAAI,CAACtD,MAAMC,OAAO,CAACY,KAAK0C,OAAO,GAAG;YAChC,MAAM,IAAI1D,MACR,CAAC,mDAAmD,EAAE,OAAOgB,KAAK0C,OAAO,CAAC,2EAA2E,CAAC;QAE1J;QAEA,IAAI1C,KAAK0C,OAAO,CAAChH,MAAM,GAAG,OAAO,CAAC8B,QAAQ;YACxCpD,IAAI0D,IAAI,CACN,CAAC,SAAS,EAAEkC,KAAK0C,OAAO,CAAChH,MAAM,CAAC,mLAAmL,CAAC;QAExN;QAEA,MAAMiH,oBAAoB,OAAO3C,KAAK4C,aAAa;QAEnD,IAAI,CAAC5C,KAAK4C,aAAa,IAAID,sBAAsB,UAAU;YACzD,MAAM,IAAI3D,MACR,CAAC,0HAA0H,CAAC;QAEhI;QAEA,IAAI,OAAOgB,KAAKuB,OAAO,KAAK,eAAe,CAACpC,MAAMC,OAAO,CAACY,KAAKuB,OAAO,GAAG;YACvE,MAAM,IAAIvC,MACR,CAAC,2IAA2I,EAAE,OAAOgB,KAAKuB,OAAO,CAAC,2EAA2E,CAAC;QAElP;QAEA,IAAIvB,KAAKuB,OAAO,EAAE;YAChB,MAAMsB,qBAAqB7C,KAAKuB,OAAO,CAACuB,MAAM,CAAC,CAACC;oBAYf/C;gBAX/B,IAAI,CAAC+C,QAAQ,OAAOA,SAAS,UAAU,OAAO;gBAC9C,IAAI,CAACA,KAAKH,aAAa,EAAE,OAAO;gBAChC,IAAI,CAACG,KAAKC,MAAM,IAAI,OAAOD,KAAKC,MAAM,KAAK,UAAU,OAAO;gBAE5D,IAAID,KAAKC,MAAM,CAACjH,QAAQ,CAAC,MAAM;oBAC7BkH,QAAQnF,IAAI,CACV,CAAC,cAAc,EAAEiF,KAAKC,MAAM,CAAC,2GAA2G,CAAC;oBAE3I,OAAO;gBACT;gBAEA,MAAME,0BAAyBlD,gBAAAA,KAAKuB,OAAO,qBAAZvB,cAAcmD,IAAI,CAC/C,CAACC,UACCA,QAAQR,aAAa,KAAKG,KAAKH,aAAa,IAC5CQ,QAAQJ,MAAM,KAAKD,KAAKC,MAAM;gBAGlC,IAAI,CAACxF,UAAU0F,wBAAwB;oBACrCD,QAAQnF,IAAI,CACV,CAAC,KAAK,EAAEiF,KAAKC,MAAM,CAAC,KAAK,EAAEE,uBAAuBF,MAAM,CAAC,8BAA8B,EAAED,KAAKH,aAAa,CAAC,+DAA+D,CAAC;oBAE9K,OAAO;gBACT;gBAEA,IAAIS,mBAAmB;gBAEvB,IAAIlE,MAAMC,OAAO,CAAC2D,KAAKL,OAAO,GAAG;oBAC/B,KAAK,MAAMY,UAAUP,KAAKL,OAAO,CAAE;wBACjC,IAAI,OAAOY,WAAW,UAAUD,mBAAmB;wBAEnD,KAAK,MAAME,cAAcvD,KAAKuB,OAAO,IAAI,EAAE,CAAE;4BAC3C,IAAIgC,eAAeR,MAAM;4BACzB,IAAIQ,WAAWb,OAAO,IAAIa,WAAWb,OAAO,CAAC3G,QAAQ,CAACuH,SAAS;gCAC7DL,QAAQnF,IAAI,CACV,CAAC,KAAK,EAAEiF,KAAKC,MAAM,CAAC,KAAK,EAAEO,WAAWP,MAAM,CAAC,wBAAwB,EAAEM,OAAO,sEAAsE,CAAC;gCAEvJD,mBAAmB;gCACnB;4BACF;wBACF;oBACF;gBACF;gBAEA,OAAOA;YACT;YAEA,IAAIR,mBAAmBnH,MAAM,GAAG,GAAG;gBACjC,MAAM,IAAIsD,MACR,CAAC,8BAA8B,EAAE6D,mBAC9B9F,GAAG,CAAC,CAACgG,OAAcS,KAAKC,SAAS,CAACV,OAClCjJ,IAAI,CACH,MACA,8KAA8K,CAAC;YAEvL;QACF;QAEA,IAAI,CAACqF,MAAMC,OAAO,CAACY,KAAK0C,OAAO,GAAG;YAChC,MAAM,IAAI1D,MACR,CAAC,2FAA2F,EAAE,OAAOgB,KAAK0C,OAAO,CAAC,2EAA2E,CAAC;QAElM;QAEA,MAAMgB,iBAAiB1D,KAAK0C,OAAO,CAACI,MAAM,CACxC,CAACQ,SAAgB,OAAOA,WAAW;QAGrC,IAAII,eAAehI,MAAM,GAAG,GAAG;YAC7B,MAAM,IAAIsD,MACR,CAAC,gDAAgD,EAAE0E,eAChD3G,GAAG,CAAC4G,QACJ7J,IAAI,CACH,MACA,wEAAwE,CAAC,GAC3E,CAAC,+HAA+H,CAAC;QAEvI;QAEA,IAAI,CAACkG,KAAK0C,OAAO,CAAC3G,QAAQ,CAACiE,KAAK4C,aAAa,GAAG;YAC9C,MAAM,IAAI5D,MACR,CAAC,0IAA0I,CAAC;QAEhJ;QAEA,MAAM4E,oBAAoB,IAAIC;QAC9B,MAAMC,mBAAmB,IAAID;QAE7B7D,KAAK0C,OAAO,CAAC1F,OAAO,CAAC,CAACsG;YACpB,MAAMS,cAAcT,OAAOU,WAAW;YACtC,IAAIJ,kBAAkBK,GAAG,CAACF,cAAc;gBACtCD,iBAAiBI,GAAG,CAACZ;YACvB;YACAM,kBAAkBM,GAAG,CAACH;QACxB;QAEA,IAAID,iBAAiBK,IAAI,GAAG,GAAG;YAC7B,MAAM,IAAInF,MACR,CAAC,kEAAkE,CAAC,GAClE,CAAC,EAAE;mBAAI8E;aAAiB,CAAChK,IAAI,CAAC,MAAM,EAAE,CAAC,GACvC,CAAC,yCAAyC,CAAC,GAC3C,CAAC,wEAAwE,CAAC;QAEhF;QAEA,2CAA2C;QAC3CkG,KAAK0C,OAAO,GAAG;YACb1C,KAAK4C,aAAa;eACf5C,KAAK0C,OAAO,CAACI,MAAM,CAAC,CAACQ,SAAWA,WAAWtD,KAAK4C,aAAa;SACjE;QAED,MAAMwB,sBAAsB,OAAOpE,KAAKqE,eAAe;QAEvD,IACED,wBAAwB,aACxBA,wBAAwB,aACxB;YACA,MAAM,IAAIpF,MACR,CAAC,yEAAyE,EAAEoF,oBAAoB,2EAA2E,CAAC;QAEhL;IACF;IAEA,KAAI3F,wBAAAA,OAAO6F,aAAa,qBAApB7F,sBAAsB8F,qBAAqB,EAAE;QAC/C,MAAM,EAAEA,qBAAqB,EAAE,GAAG9F,OAAO6F,aAAa;QACtD,MAAME,gBAAgB;YACpB;YACA;YACA;YACA;SACD;QAED,IAAI,CAACA,cAAczI,QAAQ,CAACwI,wBAAwB;YAClD,MAAM,IAAIvF,MACR,CAAC,uEAAuE,EAAEwF,cAAc1K,IAAI,CAC1F,MACA,WAAW,EAAEyK,sBAAsB,CAAC;QAE1C;IACF;IAEA,MAAME,gCAAgChG,OAAOiG,iBAAiB;IAC9D,kJAAkJ;IAClJ,6EAA6E;IAC7EjG,OAAOiG,iBAAiB,GAAG;QACzB,GAAID,iCAAiC,CAAC,CAAC;QACvC,gFAAgF;QAChF,uBAAuB;YACrBE,WAAW;QACb;QACAC,QAAQ;YACND,WAAW;QACb;QACA,eAAe;YACbA,WAAW;QACb;IACF;IAEA,MAAME,qCACJpG,EAAAA,wBAAAA,OAAON,YAAY,qBAAnBM,sBAAqBqG,sBAAsB,KAAI,EAAE;IACnD,IAAI,CAACrG,OAAON,YAAY,EAAE;QACxBM,OAAON,YAAY,GAAG,CAAC;IACzB;IACAM,OAAON,YAAY,CAAC2G,sBAAsB,GAAG;WACxC,IAAIjB,IAAI;eACNgB;YACH;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,4EAA4E;YAC5E,mCAAmC;YACnC,0EAA0E;YAC1E,wBAAwB;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;KACF;IAED,OAAOpG;AACT;AAEA,eAAe,eAAesG,WAC5BC,KAAa,EACbzG,GAAW,EACX,EACE0G,YAAY,EACZC,SAAS,EACT1H,SAAS,IAAI,EACb2H,gBAAgB,EAMjB,GAAG,CAAC,CAAC;IAEN,IAAI,CAACxF,QAAQC,GAAG,CAACwF,4BAA4B,EAAE;QAC7C,IAAI;YACF3K;QACF,EAAE,OAAO4K,KAAK;YACZ,gDAAgD;YAChD,yBAAyB;YACzB,IAAI,CAAC1F,QAAQC,GAAG,CAAC0F,gCAAgC,EAAE;gBACjD,MAAMD;YACR;QACF;IACF;IAEA,IAAI1F,QAAQC,GAAG,CAAC0F,gCAAgC,EAAE;QAChD,OAAO9B,KAAK+B,KAAK,CAAC5F,QAAQC,GAAG,CAAC0F,gCAAgC;IAChE;IAEA,2EAA2E;IAC3E,2DAA2D;IAC3D,8EAA8E;IAC9E,8BAA8B;IAC9B,yEAAyE;IACzE,mEAAmE;IACnE,IAAI3F,QAAQC,GAAG,CAAC4F,mCAAmC,EAAE;QACnD,OAAOhC,KAAK+B,KAAK,CAAC5F,QAAQC,GAAG,CAAC4F,mCAAmC;IACnE;IAEA,MAAMC,SAASjI,SACX;QACEM,MAAM,KAAO;QACb4H,MAAM,KAAO;QACbjJ,OAAO,KAAO;IAChB,IACArC;IAEJO,cAAc4D,KAAKyG,UAAU1K,0BAA0BmL;IAEvD,IAAIvH,iBAAiB;IAErB,IAAI+G,cAAc;QAChB,OAAO3G,eACLC,KACA;YACEoH,cAAc;YACdzH;YACA,GAAG+G,YAAY;QACjB,GACAzH;IAEJ;IAEA,MAAM/B,OAAO,MAAMtB,OAAOE,cAAc;QAAEuL,KAAKrH;IAAI;IAEnD,2BAA2B;IAC3B,IAAI9C,wBAAAA,KAAMC,MAAM,EAAE;YAkFZ8C,iBAUFA,gCAAAA,0BACCA,iCAAAA;QA5FHN,iBAAiBtE,SAAS6B;QAC1B,IAAIoK;QAEJ,IAAI;YACF,MAAMC,YAAYlH,OAAOmH,MAAM,CAAC,CAAC,GAAGpG,QAAQC,GAAG;YAE/C,uEAAuE;YACvE,sEAAsE;YACtE,8BAA8B;YAC9B,IAAID,QAAQC,GAAG,CAACE,gBAAgB,KAAK,QAAQ;gBAC3C,4DAA4D;gBAC5D,0DAA0D;gBAC1D,8CAA8C;gBAC9C+F,mBAAmBG,QAAQvK;YAC7B,OAAO;gBACLoK,mBAAmB,MAAM,MAAM,CAAC3L,cAAcuB,MAAMwK,IAAI;YAC1D;YACA,MAAMC,SAA6B,CAAC;YAEpC,KAAK,MAAMrI,OAAOe,OAAOC,IAAI,CAACc,QAAQC,GAAG,EAAG;gBAC1C,IAAIkG,SAAS,CAACjI,IAAI,KAAK8B,QAAQC,GAAG,CAAC/B,IAAI,EAAE;oBACvCqI,MAAM,CAACrI,IAAI,GAAG8B,QAAQC,GAAG,CAAC/B,IAAI;gBAChC;YACF;YACAjD,iBAAiBsL;YAEjB,IAAIhB,WAAW;gBACb,OAAOW;YACT;QACF,EAAE,OAAOR,KAAK;YACZI,OAAOhJ,KAAK,CACV,CAAC,eAAe,EAAEyB,eAAe,uEAAuE,CAAC;YAE3G,MAAMmH;QACR;QACA,MAAM7G,aAAa,MAAMhE,gBACvBwK,OACAa,iBAAiBM,OAAO,IAAIN;QAG9B,IAAI,CAAClG,QAAQC,GAAG,CAACwG,YAAY,EAAE;YAC7B,iEAAiE;YACjE,MAAM,EAAEC,YAAY,EAAE,GACpBL,QAAQ;YACV,MAAMM,QAAQD,aAAaE,SAAS,CAAC/H;YAErC,IAAI,CAAC8H,MAAME,OAAO,EAAE;gBAClB,uBAAuB;gBACvB,MAAM3J,WAAW;oBAAC,CAAC,QAAQ,EAAEqB,eAAe,mBAAmB,CAAC;iBAAC;gBAEjE,MAAM,CAACuI,eAAe/J,WAAW,GAAGF,mBAAmB8J,MAAM7J,KAAK;gBAClE,kBAAkB;gBAClB,KAAK,MAAMA,SAASgK,cAAe;oBACjC5J,SAASM,IAAI,CAAC,CAAC,IAAI,EAAEV,MAAM,CAAC;gBAC9B;gBAEA,uBAAuB;gBACvBI,SAASM,IAAI,CACX;gBAGF,IAAIT,YAAY;oBACd,KAAK,MAAMlB,WAAWqB,SAAU;wBAC9BoG,QAAQxG,KAAK,CAACjB;oBAChB;oBACA,MAAMX,aAAa;gBACrB,OAAO;oBACL,KAAK,MAAMW,WAAWqB,SAAU;wBAC9B4I,OAAO3H,IAAI,CAACtC;oBACd;gBACF;YACF;QACF;QAEA,IAAIgD,WAAWkI,MAAM,IAAIlI,WAAWkI,MAAM,KAAK,UAAU;YACvD,MAAM,IAAI1H,MACR,CAAC,gDAAgD,EAAEd,eAAe,GAAG,CAAC,GACpE;QAEN;QAEA,KAAIM,kBAAAA,WAAWkC,GAAG,qBAAdlC,gBAAgBmC,aAAa,EAAE;YACjC,MAAM,EAAEA,aAAa,EAAE,GAAGnC,WAAWkC,GAAG,IAAK,CAAC;YAC9ClC,WAAWkC,GAAG,GAAGlC,WAAWkC,GAAG,IAAI,CAAC;YACpClC,WAAWkC,GAAG,CAACC,aAAa,GAC1B,AAACA,CAAAA,cAAcF,QAAQ,CAAC,OACpBE,cAAcgG,KAAK,CAAC,GAAG,CAAC,KACxBhG,aAAY,KAAM;QAC1B;QAEA,IACEnC,EAAAA,2BAAAA,WAAWL,YAAY,sBAAvBK,iCAAAA,yBAAyBoI,KAAK,qBAA9BpI,+BAAgCqI,OAAO,KACvC,GAACrI,4BAAAA,WAAWL,YAAY,sBAAvBK,kCAAAA,0BAAyBoI,KAAK,qBAA9BpI,gCAAgCsI,KAAK,GACtC;YACArB,OAAO3H,IAAI,CACT,sIACE,uFACA,4FACA;YAGJ,MAAMgJ,QAA2C,CAAC;YAClD,KAAK,MAAM,CAACzH,KAAKwH,QAAQ,IAAIjI,OAAOmI,OAAO,CACzCvI,WAAWL,YAAY,CAACyI,KAAK,CAACC,OAAO,EACpC;gBACDC,KAAK,CAAC,MAAMzH,IAAI,GAAGwH;YACrB;YAEArI,WAAWL,YAAY,CAACyI,KAAK,CAACE,KAAK,GAAGA;QACxC;QAEA3B,oCAAAA,iBAAmB3G;QACnB,MAAMwI,iBAAiB1I,eACrBC,KACA;YACEoH,cAAc5L,SAASwE,KAAK9C;YAC5BwL,YAAYxL;YACZyC;YACA,GAAGM,UAAU;QACf,GACAhB;QAEF,OAAOwJ;IACT,OAAO;QACL,MAAME,iBAAiBtN,SAASS,YAAY,CAAC,EAAE,EAAER,QAAQQ,YAAY,CAAC,EAAE;QACxE,MAAM8M,YAAYhN,OAAOiN,IAAI,CAC3B;YACE,CAAC,EAAEF,eAAe,IAAI,CAAC;YACvB,CAAC,EAAEA,eAAe,GAAG,CAAC;YACtB,CAAC,EAAEA,eAAe,IAAI,CAAC;YACvB,CAAC,EAAEA,eAAe,KAAK,CAAC;SACzB,EACD;YAAEtB,KAAKrH;QAAI;QAEb,IAAI4I,6BAAAA,UAAWzL,MAAM,EAAE;YACrB,MAAM,IAAIsD,MACR,CAAC,yBAAyB,EAAEpF,SAC1BuN,WACA,uFAAuF,CAAC;QAE9F;IACF;IAEA,qDAAqD;IACrD,iEAAiE;IACjE,MAAMH,iBAAiB1I,eACrBC,KACAhE,eACAiD;IAEFwJ,eAAe9I,cAAc,GAAGA;IAChCnD,6BAA6BiM;IAC7B,OAAOA;AACT;AAEA,OAAO,SAASK,+BACdC,0BAAsD;IAEtD,MAAMC,qBAAmD,EAAE;IAE3D,IAAI,CAACD,4BAA4B,OAAOC;IAExC,uEAAuE;IACvE,+CAA+C;IAC/C,IAAIhN,cAAc4D,YAAY,EAAE;QAC9B,KAAK,MAAMqJ,eAAe5I,OAAOC,IAAI,CACnCyI,4BACiC;YACjC,IACEE,eAAejN,cAAc4D,YAAY,IACzCmJ,0BAA0B,CAACE,YAAY,KACrCjN,cAAc4D,YAAY,CAACqJ,YAAY,EACzC;gBACAD,mBAAmBpK,IAAI,CAACqK;YAC1B;QACF;IACF;IACA,OAAOD;AACT"}