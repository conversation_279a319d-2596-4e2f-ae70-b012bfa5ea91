{"version": 3, "sources": ["../../../../src/client/components/router-reducer/create-initial-router-state.ts"], "names": ["createInitialRouterState", "buildId", "initialTree", "initialSeedData", "initialCanonicalUrl", "initialParallelRoutes", "isServer", "location", "initialHead", "subTreeData", "cache", "status", "CacheStates", "READY", "data", "parallelRoutes", "Map", "size", "fillLazyItemsTillLeafWithHead", "undefined", "extractPathFromFlightRouterState", "tree", "prefetchCache", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "onlyHashChange", "hashFragment", "segmentPaths", "canonicalUrl", "createHrefFromUrl", "nextUrl", "pathname"], "mappings": ";;;;+BAuBgBA;;;eAAAA;;;+CAhBY;mCACM;+CACY;oCACG;AAa1C,SAASA,yBAAyB,KASV;IATU,IAAA,EACvCC,OAAO,EACPC,WAAW,EACXC,eAAe,EACfC,mBAAmB,EACnBC,qBAAqB,EACrBC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACkB,GATU;IAUvC,MAAMC,cAAcN,eAAe,CAAC,EAAE;IAEtC,MAAMO,QAAmB;QACvBC,QAAQC,0CAAW,CAACC,KAAK;QACzBC,MAAM;QACNL,aAAaA;QACb,oJAAoJ;QACpJM,gBAAgBT,WAAW,IAAIU,QAAQX;IACzC;IAEA,yEAAyE;IACzE,IAAIA,0BAA0B,QAAQA,sBAAsBY,IAAI,KAAK,GAAG;QACtEC,IAAAA,4DAA6B,EAC3BR,OACAS,WACAjB,aACAC,iBACAK;IAEJ;QA4BI,sEAAsE;IACrEY;IA3BL,OAAO;QACLnB;QACAoB,MAAMnB;QACNQ;QACAY,eAAe,IAAIN;QACnBO,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,mEAAmE;YACnE,gFAAgF;YAChFC,4BAA4B;QAC9B;QACAC,mBAAmB;YACjBC,OAAO;YACPC,gBAAgB;YAChBC,cAAc;YACdC,cAAc,EAAE;QAClB;QACAC,cACE,6EAA6E;QAC7E,kJAAkJ;QAClJzB,WAEI0B,IAAAA,oCAAiB,EAAC1B,YAClBH;QACN8B,SAEE,CAACd,OAAAA,IAAAA,oDAAgC,EAAClB,iBAAgBK,4BAAAA,SAAU4B,QAAQ,aAAnEf,OACD;IACJ;AACF"}