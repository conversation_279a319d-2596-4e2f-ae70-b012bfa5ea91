{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "names": ["React", "isClientReference", "getLayoutOrPageModule", "interopDefault", "parseLoaderTree", "createComponentStylesAndScripts", "getLayerAssets", "hasLoadingComponentInTree", "Postpone", "postpone", "createComponentTree", "createSegmentPath", "loaderTree", "tree", "parentParams", "firstItem", "rootLayoutIncluded", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "asNotFound", "metadataOutlet", "ctx", "renderOpts", "nextConfigOutput", "staticGenerationStore", "componentMod", "staticGenerationBailout", "NotFoundBoundary", "LayoutRouter", "RenderFromTemplateContext", "StaticGenerationSearchParamsBailoutProvider", "serverHooks", "DynamicServerError", "pagePath", "getDynamicParamFromSegment", "isPrefetch", "searchParamsProps", "page", "layoutOrPagePath", "segment", "components", "parallelRoutes", "layout", "template", "error", "loading", "notFound", "injectedCSSWithCurrentLayout", "Set", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "layerAssets", "Template", "templateStyles", "templateScripts", "filePath", "getComponent", "Fragment", "ErrorComponent", "errorStyles", "errorScripts", "Loading", "loadingStyles", "loadingScripts", "isLayout", "isPage", "layoutOrPageMod", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "dynamic", "forceDynamic", "dynamicShouldError", "link", "forceStatic", "fetchCache", "revalidate", "defaultRevalidate", "isStaticGeneration", "dynamicUsageDescription", "dynamicUsageErr", "LayoutOrPage", "undefined", "Component", "parallelKeys", "Object", "keys", "hasSlot<PERSON>ey", "length", "componentProps", "NotFoundComponent", "RootLayoutComponent", "process", "env", "NODE_ENV", "isValidElementType", "require", "Error", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "parallelRouteMap", "Promise", "all", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "currentSegmentPath", "parallelRoute", "notFoundComponent", "currentStyles", "childCacheNodeSeedData", "seedData", "styles", "childComponentStyles", "child", "parallel<PERSON><PERSON>er<PERSON>ey", "segmentPath", "hasLoading", "Boolean", "parallelRouteProps", "parallelRouteCacheNodeSeedData", "parallelRouteProp", "flightData", "children", "isClientComponent", "meta", "name", "content", "props", "params", "propsForComponent"], "mappings": "AACA,OAAOA,WAA+B,QAAO;AAC7C,SAASC,iBAAiB,QAAQ,6BAA4B;AAC9D,SAASC,qBAAqB,QAAQ,wBAAuB;AAE7D,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,eAAe,QAAQ,sBAAqB;AAErD,SAASC,+BAA+B,QAAQ,wCAAuC;AACvF,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,yBAAyB,QAAQ,kCAAiC;AAO3E;;CAEC,GACD,OAAO,MAAMC,WAAW,CAAC,EACvBC,QAAQ,EAGT;IACC,oEAAoE;IACpE,OAAOA,SAAS;AAClB,EAAC;AAED;;CAEC,GACD,OAAO,eAAeC,oBAAoB,EACxCC,iBAAiB,EACjBC,YAAYC,IAAI,EAChBC,YAAY,EACZC,SAAS,EACTC,kBAAkB,EAClBC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,UAAU,EACVC,cAAc,EACdC,GAAG,EAaJ;IACC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAE,EAChCC,qBAAqB,EACrBC,cAAc,EACZC,uBAAuB,EACvBC,gBAAgB,EAChBC,YAAY,EACZC,yBAAyB,EACzBC,2CAA2C,EAC3CC,aAAa,EAAEC,kBAAkB,EAAE,EACpC,EACDC,QAAQ,EACRC,0BAA0B,EAC1BC,UAAU,EACVC,iBAAiB,EAClB,GAAGf;IAEJ,MAAM,EAAEgB,IAAI,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,cAAc,EAAE,GACnEtC,gBAAgBS;IAElB,MAAM,EAAE8B,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE,aAAaC,QAAQ,EAAE,GAAGN;IAEpE,MAAMO,+BAA+B,IAAIC,IAAIhC;IAC7C,MAAMiC,8BAA8B,IAAID,IAAI/B;IAC5C,MAAMiC,2CAA2C,IAAIF,IACnD9B;IAGF,MAAMiC,cAAc9C,eAAe;QACjCgB;QACAiB;QACAtB,aAAa+B;QACb9B,YAAYgC;QACZ/B,yBAAyBgC;IAC3B;IAEA,MAAM,CAACE,UAAUC,gBAAgBC,gBAAgB,GAAGX,WAChD,MAAMvC,gCAAgC;QACpCiB;QACAkC,UAAUZ,QAAQ,CAAC,EAAE;QACrBa,cAAcb,QAAQ,CAAC,EAAE;QACzB3B,aAAa+B;QACb9B,YAAYgC;IACd,KACA;QAAClD,MAAM0D,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,aAAaC,aAAa,GAAGhB,QAChD,MAAMxC,gCAAgC;QACpCiB;QACAkC,UAAUX,KAAK,CAAC,EAAE;QAClBY,cAAcZ,KAAK,CAAC,EAAE;QACtB5B,aAAa+B;QACb9B,YAAYgC;IACd,KACA,EAAE;IAEN,MAAM,CAACY,SAASC,eAAeC,eAAe,GAAGlB,UAC7C,MAAMzC,gCAAgC;QACpCiB;QACAkC,UAAUV,OAAO,CAAC,EAAE;QACpBW,cAAcX,OAAO,CAAC,EAAE;QACxB7B,aAAa+B;QACb9B,YAAYgC;IACd,KACA,EAAE;IAEN,MAAMe,WAAW,OAAOtB,WAAW;IACnC,MAAMuB,SAAS,OAAO5B,SAAS;IAC/B,MAAM,CAAC6B,gBAAgB,GAAG,MAAMjE,sBAAsBW;IAEtD;;GAEC,GACD,MAAMuD,wBAAwBH,YAAY,CAACjD;IAC3C;;GAEC,GACD,MAAMqD,uCACJrD,sBAAsBoD;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAGxB,WAC/B,MAAM1C,gCAAgC;QACpCiB;QACAkC,UAAUT,QAAQ,CAAC,EAAE;QACrBU,cAAcV,QAAQ,CAAC,EAAE;QACzB9B,aAAa+B;QACb9B,YAAYgC;IACd,KACA,EAAE;IAEN,IAAIsB,UAAUL,mCAAAA,gBAAiBK,OAAO;IAEtC,IAAIhD,qBAAqB,UAAU;QACjC,IAAI,CAACgD,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtC/C,sBAAsBgD,YAAY,GAAG;YACrChD,sBAAsBiD,kBAAkB,GAAG;YAC3C/C,wBAAwB,CAAC,cAAc,CAAC,EAAE;gBACxC6C;gBACAG,MAAM;YACR;QACF;IACF;IAEA,IAAI,OAAOH,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvB/C,sBAAsBiD,kBAAkB,GAAG;QAC7C,OAAO,IAAIF,YAAY,iBAAiB;YACtC/C,sBAAsBgD,YAAY,GAAG;YAErC,0DAA0D;YAC1D,IAAI,CAAChD,sBAAsBhB,QAAQ,EAAE;gBACnC,wEAAwE;gBACxE,0CAA0C;gBAC1CkB,wBAAwB,CAAC,aAAa,CAAC,EAAE;oBAAE6C;gBAAQ;YACrD;QACF,OAAO;YACL/C,sBAAsBiD,kBAAkB,GAAG;YAC3C,IAAIF,YAAY,gBAAgB;gBAC9B/C,sBAAsBmD,WAAW,GAAG;YACtC,OAAO;gBACLnD,sBAAsBmD,WAAW,GAAG;YACtC;QACF;IACF;IAEA,IAAI,QAAOT,mCAAAA,gBAAiBU,UAAU,MAAK,UAAU;QACnDpD,sBAAsBoD,UAAU,GAAGV,mCAAAA,gBAAiBU,UAAU;IAChE;IAEA,IAAI,QAAOV,mCAAAA,gBAAiBW,UAAU,MAAK,UAAU;QACnDxD,IAAIyD,iBAAiB,GAAGZ,gBAAgBW,UAAU;QAElD,IACE,OAAOrD,sBAAsBqD,UAAU,KAAK,eAC3C,OAAOrD,sBAAsBqD,UAAU,KAAK,YAC3CrD,sBAAsBqD,UAAU,GAAGxD,IAAIyD,iBAAiB,EAC1D;YACAtD,sBAAsBqD,UAAU,GAAGxD,IAAIyD,iBAAiB;QAC1D;QAEA,IACEtD,sBAAsBuD,kBAAkB,IACxC1D,IAAIyD,iBAAiB,KAAK,KAC1B,wEAAwE;QACxE,0CAA0C;QAC1C,CAACtD,sBAAsBhB,QAAQ,EAC/B;YACA,MAAMwE,0BAA0B,CAAC,yBAAyB,EAAEzC,QAAQ,CAAC;YACrEf,sBAAsBwD,uBAAuB,GAAGA;YAEhD,MAAM,IAAIhD,mBAAmBgD;QAC/B;IACF;IAEA,oEAAoE;IACpE,IAAIxD,sBAAsByD,eAAe,EAAE;QACzC,MAAMzD,sBAAsByD,eAAe;IAC7C;IAEA,MAAMC,eAAehB,kBACjBhE,eAAegE,mBACfiB;IAEJ;;GAEC,GACD,IAAIC,YAAYF;IAChB,MAAMG,eAAeC,OAAOC,IAAI,CAAC9C;IACjC,MAAM+C,aAAaH,aAAaI,MAAM,GAAG;IAEzC,IAAID,cAAcrB,uBAAuB;QACvCiB,YAAY,CAACM;YACX,MAAMC,oBAAoBtB;YAC1B,MAAMuB,sBAAsBV;YAC5B,qBACE,oBAACvD;gBACCmB,wBACE,0CACGK,2BACD,oBAACyC,2BACEtB,8BACD,oBAACqB;6BAKP,oBAACC,qBAAwBF;QAG/B;IACF;IAEA,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,QAAQ;QACvC,IACE,AAAChC,CAAAA,UAAU,OAAOmB,cAAc,WAAU,KAC1C,CAACY,mBAAmBZ,YACpB;YACA,MAAM,IAAIc,MACR,CAAC,sDAAsD,EAAEjE,SAAS,CAAC,CAAC;QAExE;QAEA,IACE,OAAOyB,mBAAmB,eAC1B,CAACsC,mBAAmBtC,iBACpB;YACA,MAAM,IAAIwC,MACR,CAAC,8DAA8D,EAAE3D,QAAQ,CAAC;QAE9E;QAEA,IAAI,OAAOsB,YAAY,eAAe,CAACmC,mBAAmBnC,UAAU;YAClE,MAAM,IAAIqC,MACR,CAAC,0DAA0D,EAAE3D,QAAQ,CAAC;QAE1E;QAEA,IAAI,OAAO8B,aAAa,eAAe,CAAC2B,mBAAmB3B,WAAW;YACpE,MAAM,IAAI6B,MACR,CAAC,2DAA2D,EAAE3D,QAAQ,CAAC;QAE3E;IACF;IAEA,iCAAiC;IACjC,MAAM4D,eAAejE,2BAA2BK;IAChD;;GAEC,GACD,MAAM6D,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAGxF,YAAY;QACf,CAACsF,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IAEAxF;IACN,4BAA4B;IAC5B,MAAM0F,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAGjE;IAEhE,EAAE;IACF,8EAA8E;IAC9E,kBAAkB;IAClB,MAAMkE,mBAAmB,MAAMC,QAAQC,GAAG,CACxCrB,OAAOC,IAAI,CAAC9C,gBAAgBmE,GAAG,CAC7B,OACEC;QAEA,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,qBAAwCjG,YAC1C;YAAC+F;SAAiB,GAClB;YAACN;YAAeM;SAAiB;QAErC,MAAMG,gBAAgBvE,cAAc,CAACoE,iBAAiB;QAEtD,MAAMI,oBACJ5C,YAAYyC,mCAAqB,oBAACzC,kBAAcc;QAElD,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAI+B,gBAAgB/B;QACpB,IAAIgC,yBAAmD;QACvD,IACE,CACEhF,CAAAA,cACC0B,CAAAA,WAAW,CAACvD,0BAA0B0G,cAAa,CAAC,GAEvD;YACA,6BAA6B;YAC7B,MAAM,EAAEI,QAAQ,EAAEC,QAAQC,oBAAoB,EAAE,GAC9C,MAAM7G,oBAAoB;gBACxBC,mBAAmB,CAAC6G;oBAClB,OAAO7G,kBAAkB;2BAAIqG;2BAAuBQ;qBAAM;gBAC5D;gBACA5G,YAAYqG;gBACZnG,cAAcuF;gBACdrF,oBAAoBqD;gBACpBpD,aAAa+B;gBACb9B,YAAYgC;gBACZ/B,yBAAyBgC;gBACzB/B;gBACAC;gBACAC;YACF;YAEF6F,gBAAgBI;YAChBH,yBAAyBC;QAC3B;QAEA,4CAA4C;QAC5C,OAAO;YACLP;0BACA,oBAACjF;gBACC4F,mBAAmBX;gBACnBY,aAAa/G,kBAAkBqG;gBAC/BlE,SAASgB,wBAAU,oBAACA,iBAAasB;gBACjCrB,eAAeA;gBACfC,gBAAgBA;gBAChB,sKAAsK;gBACtK2D,YAAYC,QAAQ9D;gBACpBjB,OAAOc;gBACPC,aAAaA;gBACbC,cAAcA;gBACdjB,wBACE,oBAACS,8BACC,oBAACvB;gBAGLwB,gBAAgBA;gBAChBC,iBAAiBA;gBACjBR,UAAUmE;gBACV3C,gBAAgBA;gBAChB+C,QAAQH;;YAEVC;SACD;IACH;IAIJ,uFAAuF;IACvF,IAAIS,qBAAyD,CAAC;IAC9D,IAAIC,iCAEA,CAAC;IACL,KAAK,MAAMb,iBAAiBP,iBAAkB;QAC5C,MAAM,CAACI,kBAAkBiB,mBAAmBC,WAAW,GAAGf;QAC1DY,kBAAkB,CAACf,iBAAiB,GAAGiB;QACvCD,8BAA8B,CAAChB,iBAAiB,GAAGkB;IACrD;IAEA,wIAAwI;IACxI,IAAI,CAAC3C,WAAW;QACd,OAAO;YACLgC,UAAU;gBACRb;gBACAsB;gBACA,wEAAwE;gBACxE,sEAAsE;gBACtE,wEAAwE;gBACxE,uEAAuE;gBACvE,oBAAoB;8BACpB,0CAAGD,mBAAmBI,QAAQ;aAC/B;YACDX,QAAQlE;QACV;IACF;IAEA,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,gBAAgB;IAChB,IAAI3B,sBAAsBgD,YAAY,IAAIhD,sBAAsBhB,QAAQ,EAAE;QACxE,OAAO;YACL4G,UAAU;gBACRb;gBACAsB;8BACA,oBAACtH;oBAASC,UAAUgB,sBAAsBhB,QAAQ;;aACnD;YACD6G,QAAQlE;QACV;IACF;IAEA,MAAM8E,oBAAoBjI,kBAAkBkE;IAE5C,oEAAoE;IACpE,iEAAiE;IACjE,IAAI+C,oBAAoB,CAAC;IACzB,IACE5C,YACAlD,cACA,2GAA2G;IAC3G,6DAA6D;IAC7D,CAACsF,iBAAiBhB,MAAM,EACxB;QACAwB,oBAAoB;YAClBe,wBACE,wDACE,oBAACE;gBAAKC,MAAK;gBAASC,SAAQ;gBAC3BvC,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,oBAACmC;gBAAKC,MAAK;gBAAaC,SAAQ;gBAEjC9D,8BACD,oBAACD;QAGP;IACF;IAEA,MAAMgE,QAAQ;QACZ,GAAGT,kBAAkB;QACrB,GAAGX,iBAAiB;QACpB,8GAA8G;QAC9G,gEAAgE;QAChE,+GAA+G;QAC/GqB,QAAQlC;QACR,iCAAiC;QACjC,GAAG,AAAC,CAAA;YACF,IAAI6B,qBAAqBzG,sBAAsBuD,kBAAkB,EAAE;gBACjE,OAAO,CAAC;YACV;YAEA,IAAId,QAAQ;gBACV,OAAO7B;YACT;QACF,CAAA,GAAI;IACN;IAEA,OAAO;QACLgF,UAAU;YACRb;YACAsB;0BACA,0CACG5D,SAAS7C,iBAAiB,MAE1B6C,UAAUgE,kCACT,oBAACnG;gBACCyG,mBAAmBF;gBACnBjD,WAAWA;gBACXL,oBAAoBvD,sBAAsBuD,kBAAkB;+BAG9D,oBAACK,WAAciD,QAUhB;SAEJ;QACDhB,QAAQlE;IACV;AACF"}