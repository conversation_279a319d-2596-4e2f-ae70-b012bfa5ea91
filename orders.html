<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبات - شركة التوصيل السريع</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="orders.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo-container">
                    <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2"/>
                        <path d="M15 18H9"/>
                        <path d="M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14"/>
                        <circle cx="17" cy="18" r="2"/>
                        <circle cx="7" cy="18" r="2"/>
                    </svg>
                </div>
                <div class="company-info">
                    <h1 class="company-name">إدارة الطلبات</h1>
                    <p class="welcome-text">مرحباً، <span id="username">المدير</span></p>
                </div>
            </div>
            
            <div class="header-actions">
                <button class="nav-btn" onclick="window.location.href='dashboard.html'">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    الرئيسية
                </button>
                
                <button class="logout-btn" id="logout-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                        <polyline points="16,17 21,12 16,7"/>
                        <line x1="21" y1="12" x2="9" y2="12"/>
                    </svg>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Action Bar -->
        <section class="action-bar">
            <div class="action-bar-content">
                <div class="page-title">
                    <h2>إدارة الطلبات</h2>
                    <p>إضافة وتعديل وحذف الطلبات</p>
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-primary" id="add-order-btn">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"/>
                            <line x1="5" y1="12" x2="19" y2="12"/>
                        </svg>
                        إضافة طلب جديد
                    </button>
                    
                    <button class="btn btn-secondary" id="bulk-actions-btn">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4"/>
                            <polyline points="9,11 12,14 15,11"/>
                            <line x1="12" y1="2" x2="12" y2="14"/>
                        </svg>
                        إجراءات متعددة
                    </button>
                    
                    <button class="btn btn-success" id="export-btn">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="7,10 12,15 17,10"/>
                            <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                        تصدير
                    </button>
                </div>
            </div>
        </section>

        <!-- Search and Filters -->
        <section class="search-section">
            <div class="search-container">
                <div class="search-box">
                    <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"/>
                        <path d="M21 21l-4.35-4.35"/>
                    </svg>
                    <input type="text" id="search-input" class="search-input" placeholder="البحث برقم الوصل، اسم المرسل إليه، أو رقم الهاتف...">
                    <button class="clear-search" id="clear-search">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"/>
                            <line x1="6" y1="6" x2="18" y2="18"/>
                        </svg>
                    </button>
                </div>
                
                <div class="search-filters">
                    <select id="status-filter" class="filter-select">
                        <option value="">جميع الحالات</option>
                        <option value="delivering">قيد التسليم</option>
                        <option value="delayed">مؤجل</option>
                        <option value="returned">راجع</option>
                        <option value="pending">معلق</option>
                        <option value="delivered">تم التسليم</option>
                    </select>
                    
                    <select id="city-filter" class="filter-select">
                        <option value="">جميع المدن</option>
                        <option value="riyadh">الرياض</option>
                        <option value="jeddah">جدة</option>
                        <option value="dammam">الدمام</option>
                        <option value="mecca">مكة</option>
                    </select>
                    
                    <select id="date-filter" class="filter-select">
                        <option value="">جميع التواريخ</option>
                        <option value="today">اليوم</option>
                        <option value="yesterday">أمس</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month">هذا الشهر</option>
                    </select>
                    
                    <select id="amount-filter" class="filter-select">
                        <option value="">جميع المبالغ</option>
                        <option value="0-100">أقل من 100 ر.س</option>
                        <option value="100-300">100 - 300 ر.س</option>
                        <option value="300-500">300 - 500 ر.س</option>
                        <option value="500+">أكثر من 500 ر.س</option>
                    </select>
                </div>
            </div>
        </section>

        <!-- Orders Table -->
        <section class="orders-section">
            <div class="orders-container">
                <div class="table-header">
                    <div class="table-info">
                        <h3>قائمة الطلبات</h3>
                        <span class="orders-count">إجمالي: <span id="total-orders">0</span> طلب</span>
                    </div>
                    
                    <div class="table-actions">
                        <button class="refresh-btn" onclick="refreshOrders()">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="23,4 23,10 17,10"/>
                                <polyline points="1,20 1,14 7,14"/>
                                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                            </svg>
                            تحديث
                        </button>
                        
                        <div class="view-options">
                            <button class="view-btn active" data-view="table">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M3 6h18"/>
                                    <path d="M3 12h18"/>
                                    <path d="M3 18h18"/>
                                </svg>
                            </button>
                            <button class="view-btn" data-view="grid">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="3" width="7" height="7"/>
                                    <rect x="14" y="3" width="7" height="7"/>
                                    <rect x="14" y="14" width="7" height="7"/>
                                    <rect x="3" y="14" width="7" height="7"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Table View -->
                <div class="table-view active" id="table-view">
                    <div class="table-wrapper">
                        <table class="orders-table" id="orders-table">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="select-all" class="checkbox">
                                    </th>
                                    <th>رقم الوصل</th>
                                    <th>اسم المرسل إليه</th>
                                    <th>رقم الهاتف</th>
                                    <th>المدينة</th>
                                    <th>تاريخ الإرسال</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="orders-tbody">
                                <!-- Orders will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Grid View -->
                <div class="grid-view" id="grid-view">
                    <div class="orders-grid" id="orders-grid">
                        <!-- Order cards will be populated here -->
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        <span>عرض <span id="showing-from">1</span> - <span id="showing-to">10</span> من <span id="total-count">0</span></span>
                    </div>
                    
                    <div class="pagination-controls">
                        <button class="pagination-btn" id="prev-page" disabled>
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,18 9,12 15,6"/>
                            </svg>
                            السابق
                        </button>
                        
                        <div class="page-numbers" id="page-numbers">
                            <!-- Page numbers will be populated here -->
                        </div>
                        
                        <button class="pagination-btn" id="next-page">
                            التالي
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Add/Edit Order Modal -->
    <div id="order-modal" class="modal hidden">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="modal-title">إضافة طلب جديد</h3>
                <button class="close-modal" id="close-modal">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Order form will be populated here -->
            </div>
        </div>
    </div>

    <!-- Bulk Actions Modal -->
    <div id="bulk-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إجراءات متعددة</h3>
                <button class="close-modal" onclick="closeBulkModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <p>تم تحديد <span id="selected-count">0</span> طلب</p>
                <div class="bulk-actions">
                    <button class="btn btn-success" onclick="bulkUpdateStatus('delivering')">تحديد كقيد التسليم</button>
                    <button class="btn btn-warning" onclick="bulkUpdateStatus('delayed')">تحديد كمؤجل</button>
                    <button class="btn btn-danger" onclick="bulkUpdateStatus('returned')">تحديد كراجع</button>
                    <button class="btn btn-danger" onclick="bulkDelete()">حذف المحدد</button>
                </div>
            </div>
        </div>
    </div>

    <script src="orders.js"></script>
</body>
</html>
