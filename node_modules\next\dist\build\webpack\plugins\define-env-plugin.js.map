{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "names": ["getDefineEnv", "getDefineEnvPlugin", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "isTurbopack", "allowedRevalidateHeaderKeys", "clientRouterFilters", "dev", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "previewModeId", "__NEXT_DEFINE_ENV", "Object", "keys", "process", "env", "reduce", "prev", "startsWith", "JSON", "stringify", "acc", "EdgeRuntime", "NEXT_EDGE_RUNTIME_PROVIDER", "experimental", "windowHistorySupport", "ppr", "useDeploymentIdServerActions", "deploymentId", "manualClientBasePath", "clientRouterFilter", "staticFilter", "dynamicFilter", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "buildActivity", "buildActivityPosition", "reactStrictMode", "optimizeFonts", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "deviceSizes", "images", "imageSizes", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "output", "basePath", "strictNextHead", "i18n", "analyticsId", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "assetPrefix", "undefined", "needsExperimentalReact", "options", "webpack", "DefinePlugin"], "mappings": ";;;;;;;;;;;;;;;IAwCgBA,YAAY;eAAZA;;IA+LAC,kBAAkB;eAAlBA;;;yBArOQ;wCACe;AAEvC,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC;IAEnI;AACF;AA0BO,SAAST,aAAa,EAC3BU,WAAW,EACXC,2BAA2B,EAC3BC,mBAAmB,EACnBT,MAAM,EACNU,GAAG,EACHC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EAClBC,aAAa,EACU;QA0HNnB,gBAKSA,iBAY0BA;IA1IpD,OAAO;QACL,+CAA+C;QAC/CoB,mBAAmB;QAEnB,GAAGC,OAAOC,IAAI,CAACC,QAAQC,GAAG,EAAEC,MAAM,CAChC,CAACC,MAAiCzB;YAChC,IAAIA,IAAI0B,UAAU,CAAC,iBAAiB;gBAClCD,IAAI,CAAC,CAAC,YAAY,EAAEzB,IAAI,CAAC,CAAC,GAAG2B,KAAKC,SAAS,CAACN,QAAQC,GAAG,CAACvB,IAAI;YAC9D;YACA,OAAOyB;QACT,GACA,CAAC,EACF;QACD,GAAGL,OAAOC,IAAI,CAACtB,OAAOwB,GAAG,EAAEC,MAAM,CAAC,CAACK,KAAK7B;YACtCF,qBAAqBC,QAAQC;YAE7B,OAAO;gBACL,GAAG6B,GAAG;gBACN,CAAC,CAAC,YAAY,EAAE7B,IAAI,CAAC,CAAC,EAAE2B,KAAKC,SAAS,CAAC7B,OAAOwB,GAAG,CAACvB,IAAI;YACxD;QACF,GAAG,CAAC,EAAE;QACN,GAAI,CAACc,eACD,CAAC,IACD;YACEgB,aAAaH,KAAKC,SAAS,CACzB;;;;aAIC,GACDN,QAAQC,GAAG,CAACQ,0BAA0B,IAAI;QAE9C,CAAC;QACL,qBAAqBJ,KAAKC,SAAS,CAACtB;QACpC,yBAAyBqB,KAAKC,SAAS,CAACtB;QACxC,6DAA6D;QAC7D,wBAAwBqB,KAAKC,SAAS,CAACnB,MAAM,gBAAgB;QAC7D,4BAA4BkB,KAAKC,SAAS,CACxCd,eAAe,SAASE,eAAe,WAAW;QAEpD,4BAA4BW,KAAKC,SAAS,CAAC;QAC3C,6CAA6CD,KAAKC,SAAS,CACzD7B,OAAOiC,YAAY,CAACC,oBAAoB;QAE1C,0BAA0BN,KAAKC,SAAS,CAAC7B,OAAOiC,YAAY,CAACE,GAAG,KAAK;QACrE,4CAA4CP,KAAKC,SAAS,CACxD7B,OAAOiC,YAAY,CAACG,4BAA4B;QAElD,kCAAkCR,KAAKC,SAAS,CAC9C7B,OAAOiC,YAAY,CAACI,YAAY,IAAI;QAEtC,6CACET,KAAKC,SAAS,CAACjB;QACjB,sCAAsCgB,KAAKC,SAAS,CAACV;QACrD,iDAAiDS,KAAKC,SAAS,CAC7DrB;QAEF,0CAA0CoB,KAAKC,SAAS,CACtDX,sBAAsB,EAAE;QAE1B,8CAA8CU,KAAKC,SAAS,CAC1D7B,OAAOiC,YAAY,CAACK,oBAAoB;QAE1C,mDAAmDV,KAAKC,SAAS,CAC/D7B,OAAOiC,YAAY,CAACM,kBAAkB;QAExC,6CAA6CX,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqB+B,YAAY;QAEnC,6CAA6CZ,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqBgC,aAAa;QAEpC,8CAA8Cb,KAAKC,SAAS,CAC1D7B,OAAOiC,YAAY,CAACS,qBAAqB;QAE3C,0CAA0Cd,KAAKC,SAAS,CACtD7B,OAAOiC,YAAY,CAACU,kBAAkB;QAExC,mCAAmCf,KAAKC,SAAS,CAAC7B,OAAO4C,WAAW;QACpE,mBAAmBhB,KAAKC,SAAS,CAACf;QAClC,gCAAgCc,KAAKC,SAAS,CAC5CN,QAAQC,GAAG,CAACqB,gBAAgB;QAE9B,2FAA2F;QAC3F,GAAInC,OAAQI,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+Ba,KAAKC,SAAS,CAAClB;QAChD,IACA,CAAC,CAAC;QACN,qCAAqCiB,KAAKC,SAAS,CAAC7B,OAAO8C,aAAa;QACxE,sCAAsClB,KAAKC,SAAS,CAClD7B,OAAO+C,aAAa,CAACC,aAAa;QAEpC,+CAA+CpB,KAAKC,SAAS,CAC3D7B,OAAO+C,aAAa,CAACE,qBAAqB;QAE5C,kCAAkCrB,KAAKC,SAAS,CAC9C7B,OAAOkD,eAAe,KAAK,OAAO,QAAQlD,OAAOkD,eAAe;QAElE,sCAAsCtB,KAAKC,SAAS,CAClD,6EAA6E;QAC7E7B,OAAOkD,eAAe,KAAK,OAAO,OAAOlD,OAAOkD,eAAe;QAEjE,qCAAqCtB,KAAKC,SAAS,CACjD,CAACnB,OAAOV,OAAOmD,aAAa;QAE9B,mCAAmCvB,KAAKC,SAAS,CAC/C7B,OAAOiC,YAAY,CAACmB,WAAW,IAAI,CAAC1C;QAEtC,qCAAqCkB,KAAKC,SAAS,CACjD7B,OAAOiC,YAAY,CAACoB,iBAAiB,IAAI,CAAC3C;QAE5C,yCAAyCkB,KAAKC,SAAS,CACrD7B,OAAOiC,YAAY,CAACqB,iBAAiB;QAEvC,iCAAiC1B,KAAKC,SAAS,CAAC;YAC9C0B,aAAavD,OAAOwD,MAAM,CAACD,WAAW;YACtCE,YAAYzD,OAAOwD,MAAM,CAACC,UAAU;YACpCC,MAAM1D,OAAOwD,MAAM,CAACE,IAAI;YACxBC,QAAQ3D,OAAOwD,MAAM,CAACG,MAAM;YAC5BC,qBAAqB5D,OAAOwD,MAAM,CAACI,mBAAmB;YACtDC,WAAW,EAAE7D,2BAAAA,iBAAAA,OAAQwD,MAAM,qBAAdxD,eAAgB6D,WAAW;YACxC,GAAInD,MACA;gBACE,gEAAgE;gBAChEoD,SAAS9D,OAAOwD,MAAM,CAACM,OAAO;gBAC9BC,cAAc,GAAE/D,kBAAAA,OAAOwD,MAAM,qBAAbxD,gBAAe+D,cAAc;gBAC7CC,QAAQhE,OAAOgE,MAAM;YACvB,IACA,CAAC,CAAC;QACR;QACA,sCAAsCpC,KAAKC,SAAS,CAAC7B,OAAOiE,QAAQ;QACpE,uCAAuCrC,KAAKC,SAAS,CACnD7B,OAAOiC,YAAY,CAACiC,cAAc;QAEpC,mCAAmCtC,KAAKC,SAAS,CAAChB;QAClD,oCAAoCe,KAAKC,SAAS,CAAC7B,OAAOgE,MAAM;QAChE,mCAAmCpC,KAAKC,SAAS,CAAC,CAAC,CAAC7B,OAAOmE,IAAI;QAC/D,mCAAmCvC,KAAKC,SAAS,EAAC7B,eAAAA,OAAOmE,IAAI,qBAAXnE,aAAa8D,OAAO;QACtE,mCAAmClC,KAAKC,SAAS,CAAC7B,OAAOoE,WAAW;QACpE,kDAAkDxC,KAAKC,SAAS,CAC9D7B,OAAOqE,0BAA0B;QAEnC,0DAA0DzC,KAAKC,SAAS,CACtE7B,OAAOiC,YAAY,CAACqC,iCAAiC;QAEvD,4CAA4C1C,KAAKC,SAAS,CACxD7B,OAAOuE,yBAAyB;QAElC,iDAAiD3C,KAAKC,SAAS,CAC7D7B,OAAOiC,YAAY,CAACuC,oBAAoB,IACtCxE,OAAOiC,YAAY,CAACuC,oBAAoB,CAACC,MAAM,GAAG;QAEtD,6CAA6C7C,KAAKC,SAAS,CACzD7B,OAAOiC,YAAY,CAACuC,oBAAoB;QAE1C,mCAAmC5C,KAAKC,SAAS,CAAC7B,OAAO0E,WAAW;QACpE,GAAI1D,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiBY,KAAKC,SAAS,CAAC;QAClC,IACA8C,SAAS;QACb,GAAI3D,0BACA;YACE,yCAAyCY,KAAKC,SAAS,CACrD+C,IAAAA,8CAAsB,EAAC5E;QAE3B,IACA2E,SAAS;IACf;AACF;AAEO,SAAS7E,mBAAmB+E,OAA+B;IAChE,OAAO,IAAIC,gBAAO,CAACC,YAAY,CAAClF,aAAagF;AAC/C"}