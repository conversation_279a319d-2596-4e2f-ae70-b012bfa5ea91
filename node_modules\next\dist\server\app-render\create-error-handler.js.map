{"version": 3, "sources": ["../../../src/server/app-render/create-error-handler.tsx"], "names": ["createErrorHandler", "_source", "dev", "isNextExport", "errorLogger", "capturedErrors", "allCapturedErrors", "silenceLogger", "err", "push", "isDynamicUsageError", "digest", "isAbortError", "formatServerError", "message", "includes", "span", "getTracer", "getActiveScopeSpan", "recordException", "setStatus", "code", "SpanStatusCode", "ERROR", "catch", "process", "env", "NODE_ENV", "logAppDirError", "require", "console", "error", "stringHash", "stack", "toString"], "mappings": ";;;;+BAagBA;;;eAAAA;;;mEAbO;mCACW;wBACQ;8BACb;qCACO;;;;;;AAS7B,SAASA,mBAAmB,EACjC;;GAEC,GACDC,OAAO,EACPC,GAAG,EACHC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,iBAAiB,EACjBC,aAAa,EASd;IACC,OAAO,CAACC;YAoBFA;QAnBJ,IAAIF,mBAAmBA,kBAAkBG,IAAI,CAACD;QAE9C,IAAIE,IAAAA,wCAAmB,EAACF,MAAM;YAC5B,OAAOA,IAAIG,MAAM;QACnB;QAEA,8DAA8D;QAC9D,IAAIC,IAAAA,0BAAY,EAACJ,MAAM;QAEvB,yEAAyE;QACzE,IAAIN,KAAK;YACPW,IAAAA,oCAAiB,EAACL;QACpB;QACA,kCAAkC;QAClC,8BAA8B;QAC9B,+CAA+C;QAC/C,IACE,CACEL,CAAAA,iBACAK,wBAAAA,eAAAA,IAAKM,OAAO,qBAAZN,aAAcO,QAAQ,CACpB,4FACF,GAEF;YACA,oDAAoD;YACpD,MAAMC,OAAOC,IAAAA,iBAAS,IAAGC,kBAAkB;YAC3C,IAAIF,MAAM;gBACRA,KAAKG,eAAe,CAACX;gBACrBQ,KAAKI,SAAS,CAAC;oBACbC,MAAMC,sBAAc,CAACC,KAAK;oBAC1BT,SAASN,IAAIM,OAAO;gBACtB;YACF;YAEA,IAAI,CAACP,eAAe;gBAClB,IAAIH,aAAa;oBACfA,YAAYI,KAAKgB,KAAK,CAAC,KAAO;gBAChC,OAAO;oBACL,kEAAkE;oBAClE,mCAAmC;oBACnC,mEAAmE;oBACnE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;wBACzC,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;wBACVD,eAAepB;oBACjB;oBACA,IAAIiB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;wBACzCG,QAAQC,KAAK,CAACvB;oBAChB;gBACF;YACF;QACF;QAEAH,eAAeI,IAAI,CAACD;QACpB,+EAA+E;QAC/E,OAAOwB,IAAAA,mBAAU,EAACxB,IAAIM,OAAO,GAAGN,IAAIyB,KAAK,GAAIzB,CAAAA,IAAIG,MAAM,IAAI,EAAC,GAAIuB,QAAQ;IAC1E;AACF"}