<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - شركة التوصيل السريع</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="sidebar.css">
    <link rel="stylesheet" href="orders.css">
</head>
<body>
    <main class="main-content">
        <section class="action-bar">
            <div class="action-bar-content">
                <div class="page-title">
                    <h2>إدارة العملاء</h2>
                    <p>إدارة بيانات العملاء والمرسلين</p>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"/>
                            <line x1="5" y1="12" x2="19" y2="12"/>
                        </svg>
                        إضافة عميل جديد
                    </button>
                </div>
            </div>
        </section>

        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card delivering">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                            <circle cx="9" cy="7" r="4"/>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">245</h3>
                        <p class="stat-label">إجمالي العملاء</p>
                    </div>
                </div>
                
                <div class="stat-card delayed">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">189</h3>
                        <p class="stat-label">عملاء نشطين</p>
                    </div>
                </div>
                
                <div class="stat-card pending">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">45</h3>
                        <p class="stat-label">عملاء جدد هذا الشهر</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="orders-section">
            <div class="orders-container">
                <div class="table-header">
                    <div class="table-info">
                        <h3>قائمة العملاء</h3>
                        <span class="orders-count">إجمالي: 245 عميل</span>
                    </div>
                </div>

                <div class="table-wrapper">
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>المدينة</th>
                                <th>عدد الطلبات</th>
                                <th>آخر طلب</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>متجر الإلكترونيات</td>
                                <td>0501234567</td>
                                <td>الرياض</td>
                                <td>25</td>
                                <td>2024-06-30</td>
                                <td><span class="status-badge status-delivering">نشط</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn primary">تعديل</button>
                                        <button class="action-btn">التفاصيل</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>مطعم البرجر</td>
                                <td>0507654321</td>
                                <td>جدة</td>
                                <td>18</td>
                                <td>2024-06-29</td>
                                <td><span class="status-badge status-delivering">نشط</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn primary">تعديل</button>
                                        <button class="action-btn">التفاصيل</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </main>

    <script src="sidebar.js"></script>
</body>
</html>
