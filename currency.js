// Currency Management System

class CurrencyManager {
    constructor() {
        this.currencies = {
            'SAR': {
                name: 'الريال السعودي',
                symbol: 'ر.س',
                code: 'SAR',
                rate: 1
            },
            'IQD': {
                name: 'الدينار العراقي',
                symbol: 'د.ع',
                code: 'IQD',
                rate: 1312 // 1 SAR = 1312 IQD (approximate rate)
            }
        };
        
        this.currentCurrency = this.loadCurrency();
        this.init();
    }
    
    init() {
        // Apply currency to all existing amounts on page load
        this.updateAllAmounts();
        
        // Listen for currency changes
        document.addEventListener('currencyChanged', () => {
            this.updateAllAmounts();
        });
    }
    
    loadCurrency() {
        const saved = localStorage.getItem('selectedCurrency');
        return saved && this.currencies[saved] ? saved : 'SAR';
    }
    
    saveCurrency(currencyCode) {
        localStorage.setItem('selectedCurrency', currencyCode);
    }
    
    setCurrency(currencyCode) {
        if (this.currencies[currencyCode]) {
            this.currentCurrency = currencyCode;
            this.saveCurrency(currencyCode);
            
            // Dispatch event for other components to listen
            document.dispatchEvent(new CustomEvent('currencyChanged', {
                detail: { currency: currencyCode }
            }));
            
            this.updateAllAmounts();
            return true;
        }
        return false;
    }
    
    getCurrentCurrency() {
        return this.currencies[this.currentCurrency];
    }
    
    formatAmount(amount, fromCurrency = 'SAR') {
        if (typeof amount === 'string') {
            // Extract number from string like "250.00 ر.س"
            amount = parseFloat(amount.replace(/[^\d.-]/g, ''));
        }
        
        if (isNaN(amount)) return '0';
        
        const current = this.getCurrentCurrency();
        let convertedAmount = amount;
        
        // Convert from base currency (SAR) to current currency
        if (fromCurrency === 'SAR' && this.currentCurrency !== 'SAR') {
            convertedAmount = amount * current.rate;
        } else if (fromCurrency !== 'SAR' && this.currentCurrency === 'SAR') {
            const fromRate = this.currencies[fromCurrency]?.rate || 1;
            convertedAmount = amount / fromRate;
        }
        
        // Format with appropriate decimal places
        const decimals = this.currentCurrency === 'IQD' ? 0 : 2;
        const formatted = convertedAmount.toLocaleString('ar-SA', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
        
        return `${formatted} ${current.symbol}`;
    }
    
    updateAllAmounts() {
        // Update all elements with class 'amount'
        const amountElements = document.querySelectorAll('.amount');
        amountElements.forEach(element => {
            const originalAmount = element.dataset.originalAmount || element.textContent;
            if (!element.dataset.originalAmount) {
                element.dataset.originalAmount = originalAmount;
            }
            element.textContent = this.formatAmount(originalAmount);
        });
        
        // Update stat numbers that represent currency
        const statNumbers = document.querySelectorAll('.stat-number');
        statNumbers.forEach(element => {
            if (element.dataset.isCurrency === 'true') {
                const originalAmount = element.dataset.originalAmount || element.textContent;
                if (!element.dataset.originalAmount) {
                    element.dataset.originalAmount = originalAmount;
                }
                element.textContent = this.formatAmount(originalAmount).split(' ')[0]; // Only number part
            }
        });
        
        // Update currency symbols in labels
        this.updateCurrencyLabels();
    }
    
    updateCurrencyLabels() {
        const current = this.getCurrentCurrency();
        
        // Update labels that mention currency
        const currencyLabels = document.querySelectorAll('[data-currency-label]');
        currencyLabels.forEach(element => {
            const originalText = element.dataset.originalText || element.textContent;
            if (!element.dataset.originalText) {
                element.dataset.originalText = originalText;
            }
            
            // Replace currency mentions
            let updatedText = originalText
                .replace(/ر\.س/g, current.symbol)
                .replace(/الريال السعودي/g, current.name)
                .replace(/\(ر\.س\)/g, `(${current.symbol})`);
            
            element.textContent = updatedText;
        });
    }
    
    // Convert amount from one currency to another
    convertAmount(amount, fromCurrency, toCurrency) {
        if (fromCurrency === toCurrency) return amount;
        
        const fromRate = this.currencies[fromCurrency]?.rate || 1;
        const toRate = this.currencies[toCurrency]?.rate || 1;
        
        // Convert to SAR first, then to target currency
        const sarAmount = fromCurrency === 'SAR' ? amount : amount / fromRate;
        const convertedAmount = toCurrency === 'SAR' ? sarAmount : sarAmount * toRate;
        
        return convertedAmount;
    }
    
    // Get exchange rate between two currencies
    getExchangeRate(fromCurrency, toCurrency) {
        if (fromCurrency === toCurrency) return 1;
        
        const fromRate = this.currencies[fromCurrency]?.rate || 1;
        const toRate = this.currencies[toCurrency]?.rate || 1;
        
        return toRate / fromRate;
    }
    
    // Create currency selector dropdown
    createCurrencySelector() {
        const selector = document.createElement('select');
        selector.className = 'currency-selector';
        selector.id = 'currency-selector';
        
        Object.keys(this.currencies).forEach(code => {
            const option = document.createElement('option');
            option.value = code;
            option.textContent = `${this.currencies[code].name} (${this.currencies[code].symbol})`;
            option.selected = code === this.currentCurrency;
            selector.appendChild(option);
        });
        
        selector.addEventListener('change', (e) => {
            this.setCurrency(e.target.value);
            this.showCurrencyChangeNotification(this.currencies[e.target.value].name);
        });
        
        return selector;
    }
    
    showCurrencyChangeNotification(currencyName) {
        // Create notification
        const notification = document.createElement('div');
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <svg style="width: 20px; height: 20px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
                    <line x1="12" y1="17" x2="12.01" y2="17"/>
                </svg>
                <span>تم تغيير العملة إلى ${currencyName}</span>
            </div>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 15px 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            z-index: 1001;
            animation: slideInRight 0.3s ease-out;
            max-width: 400px;
            font-weight: 500;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Initialize currency manager
let currencyManager;

document.addEventListener('DOMContentLoaded', () => {
    currencyManager = new CurrencyManager();
    
    // Make it globally available
    window.currencyManager = currencyManager;
});

// Helper function to format currency (for use in other scripts)
function formatCurrency(amount, fromCurrency = 'SAR') {
    if (window.currencyManager) {
        return window.currencyManager.formatAmount(amount, fromCurrency);
    }
    return `${amount} ر.س`; // Fallback
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CurrencyManager;
}
