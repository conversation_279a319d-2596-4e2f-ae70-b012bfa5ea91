<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - شركة التوصيل السريع</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="sidebar.css">
</head>
<body>


    <!-- Main Content -->
    <main class="main-content">
        <!-- Search Section -->
        <section class="search-section">
            <div class="search-container">
                <div class="search-box">
                    <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"/>
                        <path d="M21 21l-4.35-4.35"/>
                    </svg>
                    <input type="text" id="search-input" class="search-input" placeholder="البحث برقم الوصل أو رقم الهاتف...">
                    <button class="clear-search" id="clear-search">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"/>
                            <line x1="6" y1="6" x2="18" y2="18"/>
                        </svg>
                    </button>
                </div>
                
                <div class="search-filters">
                    <select id="date-filter" class="filter-select">
                        <option value="">جميع التواريخ</option>
                        <option value="today">اليوم</option>
                        <option value="yesterday">أمس</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month">هذا الشهر</option>
                    </select>
                    
                    <select id="city-filter" class="filter-select">
                        <option value="">جميع المدن</option>
                        <option value="riyadh">الرياض</option>
                        <option value="jeddah">جدة</option>
                        <option value="dammam">الدمام</option>
                        <option value="mecca">مكة</option>
                    </select>
                </div>
            </div>
        </section>

        <!-- Statistics Cards -->
        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card delivering">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number" id="delivering-count">0</h3>
                        <p class="stat-label">قيد التسليم</p>
                    </div>
                </div>
                
                <div class="stat-card delayed">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <polyline points="12,6 12,12 16,14"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number" id="delayed-count">0</h3>
                        <p class="stat-label">مؤجلة</p>
                    </div>
                </div>
                
                <div class="stat-card returned">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="1,4 1,10 7,10"/>
                            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number" id="returned-count">0</h3>
                        <p class="stat-label">راجعة</p>
                    </div>
                </div>
                
                <div class="stat-card pending">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <line x1="12" y1="8" x2="12" y2="12"/>
                            <line x1="12" y1="16" x2="12.01" y2="16"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number" id="pending-count">0</h3>
                        <p class="stat-label">معلقة</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Tabs Section -->
        <section class="tabs-section">
            <div class="tabs-container">
                <div class="tabs-header">
                    <button class="tab-btn active" data-tab="delivering">
                        <span class="tab-count" id="tab-delivering-count">0</span>
                        قيد التسليم
                    </button>
                    <button class="tab-btn" data-tab="delayed">
                        <span class="tab-count" id="tab-delayed-count">0</span>
                        مؤجلة
                    </button>
                    <button class="tab-btn" data-tab="returned">
                        <span class="tab-count" id="tab-returned-count">0</span>
                        راجعة
                    </button>
                    <button class="tab-btn" data-tab="pending">
                        <span class="tab-count" id="tab-pending-count">0</span>
                        معلقة
                    </button>
                </div>

                <!-- Tab Content -->
                <div class="tab-content active" id="delivering-tab">
                    <div class="orders-table-container">
                        <div class="table-header">
                            <h3>الطلبات قيد التسليم</h3>
                            <button class="refresh-btn" onclick="refreshOrders()">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23,4 23,10 17,10"/>
                                    <polyline points="1,20 1,14 7,14"/>
                                    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                                </svg>
                                تحديث
                            </button>
                        </div>
                        <div class="table-wrapper">
                            <table class="orders-table" id="delivering-table">
                                <thead>
                                    <tr>
                                        <th>رقم الوصل</th>
                                        <th>اسم المرسل إليه</th>
                                        <th>رقم الهاتف</th>
                                        <th>المدينة</th>
                                        <th>تاريخ الإرسال</th>
                                        <th>المبلغ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="delivering-tbody">
                                    <!-- Orders will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="delayed-tab">
                    <div class="orders-table-container">
                        <div class="table-header">
                            <h3>الطلبات المؤجلة</h3>
                            <button class="refresh-btn" onclick="refreshOrders()">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23,4 23,10 17,10"/>
                                    <polyline points="1,20 1,14 7,14"/>
                                    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                                </svg>
                                تحديث
                            </button>
                        </div>
                        <div class="table-wrapper">
                            <table class="orders-table" id="delayed-table">
                                <thead>
                                    <tr>
                                        <th>رقم الوصل</th>
                                        <th>اسم المرسل إليه</th>
                                        <th>رقم الهاتف</th>
                                        <th>المدينة</th>
                                        <th>تاريخ الإرسال</th>
                                        <th>المبلغ</th>
                                        <th>سبب التأجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="delayed-tbody">
                                    <!-- Orders will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="returned-tab">
                    <div class="orders-table-container">
                        <div class="table-header">
                            <h3>الطلبات الراجعة</h3>
                            <button class="refresh-btn" onclick="refreshOrders()">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23,4 23,10 17,10"/>
                                    <polyline points="1,20 1,14 7,14"/>
                                    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                                </svg>
                                تحديث
                            </button>
                        </div>
                        <div class="table-wrapper">
                            <table class="orders-table" id="returned-table">
                                <thead>
                                    <tr>
                                        <th>رقم الوصل</th>
                                        <th>اسم المرسل إليه</th>
                                        <th>رقم الهاتف</th>
                                        <th>المدينة</th>
                                        <th>تاريخ الإرسال</th>
                                        <th>المبلغ</th>
                                        <th>سبب الإرجاع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="returned-tbody">
                                    <!-- Orders will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="pending-tab">
                    <div class="orders-table-container">
                        <div class="table-header">
                            <h3>الطلبات المعلقة</h3>
                            <button class="refresh-btn" onclick="refreshOrders()">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23,4 23,10 17,10"/>
                                    <polyline points="1,20 1,14 7,14"/>
                                    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                                </svg>
                                تحديث
                            </button>
                        </div>
                        <div class="table-wrapper">
                            <table class="orders-table" id="pending-table">
                                <thead>
                                    <tr>
                                        <th>رقم الوصل</th>
                                        <th>اسم المرسل إليه</th>
                                        <th>رقم الهاتف</th>
                                        <th>المدينة</th>
                                        <th>تاريخ الإرسال</th>
                                        <th>المبلغ</th>
                                        <th>ملاحظات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="pending-tbody">
                                    <!-- Orders will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Order Details Modal -->
    <div id="order-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">تفاصيل الطلب</h3>
                <button class="close-modal" id="close-modal">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Order details will be populated here -->
            </div>
        </div>
    </div>

    <script src="sidebar.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>
