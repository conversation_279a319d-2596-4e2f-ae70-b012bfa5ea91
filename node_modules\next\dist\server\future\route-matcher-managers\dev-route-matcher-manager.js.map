{"version": 3, "sources": ["../../../../src/server/future/route-matcher-managers/dev-route-matcher-manager.ts"], "names": ["DevRouteMatcherManager", "DefaultRouteMatcherManager", "constructor", "production", "ensurer", "dir", "test", "pathname", "options", "match", "validate", "matcher", "duplicated", "some", "duplicate", "definition", "kind", "RouteKind", "APP_PAGE", "APP_ROUTE", "PAGES", "PAGES_API", "matchAll", "reload", "development", "ensure", "matchers", "Object", "entries", "duplicates", "identity", "slice", "Log", "warn", "map", "cyan", "path", "relative", "filename", "join"], "mappings": ";;;;+BAcaA;;;eAAAA;;;2BAda;4CAGiB;6DAE1B;6DACI;4BACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd,MAAMA,+BAA+BC,sDAA0B;IACpEC,YACmBC,YACAC,SACAC,IACjB;QACA,KAAK;0BAJYF;uBACAC;mBACAC;IAGnB;IAEA,MAAaC,KAAKC,QAAgB,EAAEC,OAAqB,EAAoB;QAC3E,mDAAmD;QACnD,MAAMC,QAAQ,MAAM,KAAK,CAACA,MAAMF,UAAUC;QAE1C,wEAAwE;QACxE,uEAAuE;QACvE,qCAAqC;QACrC,OAAOC,UAAU;IACnB;IAEUC,SACRH,QAAgB,EAChBI,OAAqB,EACrBH,OAAqB,EACF;QACnB,MAAMC,QAAQ,KAAK,CAACC,SAASH,UAAUI,SAASH;QAEhD,0EAA0E;QAC1E,eAAe;QACf,8DAA8D;QAC9D,IACEC,SACAE,QAAQC,UAAU,IAClBD,QAAQC,UAAU,CAACC,IAAI,CACrB,CAACC,YACCA,UAAUC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACC,QAAQ,IAChDJ,UAAUC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACE,SAAS,KAErDR,QAAQC,UAAU,CAACC,IAAI,CACrB,CAACC,YACCA,UAAUC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACG,KAAK,IAC7CN,UAAUC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACI,SAAS,GAErD;YACA,OAAO;QACT;QAEA,OAAOZ;IACT;IAEA,OAAca,SACZf,QAAgB,EAChBC,OAAqB,EACoD;QACzE,kCAAkC;QAClC,+GAA+G;QAC/G,MAAM,KAAK,CAACe;QAEZ,uEAAuE;QACvE,gBAAgB;QAChB,WAAW,MAAMC,eAAe,KAAK,CAACF,SAASf,UAAUC,SAAU;YACjE,qEAAqE;YACrE,gEAAgE;YAChE,MAAM,IAAI,CAACJ,OAAO,CAACqB,MAAM,CAACD,aAAajB;YACvC,MAAM,IAAI,CAACJ,UAAU,CAACoB,MAAM;YAE5B,yEAAyE;YACzE,sEAAsE;YACtE,WAAW,MAAMpB,cAAc,IAAI,CAACA,UAAU,CAACmB,QAAQ,CACrDf,UACAC,SACC;gBACD,MAAML;YACR;QACF;QAEA,4EAA4E;QAC5E,gCAAgC;QAChC,OAAO;IACT;IAEA,MAAaoB,SAAwB;QACnC,uCAAuC;QACvC,MAAM,IAAI,CAACpB,UAAU,CAACoB,MAAM;QAE5B,kCAAkC;QAClC,MAAM,KAAK,CAACA;QAEZ,wCAAwC;QACxC,KAAK,MAAM,CAAChB,UAAUmB,SAAS,IAAIC,OAAOC,OAAO,CAC/C,IAAI,CAACF,QAAQ,CAACG,UAAU,EACvB;YACD,0EAA0E;YAC1E,4BAA4B;YAC5B,MAAMC,WAAWJ,QAAQ,CAAC,EAAE,CAACI,QAAQ;YACrC,IAAIJ,SAASK,KAAK,CAAC,GAAGlB,IAAI,CAAC,CAACF,UAAYA,QAAQmB,QAAQ,KAAKA,WAAW;gBACtE;YACF;YAEAE,KAAIC,IAAI,CACN,CAAC,yBAAyB,EAAEP,SACzBQ,GAAG,CAAC,CAACvB,UACJwB,IAAAA,gBAAI,EAACC,aAAI,CAACC,QAAQ,CAAC,IAAI,CAAChC,GAAG,EAAEM,QAAQI,UAAU,CAACuB,QAAQ,IAEzDC,IAAI,CAAC,SAAS,YAAY,EAAEJ,IAAAA,gBAAI,EAAC5B,UAAU,CAAC;QAEnD;IACF;AACF"}