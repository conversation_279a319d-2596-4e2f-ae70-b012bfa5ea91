// Sample data for demonstration
const sampleOrders = [
    {
        id: 'DL001',
        recipientName: 'أحمد محمد علي',
        senderName: 'محمد علي',
        phone: '0501234567',
        city: 'riyadh',
        cityName: 'الرياض',
        address: 'حي النخيل، شارع الملك فهد، مبنى رقم 123',
        date: '2024-06-30',
        amount: 250.00,
        status: 'delivering',
        reason: '',
        notes: 'طلب عادي'
    },
    {
        id: 'DL002',
        recipientName: 'فاطمة أحمد',
        senderName: 'سارة محمد',
        phone: '0507654321',
        city: 'jeddah',
        cityName: 'جدة',
        address: 'حي الصفا، شارع التحلية، برج الأندلس، الدور الثالث',
        date: '2024-06-29',
        amount: 180.50,
        status: 'delayed',
        reason: 'عنوان غير واضح',
        notes: 'يحتاج تأكيد العنوان'
    },
    {
        id: 'DL003',
        recipientName: 'محمد سعد',
        senderName: 'خالد أحمد',
        phone: '0551234567',
        city: 'dammam',
        cityName: 'الدمام',
        address: 'حي الشاطئ، شارع الكورنيش، فيلا رقم 45',
        date: '2024-06-28',
        amount: 320.75,
        status: 'returned',
        reason: 'رفض الاستلام',
        notes: 'العميل غير متواجد'
    },
    {
        id: 'DL004',
        recipientName: 'نورا خالد',
        phone: '0509876543',
        city: 'mecca',
        cityName: 'مكة',
        date: '2024-06-30',
        amount: 95.25,
        status: 'pending',
        reason: '',
        notes: 'في انتظار التأكيد'
    },
    {
        id: 'DL005',
        recipientName: 'عبدالله أحمد',
        phone: '0556789012',
        city: 'riyadh',
        cityName: 'الرياض',
        date: '2024-06-30',
        amount: 450.00,
        status: 'delivering',
        reason: '',
        notes: 'طلب مستعجل'
    },
    {
        id: 'DL006',
        recipientName: 'سارة محمد',
        phone: '0503456789',
        city: 'jeddah',
        cityName: 'جدة',
        date: '2024-06-29',
        amount: 275.80,
        status: 'delayed',
        reason: 'مشكلة في التوصيل',
        notes: 'إعادة جدولة'
    },
    {
        id: 'DL007',
        recipientName: 'خالد عبدالرحمن',
        phone: '0558901234',
        city: 'riyadh',
        cityName: 'الرياض',
        date: '2024-06-27',
        amount: 125.50,
        status: 'returned',
        reason: 'عنوان خاطئ',
        notes: 'تحديث العنوان مطلوب'
    },
    {
        id: 'DL008',
        recipientName: 'مريم سالم',
        phone: '0504567890',
        city: 'dammam',
        cityName: 'الدمام',
        date: '2024-06-30',
        amount: 380.25,
        status: 'pending',
        reason: '',
        notes: 'تأكيد الطلب'
    }
];

// Global variables
let currentOrders = [...sampleOrders];
let filteredOrders = [...sampleOrders];
let currentTab = 'delivering';

// DOM Elements
const searchInput = document.getElementById('search-input');
const clearSearchBtn = document.getElementById('clear-search');
const dateFilter = document.getElementById('date-filter');
const cityFilter = document.getElementById('city-filter');
const tabButtons = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');
// Logout and notification buttons are now handled by sidebar
const orderModal = document.getElementById('order-modal');
const closeModalBtn = document.getElementById('close-modal');

// Initialize the dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    if (!localStorage.getItem('isLoggedIn')) {
        window.location.href = 'index.html';
        return;
    }

    initializeDashboard();
});

function initializeDashboard() {
    // Load user info
    loadUserInfo();
    
    // Add event listeners
    addEventListeners();
    
    // Initial data load
    updateStatistics();
    renderOrders();
    
    // Auto-refresh every 30 seconds
    setInterval(() => {
        refreshOrders();
    }, 30000);

    // Show welcome message with shortcuts hint
    showWelcomeMessage();
}

function loadUserInfo() {
    // User info is now handled by sidebar
}

function addEventListeners() {
    // Search functionality
    searchInput.addEventListener('input', handleSearch);
    clearSearchBtn.addEventListener('click', clearSearch);
    
    // Filters
    dateFilter.addEventListener('change', applyFilters);
    cityFilter.addEventListener('change', applyFilters);
    
    // Tab switching
    tabButtons.forEach(btn => {
        btn.addEventListener('click', () => switchTab(btn.dataset.tab));
    });
    
    // Logout is now handled by sidebar
    
    // Modal
    closeModalBtn.addEventListener('click', closeModal);
    orderModal.addEventListener('click', (e) => {
        if (e.target === orderModal) closeModal();
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);

    // Add click handlers for interactive elements
    addClickHandlers();
}

function handleSearch() {
    const query = searchInput.value.trim();
    
    if (query) {
        clearSearchBtn.classList.add('visible');
    } else {
        clearSearchBtn.classList.remove('visible');
    }
    
    applyFilters();
}

function clearSearch() {
    searchInput.value = '';
    clearSearchBtn.classList.remove('visible');
    applyFilters();
}

function addClickHandlers() {
    // Make stat cards clickable
    document.querySelectorAll('.stat-card').forEach(card => {
        card.style.cursor = 'pointer';
        card.addEventListener('click', () => {
            const cardClass = card.className;
            if (cardClass.includes('delivering')) {
                switchTab('delivering');
            } else if (cardClass.includes('delayed')) {
                switchTab('delayed');
            } else if (cardClass.includes('returned')) {
                switchTab('returned');
            } else if (cardClass.includes('pending')) {
                switchTab('pending');
            }
        });

        // Add hover effect
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-2px)';
            card.style.boxShadow = '10px 10px 20px #a3b1c6, -10px -10px 20px #ffffff';
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0)';
            card.style.boxShadow = '8px 8px 16px #a3b1c6, -8px -8px 16px #ffffff';
        });
    });

    // Make quick action buttons functional
    addQuickActionHandlers();

    // Make recent orders clickable
    addRecentOrdersHandlers();

    // Add navigation shortcuts to action buttons
    addActionButtonHandlers();
}

function addQuickActionHandlers() {
    // Add New Order button
    const addOrderBtn = document.querySelector('[onclick="addNewOrder()"]');
    if (addOrderBtn) {
        addOrderBtn.onclick = () => {
            window.location.href = 'orders.html';
        };
    }

    // View All Orders button
    const viewAllBtn = document.querySelector('[onclick="viewAllOrders()"]');
    if (viewAllBtn) {
        viewAllBtn.onclick = () => {
            window.location.href = 'orders.html';
        };
    }

    // Manage Drivers button
    const manageDriversBtn = document.querySelector('[onclick="manageDrivers()"]');
    if (manageDriversBtn) {
        manageDriversBtn.onclick = () => {
            window.location.href = 'drivers.html';
        };
    }

    // View Reports button
    const viewReportsBtn = document.querySelector('[onclick="viewReports()"]');
    if (viewReportsBtn) {
        viewReportsBtn.onclick = () => {
            window.location.href = 'reports.html';
        };
    }
}

function addRecentOrdersHandlers() {
    // Make order rows clickable
    document.addEventListener('click', (e) => {
        const orderRow = e.target.closest('.order-row');
        if (orderRow) {
            const orderId = orderRow.dataset.orderId;
            if (orderId) {
                showOrderDetails(orderId);
            }
        }
    });
}

function addActionButtonHandlers() {
    // Add click handlers for action buttons that might not have onclick attributes
    const actionButtons = document.querySelectorAll('.action-btn, .btn');
    actionButtons.forEach(btn => {
        if (!btn.onclick && !btn.getAttribute('onclick')) {
            const text = btn.textContent.trim();

            switch(text) {
                case 'طلب جديد':
                case 'إضافة طلب':
                    btn.onclick = () => window.location.href = 'orders.html';
                    break;
                case 'عرض الكل':
                case 'جميع الطلبات':
                    btn.onclick = () => window.location.href = 'orders.html';
                    break;
                case 'إدارة المندوبين':
                    btn.onclick = () => window.location.href = 'drivers.html';
                    break;
                case 'إدارة العملاء':
                    btn.onclick = () => window.location.href = 'customers.html';
                    break;
                case 'التقارير':
                case 'عرض التقارير':
                    btn.onclick = () => window.location.href = 'reports.html';
                    break;
                case 'الإعدادات':
                    btn.onclick = () => window.location.href = 'settings.html';
                    break;
                case 'إسناد الطلبات':
                    btn.onclick = () => window.location.href = 'assignments.html';
                    break;
            }
        }
    });
}

function applyFilters() {
    const searchQuery = searchInput.value.trim().toLowerCase();
    const dateFilterValue = dateFilter.value;
    const cityFilterValue = cityFilter.value;
    
    filteredOrders = currentOrders.filter(order => {
        // Search filter
        const matchesSearch = !searchQuery || 
            order.id.toLowerCase().includes(searchQuery) ||
            order.phone.includes(searchQuery) ||
            order.recipientName.toLowerCase().includes(searchQuery);
        
        // Date filter
        const matchesDate = !dateFilterValue || checkDateFilter(order.date, dateFilterValue);
        
        // City filter
        const matchesCity = !cityFilterValue || order.city === cityFilterValue;
        
        return matchesSearch && matchesDate && matchesCity;
    });
    
    updateStatistics();
    renderOrders();
}

function checkDateFilter(orderDate, filterValue) {
    const today = new Date();
    const orderDateObj = new Date(orderDate);
    
    switch (filterValue) {
        case 'today':
            return orderDateObj.toDateString() === today.toDateString();
        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            return orderDateObj.toDateString() === yesterday.toDateString();
        case 'week':
            const weekAgo = new Date(today);
            weekAgo.setDate(weekAgo.getDate() - 7);
            return orderDateObj >= weekAgo;
        case 'month':
            const monthAgo = new Date(today);
            monthAgo.setMonth(monthAgo.getMonth() - 1);
            return orderDateObj >= monthAgo;
        default:
            return true;
    }
}

function updateStatistics() {
    const stats = {
        delivering: filteredOrders.filter(order => order.status === 'delivering').length,
        delayed: filteredOrders.filter(order => order.status === 'delayed').length,
        returned: filteredOrders.filter(order => order.status === 'returned').length,
        pending: filteredOrders.filter(order => order.status === 'pending').length
    };
    
    // Update stat cards
    document.getElementById('delivering-count').textContent = stats.delivering;
    document.getElementById('delayed-count').textContent = stats.delayed;
    document.getElementById('returned-count').textContent = stats.returned;
    document.getElementById('pending-count').textContent = stats.pending;
    
    // Update tab counts
    document.getElementById('tab-delivering-count').textContent = stats.delivering;
    document.getElementById('tab-delayed-count').textContent = stats.delayed;
    document.getElementById('tab-returned-count').textContent = stats.returned;
    document.getElementById('tab-pending-count').textContent = stats.pending;
}

function switchTab(tabName) {
    currentTab = tabName;
    
    // Update tab buttons
    tabButtons.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName);
    });
    
    // Update tab contents
    tabContents.forEach(content => {
        content.classList.toggle('active', content.id === `${tabName}-tab`);
    });
    
    renderOrders();
}

function renderOrders() {
    const statusOrders = filteredOrders.filter(order => order.status === currentTab);
    const tbody = document.getElementById(`${currentTab}-tbody`);
    
    if (statusOrders.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="empty-state">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M16 16s-1.5-2-4-2-4 2-4 2"/>
                            <line x1="9" y1="9" x2="9.01" y2="9"/>
                            <line x1="15" y1="9" x2="15.01" y2="9"/>
                        </svg>
                        <h3>لا توجد طلبات</h3>
                        <p>لم يتم العثور على طلبات في هذا القسم</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = statusOrders.map(order => createOrderRow(order)).join('');
}

function createOrderRow(order) {
    const reasonColumn = currentTab === 'delayed' || currentTab === 'returned' ? 
        `<td>${order.reason || '-'}</td>` : 
        currentTab === 'pending' ? `<td>${order.notes || '-'}</td>` : '';
    
    return `
        <tr class="order-row" data-order-id="${order.id}" style="cursor: pointer;" onclick="showOrderDetails('${order.id}')">
            <td><span class="order-id">${order.id}</span></td>
            <td>${order.recipientName}</td>
            <td>${order.phone}</td>
            <td>${order.cityName}</td>
            <td>${formatDate(order.date)}</td>
            <td class="amount" data-original-amount="${order.amount}">${formatCurrency(order.amount)}</td>
            ${reasonColumn}
            <td>
                <div class="action-buttons">
                    <button class="action-btn" onclick="event.stopPropagation(); showOrderDetails('${order.id}')">تفاصيل</button>
                    ${getActionButtons(order)}
                </div>
            </td>
        </tr>
    `;
}

function getActionButtons(order) {
    const buttons = [];
    
    switch (order.status) {
        case 'delivering':
            buttons.push('<button class="action-btn success" onclick="markAsDelivered(\'' + order.id + '\')">تم التسليم</button>');
            buttons.push('<button class="action-btn warning" onclick="markAsDelayed(\'' + order.id + '\')">تأجيل</button>');
            buttons.push('<button class="action-btn danger" onclick="markAsReturned(\'' + order.id + '\')">إرجاع</button>');
            break;
        case 'delayed':
            buttons.push('<button class="action-btn success" onclick="markAsDelivering(\'' + order.id + '\')">إعادة التسليم</button>');
            buttons.push('<button class="action-btn danger" onclick="markAsReturned(\'' + order.id + '\')">إرجاع</button>');
            break;
        case 'returned':
            buttons.push('<button class="action-btn primary" onclick="markAsDelivering(\'' + order.id + '\')">إعادة المحاولة</button>');
            break;
        case 'pending':
            buttons.push('<button class="action-btn success" onclick="markAsDelivering(\'' + order.id + '\')">تأكيد</button>');
            buttons.push('<button class="action-btn danger" onclick="cancelOrder(\'' + order.id + '\')">إلغاء</button>');
            break;
    }
    
    buttons.push('<button class="action-btn primary" onclick="showOrderDetails(\'' + order.id + '\')">التفاصيل</button>');
    
    return buttons.join('');
}

// Order status management functions
function markAsDelivered(orderId) {
    if (confirm('هل أنت متأكد من تسليم هذا الطلب؟')) {
        updateOrderStatus(orderId, 'delivered');
        showNotification('تم تسليم الطلب بنجاح', 'success');
    }
}

function markAsDelayed(orderId) {
    const reason = prompt('سبب التأجيل:');
    if (reason) {
        updateOrderStatus(orderId, 'delayed', reason);
        showNotification('تم تأجيل الطلب', 'warning');
    }
}

function markAsReturned(orderId) {
    const reason = prompt('سبب الإرجاع:');
    if (reason) {
        updateOrderStatus(orderId, 'returned', reason);
        showNotification('تم إرجاع الطلب', 'error');
    }
}

function markAsDelivering(orderId) {
    updateOrderStatus(orderId, 'delivering');
    showNotification('تم تحديث حالة الطلب إلى قيد التسليم', 'info');
}

function cancelOrder(orderId) {
    if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
        const orderIndex = currentOrders.findIndex(order => order.id === orderId);
        if (orderIndex !== -1) {
            currentOrders.splice(orderIndex, 1);
            applyFilters();
            showNotification('تم إلغاء الطلب', 'error');
        }
    }
}

function updateOrderStatus(orderId, newStatus, reason = '') {
    const order = currentOrders.find(order => order.id === orderId);
    if (order) {
        order.status = newStatus;
        if (reason) {
            order.reason = reason;
        }
        applyFilters();
    }
}

// Modal functions
function showOrderDetails(orderId) {
    const order = currentOrders.find(order => order.id === orderId);
    if (!order) return;

    const modalBody = document.getElementById('modal-body');
    modalBody.innerHTML = `
        <div style="display: grid; gap: 20px;">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div>
                    <strong>رقم الوصل:</strong><br>
                    <span style="color: #3b82f6; font-weight: 600;">${order.id}</span>
                </div>
                <div>
                    <strong>اسم المرسل إليه:</strong><br>
                    ${order.recipientName}
                </div>
                <div>
                    <strong>رقم الهاتف:</strong><br>
                    <a href="tel:${order.phone}" style="color: #10b981;">${order.phone}</a>
                </div>
                <div>
                    <strong>المدينة:</strong><br>
                    ${order.cityName}
                </div>
                <div>
                    <strong>تاريخ الإرسال:</strong><br>
                    ${formatDate(order.date)}
                </div>
                <div>
                    <strong>المبلغ:</strong><br>
                    <span style="color: #059669; font-weight: 600;">${order.amount.toFixed(2)} ر.س</span>
                </div>
            </div>

            <div>
                <strong>الحالة:</strong><br>
                <span class="status-badge status-${order.status}">${getStatusText(order.status)}</span>
            </div>

            ${order.reason ? `
                <div>
                    <strong>السبب/الملاحظات:</strong><br>
                    <div style="background: rgba(163, 177, 198, 0.1); padding: 10px; border-radius: 8px; margin-top: 5px;">
                        ${order.reason}
                    </div>
                </div>
            ` : ''}

            ${order.notes ? `
                <div>
                    <strong>ملاحظات إضافية:</strong><br>
                    <div style="background: rgba(163, 177, 198, 0.1); padding: 10px; border-radius: 8px; margin-top: 5px;">
                        ${order.notes}
                    </div>
                </div>
            ` : ''}

            <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-top: 20px;">
                ${getModalActionButtons(order)}
            </div>
        </div>
    `;

    orderModal.classList.remove('hidden');
}

function getModalActionButtons(order) {
    const buttons = [];

    switch (order.status) {
        case 'delivering':
            buttons.push('<button class="action-btn success" onclick="markAsDelivered(\'' + order.id + '\'); closeModal();">تم التسليم</button>');
            buttons.push('<button class="action-btn warning" onclick="markAsDelayed(\'' + order.id + '\'); closeModal();">تأجيل</button>');
            buttons.push('<button class="action-btn danger" onclick="markAsReturned(\'' + order.id + '\'); closeModal();">إرجاع</button>');
            break;
        case 'delayed':
            buttons.push('<button class="action-btn success" onclick="markAsDelivering(\'' + order.id + '\'); closeModal();">إعادة التسليم</button>');
            buttons.push('<button class="action-btn danger" onclick="markAsReturned(\'' + order.id + '\'); closeModal();">إرجاع</button>');
            break;
        case 'returned':
            buttons.push('<button class="action-btn primary" onclick="markAsDelivering(\'' + order.id + '\'); closeModal();">إعادة المحاولة</button>');
            break;
        case 'pending':
            buttons.push('<button class="action-btn success" onclick="markAsDelivering(\'' + order.id + '\'); closeModal();">تأكيد</button>');
            buttons.push('<button class="action-btn danger" onclick="cancelOrder(\'' + order.id + '\'); closeModal();">إلغاء</button>');
            break;
    }

    return buttons.join('');
}

function closeModal() {
    orderModal.classList.add('hidden');
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function getStatusText(status) {
    const statusTexts = {
        'delivering': 'قيد التسليم',
        'delayed': 'مؤجل',
        'returned': 'راجع',
        'pending': 'معلق',
        'delivered': 'تم التسليم'
    };
    return statusTexts[status] || status;
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <svg style="width: 20px; height: 20px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                ${getNotificationIcon(type)}
            </svg>
            <span>${message}</span>
        </div>
    `;

    // Add notification styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${getNotificationColor(type)};
        color: white;
        padding: 15px 20px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 1001;
        animation: slideInRight 0.3s ease-out;
        max-width: 400px;
        font-weight: 500;
    `;

    document.body.appendChild(notification);

    // Remove after 4 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 4000);
}

function getNotificationIcon(type) {
    const icons = {
        'success': '<path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/><polyline points="22,4 12,14.01 9,11.01"/>',
        'error': '<circle cx="12" cy="12" r="10"/><line x1="15" y1="9" x2="9" y2="15"/><line x1="9" y1="9" x2="15" y2="15"/>',
        'warning': '<path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/><line x1="12" y1="9" x2="12" y2="13"/><line x1="12" y1="17" x2="12.01" y2="17"/>',
        'info': '<circle cx="12" cy="12" r="10"/><line x1="12" y1="16" x2="12" y2="12"/><line x1="12" y1="8" x2="12.01" y2="8"/>'
    };
    return icons[type] || icons.info;
}

function getNotificationColor(type) {
    const colors = {
        'success': 'linear-gradient(135deg, #10b981, #059669)',
        'error': 'linear-gradient(135deg, #ef4444, #dc2626)',
        'warning': 'linear-gradient(135deg, #f59e0b, #d97706)',
        'info': 'linear-gradient(135deg, #3b82f6, #2563eb)'
    };
    return colors[type] || colors.info;
}

function refreshOrders() {
    // In a real application, this would fetch data from the server
    showNotification('تم تحديث البيانات', 'info');
    updateStatistics();
    renderOrders();
}

function showOrderDetails(orderId) {
    // Find the order
    const order = currentOrders.find(o => o.id === orderId);
    if (!order) {
        showNotification('لم يتم العثور على الطلب', 'error');
        return;
    }

    // Create modal
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>تفاصيل الطلب ${orderId}</h3>
                <button class="close-modal" onclick="this.closest('.modal').remove()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div style="display: grid; gap: 20px;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div>
                            <strong>رقم الوصل:</strong><br>
                            <span style="color: #3b82f6; font-weight: 600;">${order.id}</span>
                        </div>
                        <div>
                            <strong>اسم المرسل إليه:</strong><br>
                            ${order.recipientName}
                        </div>
                        <div>
                            <strong>اسم المرسل:</strong><br>
                            ${order.senderName}
                        </div>
                        <div>
                            <strong>رقم الهاتف:</strong><br>
                            <a href="tel:${order.phone}" style="color: #10b981;">${order.phone}</a>
                        </div>
                        <div>
                            <strong>المدينة:</strong><br>
                            ${order.cityName}
                        </div>
                        <div>
                            <strong>تاريخ الإرسال:</strong><br>
                            ${formatDate(order.date)}
                        </div>
                        <div>
                            <strong>المبلغ:</strong><br>
                            <span style="color: #059669; font-weight: 600;">${formatCurrency(order.amount)}</span>
                        </div>
                        <div>
                            <strong>الحالة:</strong><br>
                            <span class="status-badge status-${order.status}">${getStatusText(order.status)}</span>
                        </div>
                    </div>

                    <div>
                        <strong>العنوان:</strong><br>
                        <div style="background: rgba(163, 177, 198, 0.1); padding: 10px; border-radius: 8px; margin-top: 5px;">
                            ${order.address}
                        </div>
                    </div>

                    ${order.notes ? `
                        <div>
                            <strong>ملاحظات:</strong><br>
                            <div style="background: rgba(163, 177, 198, 0.1); padding: 10px; border-radius: 8px; margin-top: 5px;">
                                ${order.notes}
                            </div>
                        </div>
                    ` : ''}

                    <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-top: 20px;">
                        <button class="btn btn-primary" onclick="window.location.href='orders.html'">إدارة الطلبات</button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close on background click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function getStatusText(status) {
    const statuses = {
        'pending': 'معلق',
        'delivering': 'قيد التسليم',
        'delayed': 'مؤجل',
        'returned': 'راجع',
        'delivered': 'تم التسليم'
    };
    return statuses[status] || status;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function showWelcomeMessage() {
    // Only show on first visit or if user hasn't seen it recently
    const lastShown = localStorage.getItem('welcomeMessageShown');
    const today = new Date().toDateString();

    if (lastShown !== today) {
        setTimeout(() => {
            const welcomeNotification = document.createElement('div');
            welcomeNotification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="font-size: 24px;">👋</div>
                    <div>
                        <div style="font-weight: 600; margin-bottom: 5px;">مرحباً بك في نظام إدارة التوصيل!</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">
                            اضغط <kbd style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 4px; font-size: 0.8rem;">?</kbd>
                            لعرض الاختصارات أو
                            <kbd style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 4px; font-size: 0.8rem;">F1</kbd>
                            للمساعدة
                        </div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()"
                            style="background: none; border: none; color: white; cursor: pointer; font-size: 18px; margin-right: auto;">×</button>
                </div>
            `;

            welcomeNotification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                padding: 20px;
                border-radius: 15px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
                z-index: 1001;
                animation: slideInRight 0.5s ease-out;
                max-width: 400px;
                font-family: 'Cairo', sans-serif;
            `;

            document.body.appendChild(welcomeNotification);

            // Auto-remove after 8 seconds
            setTimeout(() => {
                if (document.body.contains(welcomeNotification)) {
                    welcomeNotification.style.animation = 'slideOutRight 0.3s ease-in';
                    setTimeout(() => {
                        if (document.body.contains(welcomeNotification)) {
                            document.body.removeChild(welcomeNotification);
                        }
                    }, 300);
                }
            }, 8000);

            // Mark as shown for today
            localStorage.setItem('welcomeMessageShown', today);
        }, 1500); // Show after 1.5 seconds
    }
}

function handleLogout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        // Clear session data
        localStorage.removeItem('isLoggedIn');

        // Redirect to login page
        window.location.href = 'index.html';
    }
}

function handleKeyboardShortcuts(e) {
    // Prevent shortcuts when typing in input fields
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
        // Only allow Escape to work in input fields
        if (e.key === 'Escape') {
            e.target.blur();
            closeModal();
        }
        return;
    }

    // Ctrl/Cmd + K to focus search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    // Ctrl/Cmd + R to refresh data
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        refreshOrders();
    }

    // Ctrl/Cmd + N to add new order (redirect to orders page)
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        window.location.href = 'orders.html';
    }

    // Ctrl/Cmd + D to go to dashboard
    if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
        e.preventDefault();
        window.location.href = 'dashboard.html';
    }

    // Ctrl/Cmd + O to go to orders
    if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
        e.preventDefault();
        window.location.href = 'orders.html';
    }

    // Ctrl/Cmd + U to go to drivers
    if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
        e.preventDefault();
        window.location.href = 'drivers.html';
    }

    // Ctrl/Cmd + C to go to customers
    if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
        e.preventDefault();
        window.location.href = 'customers.html';
    }

    // Ctrl/Cmd + A to go to assignments
    if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
        e.preventDefault();
        window.location.href = 'assignments.html';
    }

    // Ctrl/Cmd + S to go to settings
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        window.location.href = 'settings.html';
    }

    // Ctrl/Cmd + P to go to reports
    if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        window.location.href = 'reports.html';
    }

    // Ctrl/Cmd + L to logout
    if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
        e.preventDefault();
        handleLogout();
    }

    // Escape to close modal
    if (e.key === 'Escape') {
        closeModal();
    }

    // Number keys to switch tabs
    if (e.key >= '1' && e.key <= '4') {
        const tabIndex = parseInt(e.key) - 1;
        const tabs = ['delivering', 'delayed', 'returned', 'pending'];
        if (tabs[tabIndex]) {
            switchTab(tabs[tabIndex]);
        }
    }

    // F1 to show help
    if (e.key === 'F1') {
        e.preventDefault();
        showKeyboardShortcutsHelp();
    }

    // F5 to refresh (override default)
    if (e.key === 'F5') {
        e.preventDefault();
        refreshOrders();
    }

    // Alt + Arrow keys for navigation
    if (e.altKey) {
        switch(e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                window.history.back();
                break;
            case 'ArrowRight':
                e.preventDefault();
                window.history.forward();
                break;
            case 'ArrowUp':
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
                break;
            case 'ArrowDown':
                e.preventDefault();
                window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                break;
        }
    }
}

// Show keyboard shortcuts help
function showKeyboardShortcutsHelp() {
    const helpModal = document.createElement('div');
    helpModal.className = 'modal';
    helpModal.innerHTML = `
        <div class="modal-content large">
            <div class="modal-header">
                <h3>اختصارات لوحة المفاتيح</h3>
                <button class="close-modal" onclick="this.closest('.modal').remove()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div class="shortcuts-section">
                        <h4>🔍 البحث والتنقل</h4>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>K</kbd>
                            <span>البحث السريع</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>R</kbd>
                            <span>تحديث البيانات</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>F5</kbd>
                            <span>تحديث الصفحة</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Esc</kbd>
                            <span>إغلاق النوافذ</span>
                        </div>
                    </div>

                    <div class="shortcuts-section">
                        <h4>📄 إدارة الصفحات</h4>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>D</kbd>
                            <span>لوحة التحكم</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>O</kbd>
                            <span>إدارة الطلبات</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>U</kbd>
                            <span>إدارة المندوبين</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>C</kbd>
                            <span>إدارة العملاء</span>
                        </div>
                    </div>

                    <div class="shortcuts-section">
                        <h4>⚡ إجراءات سريعة</h4>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>N</kbd>
                            <span>طلب جديد</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>A</kbd>
                            <span>إسناد الطلبات</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>P</kbd>
                            <span>التقارير</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>S</kbd>
                            <span>الإعدادات</span>
                        </div>
                    </div>

                    <div class="shortcuts-section">
                        <h4>🎯 التبويبات والتنقل</h4>
                        <div class="shortcut-item">
                            <kbd>1</kbd> - <kbd>4</kbd>
                            <span>التبديل بين التبويبات</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Alt</kbd> + <kbd>←</kbd>
                            <span>الصفحة السابقة</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Alt</kbd> + <kbd>→</kbd>
                            <span>الصفحة التالية</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Alt</kbd> + <kbd>↑</kbd>
                            <span>أعلى الصفحة</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Alt</kbd> + <kbd>↓</kbd>
                            <span>أسفل الصفحة</span>
                        </div>
                    </div>

                    <div class="shortcuts-section">
                        <h4>🔐 النظام</h4>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>L</kbd>
                            <span>تسجيل الخروج</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>F1</kbd>
                            <span>عرض هذه المساعدة</span>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 15px; background: rgba(59, 130, 246, 0.1); border-radius: 10px;">
                    <p style="margin: 0; color: #1e40af; font-weight: 500;">
                        💡 نصيحة: يمكنك استخدام هذه الاختصارات في أي صفحة من صفحات النظام لتسريع عملك!
                    </p>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(helpModal);

    // Close on background click
    helpModal.addEventListener('click', (e) => {
        if (e.target === helpModal) {
            helpModal.remove();
        }
    });
}

// Add keyboard shortcuts styles
const keyboardStyles = document.createElement('style');
keyboardStyles.textContent = `
    .shortcuts-section {
        background: rgba(163, 177, 198, 0.1);
        padding: 20px;
        border-radius: 15px;
    }

    .shortcuts-section h4 {
        margin: 0 0 15px 0;
        color: #1a202c;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .shortcut-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(163, 177, 198, 0.2);
    }

    .shortcut-item:last-child {
        border-bottom: none;
    }

    .shortcut-item kbd {
        background: #e0e5ec;
        border: 1px solid #a3b1c6;
        border-radius: 6px;
        padding: 4px 8px;
        font-family: 'Cairo', sans-serif;
        font-size: 0.8rem;
        font-weight: 600;
        color: #374151;
        box-shadow: 0 2px 4px rgba(163, 177, 198, 0.3);
        margin: 0 2px;
    }

    .shortcut-item span {
        color: #4a5568;
        font-size: 0.9rem;
    }
`;

// Add notification animation styles
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }
`;

document.head.appendChild(keyboardStyles);
document.head.appendChild(notificationStyles);
