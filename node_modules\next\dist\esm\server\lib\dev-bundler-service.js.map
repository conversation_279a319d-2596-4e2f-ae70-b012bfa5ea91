{"version": 3, "sources": ["../../../src/server/lib/dev-bundler-service.ts"], "names": ["createRequestResponseMocks", "DevBundlerService", "constructor", "bundler", "handler", "ensurePage", "definition", "hotReloader", "logErrorWithOriginalStack", "args", "getFallbackErrorComponents", "url", "buildFallbackError", "page", "clientOnly", "undefined", "getCompilationError", "errors", "getCompilationErrors", "revalidate", "url<PERSON><PERSON>", "revalidateHeaders", "opts", "revalidateOpts", "mocked", "headers", "req", "res", "hasStreamed", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "unstable_onlyGenerated", "Error"], "mappings": "AAIA,SAASA,0BAA0B,QAAQ,iBAAgB;AAE3D;;;CAGC,GACD,OAAO,MAAMC;IACXC,YACmBC,SACAC,QACjB;uBAFiBD;uBACAC;aAGZC,aAAyD,OAC9DC;YAEA,oDAAoD;YACpD,OAAO,MAAM,IAAI,CAACH,OAAO,CAACI,WAAW,CAACF,UAAU,CAACC;QACnD;aAEOE,4BACL,OAAO,GAAGC;YACR,OAAO,MAAM,IAAI,CAACN,OAAO,CAACK,yBAAyB,IAAIC;QACzD;IAZC;IAcH,MAAaC,2BAA2BC,GAAY,EAAE;QACpD,MAAM,IAAI,CAACR,OAAO,CAACI,WAAW,CAACK,kBAAkB;QACjD,4DAA4D;QAC5D,8DAA8D;QAC9D,MAAM,IAAI,CAACT,OAAO,CAACI,WAAW,CAACF,UAAU,CAAC;YACxCQ,MAAM;YACNC,YAAY;YACZR,YAAYS;YACZJ;QACF;IACF;IAEA,MAAaK,oBAAoBH,IAAY,EAAE;QAC7C,MAAMI,SAAS,MAAM,IAAI,CAACd,OAAO,CAACI,WAAW,CAACW,oBAAoB,CAACL;QACnE,IAAI,CAACI,QAAQ;QAEb,wCAAwC;QACxC,OAAOA,MAAM,CAAC,EAAE;IAClB;IAEA,MAAaE,WAAW,EACtBC,OAAO,EACPC,iBAAiB,EACjBC,MAAMC,cAAc,EAKrB,EAAE;QACD,MAAMC,SAASxB,2BAA2B;YACxCW,KAAKS;YACLK,SAASJ;QACX;QAEA,MAAM,IAAI,CAACjB,OAAO,CAACoB,OAAOE,GAAG,EAAEF,OAAOG,GAAG;QACzC,MAAMH,OAAOG,GAAG,CAACC,WAAW;QAE5B,IACEJ,OAAOG,GAAG,CAACE,SAAS,CAAC,sBAAsB,iBAC3C,CAAEL,CAAAA,OAAOG,GAAG,CAACG,UAAU,KAAK,OAAOP,eAAeQ,sBAAsB,AAAD,GACvE;YACA,MAAM,IAAIC,MAAM,CAAC,iBAAiB,EAAER,OAAOG,GAAG,CAACG,UAAU,CAAC,CAAC;QAC7D;QAEA,OAAO,CAAC;IACV;AACF"}