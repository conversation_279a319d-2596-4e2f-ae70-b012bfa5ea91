{"version": 3, "sources": ["../../../src/server/lib/mock-request.ts"], "names": ["MockedRequest", "MockedResponse", "createRequestResponseMocks", "Stream", "Readable", "constructor", "url", "headers", "method", "socket", "readable", "httpVersion", "httpVersionMajor", "httpVersionMinor", "Proxy", "get", "_target", "prop", "Error", "undefined", "bodyReadable", "on", "emit", "headersDistinct", "key", "value", "Object", "entries", "Array", "isArray", "_read", "size", "connection", "aborted", "complete", "trailers", "trailersDistinct", "rawTrailers", "rawHeaders", "setTimeout", "Writable", "res", "statusMessage", "finished", "headersSent", "buffers", "statusCode", "fromNodeOutgoingHttpHeaders", "Headers", "head<PERSON><PERSON><PERSON>", "Promise", "resolve", "headPromiseResolve", "hasStreamed", "reject", "err", "then", "val", "resWriter", "append<PERSON><PERSON>er", "name", "values", "v", "append", "isSent", "write", "chunk", "push", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "from", "end", "arguments", "_implicitHeader", "_write", "_encoding", "callback", "writeHead", "length", "i", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "has", "<PERSON><PERSON><PERSON><PERSON>", "getHeaders", "toNodeOutgoingHttpHeaders", "getHeaderNames", "keys", "delete", "set", "toString", "removeHeader", "flushHeaders", "strictContent<PERSON>ength", "writeEarlyHints", "req", "assignSocket", "detachSocket", "writeContinue", "writeProcessing", "upgrading", "chunkedEncoding", "shouldKeepAlive", "useChunkedEncodingByDefault", "sendDate", "addTrailers"], "mappings": ";;;;;;;;;;;;;;;;IAwBaA,aAAa;eAAbA;;IA0HAC,cAAc;eAAdA;;IA2SGC,0BAA0B;eAA1BA;;;+DAnbG;uBAIZ;;;;;;AAUA,MAAMF,sBAAsBG,eAAM,CAACC,QAAQ;IA8BhDC,YAAY,EACVC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,SAAS,IAAI,EACbC,QAAQ,EACa,CAAE;QACvB,KAAK;QA9BP,8EAA8E;aAC9DC,cAAc;aACdC,mBAAmB;aACnBC,mBAAmB;QAInC,qEAAqE;QACrE,0EAA0E;QAC1E,4BAA4B;aACrBJ,SAAiB,IAAIK,MAAiB,CAAC,GAAgB;YAC5DC,KAAK,CAACC,SAASC;gBACb,IAAIA,SAAS,eAAeA,SAAS,iBAAiB;oBACpD,MAAM,IAAIC,MAAM;gBAClB;gBAEA,IAAID,SAAS,iBAAiB,OAAOE;gBACrC,0EAA0E;gBAC1E,kDAAkD;gBAClD,OAAO;YACT;QACF;QAWE,IAAI,CAACb,GAAG,GAAGA;QACX,IAAI,CAACC,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;QAEd,IAAIE,UAAU;YACZ,IAAI,CAACU,YAAY,GAAGV;YACpB,IAAI,CAACU,YAAY,CAACC,EAAE,CAAC,OAAO,IAAM,IAAI,CAACC,IAAI,CAAC;YAC5C,IAAI,CAACF,YAAY,CAACC,EAAE,CAAC,SAAS,IAAM,IAAI,CAACC,IAAI,CAAC;QAChD;QAEA,IAAIb,QAAQ;YACV,IAAI,CAACA,MAAM,GAAGA;QAChB;IACF;IAEA,IAAWc,kBAAyC;QAClD,MAAMhB,UAAiC,CAAC;QACxC,KAAK,MAAM,CAACiB,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAC,IAAI,CAACpB,OAAO,EAAG;YACvD,IAAI,CAACkB,OAAO;YAEZlB,OAAO,CAACiB,IAAI,GAAGI,MAAMC,OAAO,CAACJ,SAASA,QAAQ;gBAACA;aAAM;QACvD;QAEA,OAAOlB;IACT;IAEOuB,MAAMC,IAAY,EAAQ;QAC/B,IAAI,IAAI,CAACX,YAAY,EAAE;YACrB,OAAO,IAAI,CAACA,YAAY,CAACU,KAAK,CAACC;QACjC,OAAO;YACL,IAAI,CAACT,IAAI,CAAC;YACV,IAAI,CAACA,IAAI,CAAC;QACZ;IACF;IAEA;;;;GAIC,GACD,IAAWU,aAAqB;QAC9B,OAAO,IAAI,CAACvB,MAAM;IACpB;IAEA,wEAAwE;IACxE,oBAAoB;IAEpB,IAAWwB,UAAmB;QAC5B,MAAM,IAAIf,MAAM;IAClB;IAEA,IAAWgB,WAAoB;QAC7B,MAAM,IAAIhB,MAAM;IAClB;IAEA,IAAWiB,WAAgC;QACzC,MAAM,IAAIjB,MAAM;IAClB;IAEA,IAAWkB,mBAA0C;QACnD,MAAM,IAAIlB,MAAM;IAClB;IAEA,IAAWmB,cAAwB;QACjC,MAAM,IAAInB,MAAM;IAClB;IAEA,IAAWoB,aAAuB;QAChC,MAAM,IAAIpB,MAAM;IAClB;IAEOqB,aAAmB;QACxB,MAAM,IAAIrB,MAAM;IAClB;AACF;AASO,MAAMjB,uBAAuBE,eAAM,CAACqC,QAAQ;IAkCjDnC,YAAYoC,MAA6B,CAAC,CAAC,CAAE;QAC3C,KAAK;aAjCAC,gBAAwB;aACxBC,WAAW;aACXC,cAAc;QAUrB;;;;GAIC,QACeC,UAAoB,EAAE;QAkBpC,IAAI,CAACC,UAAU,GAAGL,IAAIK,UAAU,IAAI;QACpC,IAAI,CAACrC,MAAM,GAAGgC,IAAIhC,MAAM,IAAI;QAC5B,IAAI,CAACF,OAAO,GAAGkC,IAAIlC,OAAO,GACtBwC,IAAAA,kCAA2B,EAACN,IAAIlC,OAAO,IACvC,IAAIyC;QAER,IAAI,CAACC,WAAW,GAAG,IAAIC,QAAc,CAACC;YACpC,IAAI,CAACC,kBAAkB,GAAGD;QAC5B;QAEA,sEAAsE;QACtE,6BAA6B;QAC7B,IAAI,CAACE,WAAW,GAAG,IAAIH,QAAiB,CAACC,SAASG;YAChD,IAAI,CAACjC,EAAE,CAAC,UAAU,IAAM8B,QAAQ;YAChC,IAAI,CAAC9B,EAAE,CAAC,OAAO,IAAM8B,QAAQ;YAC7B,IAAI,CAAC9B,EAAE,CAAC,SAAS,CAACkC,MAAQD,OAAOC;QACnC,GAAGC,IAAI,CAAC,CAACC;gBACP,0BAAA;aAAA,2BAAA,CAAA,QAAA,IAAI,EAACL,kBAAkB,qBAAvB,8BAAA;YACA,OAAOK;QACT;QAEA,IAAIhB,IAAIiB,SAAS,EAAE;YACjB,IAAI,CAACA,SAAS,GAAGjB,IAAIiB,SAAS;QAChC;IACF;IAEOC,aAAaC,IAAY,EAAEnC,KAAwB,EAAQ;QAChE,MAAMoC,SAASjC,MAAMC,OAAO,CAACJ,SAASA,QAAQ;YAACA;SAAM;QACrD,KAAK,MAAMqC,KAAKD,OAAQ;YACtB,IAAI,CAACtD,OAAO,CAACwD,MAAM,CAACH,MAAME;QAC5B;QAEA,OAAO,IAAI;IACb;IAEA;;;;GAIC,GACD,IAAWE,SAAS;QAClB,OAAO,IAAI,CAACrB,QAAQ,IAAI,IAAI,CAACC,WAAW;IAC1C;IAEA;;;;GAIC,GACD,IAAWZ,aAA4B;QACrC,OAAO,IAAI,CAACvB,MAAM;IACpB;IAEOwD,MAAMC,KAAmC,EAAE;QAChD,IAAI,IAAI,CAACR,SAAS,EAAE;YAClB,OAAO,IAAI,CAACA,SAAS,CAACQ;QACxB;QACA,IAAI,CAACrB,OAAO,CAACsB,IAAI,CAACC,OAAOC,QAAQ,CAACH,SAASA,QAAQE,OAAOE,IAAI,CAACJ;QAE/D,OAAO;IACT;IAEOK,MAAM;QACX,IAAI,CAAC5B,QAAQ,GAAG;QAChB,OAAO,KAAK,CAAC4B,OAAOC;IACtB;IAEA;;;;;;GAMC,GACD,AAAOC,kBAAkB,CAAC;IAEnBC,OACLR,KAAsB,EACtBS,SAAiB,EACjBC,QAAoB,EACpB;QACA,IAAI,CAACX,KAAK,CAACC;QAEX,sEAAsE;QACtE,wEAAwE;QACxE,mDAAmD;QACnD,EAAE;QACF,6FAA6F;QAC7FU;IACF;IAWOC,UACL/B,UAAkB,EAClBJ,aAIa,EACbnC,OAAgE,EAC1D;YAqCN,0BAAA;QApCA,IAAI,CAACA,WAAW,OAAOmC,kBAAkB,UAAU;YACjDnC,UAAUmC;QACZ,OAAO,IAAI,OAAOA,kBAAkB,YAAYA,cAAcoC,MAAM,GAAG,GAAG;YACxE,IAAI,CAACpC,aAAa,GAAGA;QACvB;QAEA,IAAInC,SAAS;YACX,qEAAqE;YACrE,mEAAmE;YACnE,2DAA2D;YAC3D,EAAE;YACF,qFAAqF;YACrF,EAAE;YACF,uEAAuE;YACvE,kCAAkC;YAClC,IAAIqB,MAAMC,OAAO,CAACtB,UAAU;gBAC1B,0EAA0E;gBAC1E,oEAAoE;gBACpE,sEAAsE;gBACtE,qDAAqD;gBACrD,IAAK,IAAIwE,IAAI,GAAGA,IAAIxE,QAAQuE,MAAM,EAAEC,KAAK,EAAG;oBAC1C,2DAA2D;oBAC3D,IAAI,CAACC,SAAS,CAACzE,OAAO,CAACwE,EAAE,EAAYxE,OAAO,CAACwE,IAAI,EAAE;gBACrD;YACF,OAAO;gBACL,KAAK,MAAM,CAACvD,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACpB,SAAU;oBAClD,wBAAwB;oBACxB,IAAI,OAAOkB,UAAU,aAAa;oBAElC,IAAI,CAACuD,SAAS,CAACxD,KAAKC;gBACtB;YACF;QACF;QAEA,IAAI,CAACqB,UAAU,GAAGA;QAClB,IAAI,CAACF,WAAW,GAAG;SACnB,2BAAA,CAAA,QAAA,IAAI,EAACQ,kBAAkB,qBAAvB,8BAAA;QAEA,OAAO,IAAI;IACb;IAEO6B,UAAUrB,IAAY,EAAW;QACtC,OAAO,IAAI,CAACrD,OAAO,CAAC2E,GAAG,CAACtB;IAC1B;IAEOuB,UAAUvB,IAAY,EAAsB;QACjD,OAAO,IAAI,CAACrD,OAAO,CAACQ,GAAG,CAAC6C,SAASzC;IACnC;IAEOiE,aAAkC;QACvC,OAAOC,IAAAA,gCAAyB,EAAC,IAAI,CAAC9E,OAAO;IAC/C;IAEO+E,iBAA2B;QAChC,OAAO1D,MAAM0C,IAAI,CAAC,IAAI,CAAC/D,OAAO,CAACgF,IAAI;IACrC;IAEOP,UAAUpB,IAAY,EAAEnC,KAAyB,EAAE;QACxD,IAAIG,MAAMC,OAAO,CAACJ,QAAQ;YACxB,qEAAqE;YACrE,uEAAuE;YACvE,IAAI,CAAClB,OAAO,CAACiF,MAAM,CAAC5B;YAEpB,KAAK,MAAME,KAAKrC,MAAO;gBACrB,IAAI,CAAClB,OAAO,CAACwD,MAAM,CAACH,MAAME;YAC5B;QACF,OAAO,IAAI,OAAOrC,UAAU,UAAU;YACpC,IAAI,CAAClB,OAAO,CAACkF,GAAG,CAAC7B,MAAMnC,MAAMiE,QAAQ;QACvC,OAAO;YACL,IAAI,CAACnF,OAAO,CAACkF,GAAG,CAAC7B,MAAMnC;QACzB;QAEA,OAAO,IAAI;IACb;IAEOkE,aAAa/B,IAAY,EAAQ;QACtC,IAAI,CAACrD,OAAO,CAACiF,MAAM,CAAC5B;IACtB;IAEOgC,eAAqB;IAC1B,uEAAuE;IACvE,cAAc;IAChB;IAEA,wEAAwE;IACxE,oBAAoB;IAEpB,IAAWC,sBAA+B;QACxC,MAAM,IAAI3E,MAAM;IAClB;IAEO4E,kBAAkB;QACvB,MAAM,IAAI5E,MAAM;IAClB;IAEA,IAAW6E,MAAuB;QAChC,MAAM,IAAI7E,MAAM;IAClB;IAEO8E,eAAe;QACpB,MAAM,IAAI9E,MAAM;IAClB;IAEO+E,eAAqB;QAC1B,MAAM,IAAI/E,MAAM;IAClB;IAEOgF,gBAAsB;QAC3B,MAAM,IAAIhF,MAAM;IAClB;IAEOiF,kBAAwB;QAC7B,MAAM,IAAIjF,MAAM;IAClB;IAEA,IAAWkF,YAAqB;QAC9B,MAAM,IAAIlF,MAAM;IAClB;IAEA,IAAWmF,kBAA2B;QACpC,MAAM,IAAInF,MAAM;IAClB;IAEA,IAAWoF,kBAA2B;QACpC,MAAM,IAAIpF,MAAM;IAClB;IAEA,IAAWqF,8BAAuC;QAChD,MAAM,IAAIrF,MAAM;IAClB;IAEA,IAAWsF,WAAoB;QAC7B,MAAM,IAAItF,MAAM;IAClB;IAEOqB,aAAmB;QACxB,MAAM,IAAIrB,MAAM;IAClB;IAEOuF,cAAoB;QACzB,MAAM,IAAIvF,MAAM;IAClB;AACF;AAWO,SAAShB,2BAA2B,EACzCI,GAAG,EACHC,UAAU,CAAC,CAAC,EACZC,SAAS,KAAK,EACdY,YAAY,EACZsC,SAAS,EACTjD,SAAS,IAAI,EACgB;IAC7B,OAAO;QACLsF,KAAK,IAAI/F,cAAc;YACrBM;YACAC;YACAC;YACAC;YACAC,UAAUU;QACZ;QACAqB,KAAK,IAAIxC,eAAe;YAAEQ;YAAQiD;QAAU;IAC9C;AACF"}