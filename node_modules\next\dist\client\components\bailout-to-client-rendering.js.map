{"version": 3, "sources": ["../../../src/client/components/bailout-to-client-rendering.ts"], "names": ["bailoutToClientRendering", "staticGenerationStore", "staticGenerationAsyncStorage", "getStore", "forceStatic", "isStaticGeneration", "throwWithNoSSR"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;4BAHe;sDACc;AAEtC,SAASA;IACd,MAAMC,wBAAwBC,kEAA4B,CAACC,QAAQ;IAEnE,IAAIF,yCAAAA,sBAAuBG,WAAW,EAAE;QACtC;IACF;IAEA,IAAIH,yCAAAA,sBAAuBI,kBAAkB,EAAE;QAC7CC,IAAAA,0BAAc;IAChB;AACF"}