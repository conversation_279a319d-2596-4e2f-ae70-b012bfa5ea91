{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-flight-client-entry-loader.ts"], "names": ["BARREL_OPTIMIZATION_PREFIX", "RSC_MODULE_TYPES", "getModuleBuildInfo", "regexCSS", "transformSource", "modules", "server", "getOptions", "isServer", "Array", "isArray", "requests", "code", "filter", "request", "test", "map", "JSON", "stringify", "startsWith", "replace", "join", "buildInfo", "_module", "rsc", "type", "client"], "mappings": "AAAA,SACEA,0BAA0B,EAC1BC,gBAAgB,QACX,gCAA+B;AACtC,SAASC,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,QAAQ,QAAQ,UAAS;AAWlC,eAAe,SAASC;IACtB,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAE,GACrB,IAAI,CAACC,UAAU;IACjB,MAAMC,WAAWF,WAAW;IAE5B,IAAI,CAACG,MAAMC,OAAO,CAACL,UAAU;QAC3BA,UAAUA,UAAU;YAACA;SAAQ,GAAG,EAAE;IACpC;IAEA,MAAMM,WAAWN;IACjB,MAAMO,OAAOD,QACX,8CAA8C;KAC7CE,MAAM,CAAC,CAACC,UAAaN,WAAW,CAACL,SAASY,IAAI,CAACD,WAAW,MAC1DE,GAAG,CACF,CAACF,UACC,CAAC,kCAAkC,EAAEG,KAAKC,SAAS,CACjDJ,QAAQK,UAAU,CAACnB,8BACfc,QAAQM,OAAO,CAAC,KAAK,SACrBN,SACJ,CAAC,CAAC,EAEPO,IAAI,CAAC;IAER,MAAMC,YAAYpB,mBAAmB,IAAI,CAACqB,OAAO;IAEjDD,UAAUE,GAAG,GAAG;QACdC,MAAMxB,iBAAiByB,MAAM;IAC/B;IAEA,OAAOd;AACT"}