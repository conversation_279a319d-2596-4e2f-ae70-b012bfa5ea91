{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.ts"], "names": ["CacheStates", "createRouterCache<PERSON>ey", "fillLazyItemsTillLeafWithHead", "newCache", "existingCache", "routerState", "cacheNodeSeedData", "head", "wasPrefetched", "isLastSegment", "Object", "keys", "length", "key", "parallelRouteState", "segmentForParallelRoute", "cache<PERSON>ey", "parallelSeedData", "undefined", "existingParallelRoutesCacheNode", "parallelRoutes", "get", "parallelRouteCacheNode", "Map", "existingCacheNode", "newCacheNode", "seedNode", "status", "READY", "data", "subTreeData", "LAZY_INITIALIZED", "set", "existingParallelRoutes"], "mappings": "AAAA,SAASA,WAAW,QAAQ,wDAAuD;AAMnF,SAASC,oBAAoB,QAAQ,4BAA2B;AAEhE,OAAO,SAASC,8BACdC,QAAmB,EACnBC,aAAoC,EACpCC,WAA8B,EAC9BC,iBAA2C,EAC3CC,IAAqB,EACrBC,aAAuB;IAEvB,MAAMC,gBAAgBC,OAAOC,IAAI,CAACN,WAAW,CAAC,EAAE,EAAEO,MAAM,KAAK;IAC7D,IAAIH,eAAe;QACjBN,SAASI,IAAI,GAAGA;QAChB;IACF;IACA,+FAA+F;IAC/F,IAAK,MAAMM,OAAOR,WAAW,CAAC,EAAE,CAAE;QAChC,MAAMS,qBAAqBT,WAAW,CAAC,EAAE,CAACQ,IAAI;QAC9C,MAAME,0BAA0BD,kBAAkB,CAAC,EAAE;QACrD,MAAME,WAAWf,qBAAqBc;QAEtC,4EAA4E;QAC5E,2EAA2E;QAC3E,wEAAwE;QACxE,wEAAwE;QACxE,0EAA0E;QAC1E,qBAAqB;QACrB,EAAE;QACF,0EAA0E;QAC1E,wEAAwE;QACxE,kEAAkE;QAClE,MAAME,mBACJX,sBAAsB,QACtBA,iBAAiB,CAAC,EAAE,KAAK,QACzBA,iBAAiB,CAAC,EAAE,CAACO,IAAI,KAAKK,YAC1BZ,iBAAiB,CAAC,EAAE,CAACO,IAAI,GACzB;QACN,IAAIT,eAAe;YACjB,MAAMe,kCACJf,cAAcgB,cAAc,CAACC,GAAG,CAACR;YACnC,IAAIM,iCAAiC;gBACnC,IAAIG,yBAAyB,IAAIC,IAAIJ;gBACrC,MAAMK,oBAAoBF,uBAAuBD,GAAG,CAACL;gBACrD,IAAIS;gBACJ,IAAIR,qBAAqB,MAAM;oBAC7B,qCAAqC;oBACrC,MAAMS,WAAWT,gBAAgB,CAAC,EAAE;oBACpCQ,eAAe;wBACbE,QAAQ3B,YAAY4B,KAAK;wBACzBC,MAAM;wBACNC,aAAaJ;wBACbN,gBAAgB,IAAIG,IAAIC,qCAAAA,kBAAmBJ,cAAc;oBAC3D;gBACF,OAAO,IAAIZ,iBAAiBgB,mBAAmB;oBAC7C,oEAAoE;oBACpE,2CAA2C;oBAC3CC,eAAe;wBACbE,QAAQH,kBAAkBG,MAAM;wBAChCE,MAAML,kBAAkBK,IAAI;wBAC5BC,aAAaN,kBAAkBM,WAAW;wBAC1CV,gBAAgB,IAAIG,IAAIC,kBAAkBJ,cAAc;oBAC1D;gBACF,OAAO;oBACL,kEAAkE;oBAClE,iBAAiB;oBACjBK,eAAe;wBACbE,QAAQ3B,YAAY+B,gBAAgB;wBACpCF,MAAM;wBACNC,aAAa;wBACbV,gBAAgB,IAAIG,IAAIC,qCAAAA,kBAAmBJ,cAAc;oBAC3D;gBACF;gBAEA,mDAAmD;gBACnDE,uBAAuBU,GAAG,CAAChB,UAAUS;gBACrC,qEAAqE;gBACrEvB,8BACEuB,cACAD,mBACAV,oBACAG,mBAAmBA,mBAAmB,MACtCV,MACAC;gBAGFL,SAASiB,cAAc,CAACY,GAAG,CAACnB,KAAKS;gBACjC;YACF;QACF;QAEA,IAAIG;QACJ,IAAIR,qBAAqB,MAAM;YAC7B,qCAAqC;YACrC,MAAMS,WAAWT,gBAAgB,CAAC,EAAE;YACpCQ,eAAe;gBACbE,QAAQ3B,YAAY4B,KAAK;gBACzBC,MAAM;gBACNC,aAAaJ;gBACbN,gBAAgB,IAAIG;YACtB;QACF,OAAO;YACL,kEAAkE;YAClE,iBAAiB;YACjBE,eAAe;gBACbE,QAAQ3B,YAAY+B,gBAAgB;gBACpCF,MAAM;gBACNC,aAAa;gBACbV,gBAAgB,IAAIG;YACtB;QACF;QAEA,MAAMU,yBAAyB9B,SAASiB,cAAc,CAACC,GAAG,CAACR;QAC3D,IAAIoB,wBAAwB;YAC1BA,uBAAuBD,GAAG,CAAChB,UAAUS;QACvC,OAAO;YACLtB,SAASiB,cAAc,CAACY,GAAG,CAACnB,KAAK,IAAIU,IAAI;gBAAC;oBAACP;oBAAUS;iBAAa;aAAC;QACrE;QAEAvB,8BACEuB,cACAP,WACAJ,oBACAG,kBACAV,MACAC;IAEJ;AACF"}