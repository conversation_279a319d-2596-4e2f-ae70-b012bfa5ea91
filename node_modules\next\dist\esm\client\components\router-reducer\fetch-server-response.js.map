{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fetch-server-response.ts"], "names": ["createFromFetch", "process", "env", "NEXT_RUNTIME", "require", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE", "NEXT_RSC_UNION_QUERY", "NEXT_URL", "RSC_HEADER", "RSC_CONTENT_TYPE_HEADER", "NEXT_DID_POSTPONE_HEADER", "urlToUrlWithoutFlightMarker", "callServer", "PrefetchKind", "hexHash", "doMpaNavigation", "url", "toString", "undefined", "fetchServerResponse", "flightRouterState", "nextUrl", "currentBuildId", "prefetchKind", "headers", "encodeURIComponent", "JSON", "stringify", "AUTO", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "fetchUrl", "URL", "NODE_ENV", "__NEXT_CONFIG_OUTPUT", "pathname", "endsWith", "searchParams", "set", "res", "fetch", "credentials", "responseUrl", "canonicalUrl", "redirected", "contentType", "get", "postponed", "isFlightResponse", "startsWith", "ok", "hash", "buildId", "flightData", "Promise", "resolve", "err", "console", "error"], "mappings": "AAAA;AAEA,aAAa;AACb,6DAA6D;AAC7D,oEAAoE;AACpE,MAAM,EAAEA,eAAe,EAAE,GACvB,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AAQd,SACEC,2BAA2B,EAC3BC,sBAAsB,EACtBC,oBAAoB,EACpBC,QAAQ,EACRC,UAAU,EACVC,uBAAuB,EACvBC,wBAAwB,QACnB,wBAAuB;AAC9B,SAASC,2BAA2B,QAAQ,gBAAe;AAC3D,SAASC,UAAU,QAAQ,wBAAuB;AAClD,SAASC,YAAY,QAAQ,yBAAwB;AACrD,SAASC,OAAO,QAAQ,2BAA0B;AAQlD,SAASC,gBAAgBC,GAAW;IAClC,OAAO;QAACL,4BAA4BK,KAAKC,QAAQ;QAAIC;KAAU;AACjE;AAEA;;CAEC,GACD,OAAO,eAAeC,oBACpBH,GAAQ,EACRI,iBAAoC,EACpCC,OAAsB,EACtBC,cAAsB,EACtBC,YAA2B;IAE3B,MAAMC,UAKF;QACF,yBAAyB;QACzB,CAAChB,WAAW,EAAE;QACd,mCAAmC;QACnC,CAACH,uBAAuB,EAAEoB,mBACxBC,KAAKC,SAAS,CAACP;IAEnB;IAEA;;;;;GAKC,GACD,IAAIG,iBAAiBV,aAAae,IAAI,EAAE;QACtCJ,OAAO,CAACpB,4BAA4B,GAAG;IACzC;IAEA,IAAIiB,SAAS;QACXG,OAAO,CAACjB,SAAS,GAAGc;IACtB;IAEA,MAAMQ,mBAAmBf,QACvB;QACEU,OAAO,CAACpB,4BAA4B,IAAI;QACxCoB,OAAO,CAACnB,uBAAuB;QAC/BmB,OAAO,CAACjB,SAAS;KAClB,CAACuB,IAAI,CAAC;IAGT,IAAI;QACF,IAAIC,WAAW,IAAIC,IAAIhB;QACvB,IAAIhB,QAAQC,GAAG,CAACgC,QAAQ,KAAK,cAAc;YACzC,IAAIjC,QAAQC,GAAG,CAACiC,oBAAoB,KAAK,UAAU;gBACjD,IAAIH,SAASI,QAAQ,CAACC,QAAQ,CAAC,MAAM;oBACnCL,SAASI,QAAQ,IAAI;gBACvB,OAAO;oBACLJ,SAASI,QAAQ,IAAI;gBACvB;YACF;QACF;QAEA,8FAA8F;QAC9FJ,SAASM,YAAY,CAACC,GAAG,CAAChC,sBAAsBuB;QAEhD,MAAMU,MAAM,MAAMC,MAAMT,UAAU;YAChC,wFAAwF;YACxFU,aAAa;YACbjB;QACF;QAEA,MAAMkB,cAAc/B,4BAA4B4B,IAAIvB,GAAG;QACvD,MAAM2B,eAAeJ,IAAIK,UAAU,GAAGF,cAAcxB;QAEpD,MAAM2B,cAAcN,IAAIf,OAAO,CAACsB,GAAG,CAAC,mBAAmB;QACvD,MAAMC,YAAY,CAAC,CAACR,IAAIf,OAAO,CAACsB,GAAG,CAACpC;QACpC,IAAIsC,mBAAmBH,gBAAgBpC;QAEvC,IAAIT,QAAQC,GAAG,CAACgC,QAAQ,KAAK,cAAc;YACzC,IAAIjC,QAAQC,GAAG,CAACiC,oBAAoB,KAAK,UAAU;gBACjD,IAAI,CAACc,kBAAkB;oBACrBA,mBAAmBH,YAAYI,UAAU,CAAC;gBAC5C;YACF;QACF;QAEA,4FAA4F;QAC5F,oEAAoE;QACpE,IAAI,CAACD,oBAAoB,CAACT,IAAIW,EAAE,EAAE;YAChC,2FAA2F;YAC3F,IAAIlC,IAAImC,IAAI,EAAE;gBACZT,YAAYS,IAAI,GAAGnC,IAAImC,IAAI;YAC7B;YAEA,OAAOpC,gBAAgB2B,YAAYzB,QAAQ;QAC7C;QAEA,2EAA2E;QAC3E,MAAM,CAACmC,SAASC,WAAW,GAAuB,MAAMtD,gBACtDuD,QAAQC,OAAO,CAAChB,MAChB;YACE3B;QACF;QAGF,IAAIU,mBAAmB8B,SAAS;YAC9B,OAAOrC,gBAAgBwB,IAAIvB,GAAG;QAChC;QAEA,OAAO;YAACqC;YAAYV;YAAcI;SAAU;IAC9C,EAAE,OAAOS,KAAK;QACZC,QAAQC,KAAK,CACX,AAAC,qCAAkC1C,MAAI,yCACvCwC;QAEF,iDAAiD;QACjD,qHAAqH;QACrH,iGAAiG;QACjG,OAAO;YAACxC,IAAIC,QAAQ;YAAIC;SAAU;IACpC;AACF"}