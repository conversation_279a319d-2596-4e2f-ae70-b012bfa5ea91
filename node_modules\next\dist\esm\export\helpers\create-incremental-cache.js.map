{"version": 3, "sources": ["../../../src/export/helpers/create-incremental-cache.ts"], "names": ["path", "IncrementalCache", "hasNextSupport", "nodeFs", "createIncrementalCache", "incremental<PERSON>ache<PERSON>andlerPath", "isrMemoryCacheSize", "fetchCacheKeyPrefix", "distDir", "dir", "enabledDirectories", "experimental", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "isAbsolute", "join", "default", "incrementalCache", "dev", "requestHeaders", "fetchCache", "maxMemoryCacheSize", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "fs", "pagesDir", "pages", "appDir", "app", "serverDistDir", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minimalMode", "globalThis", "__incrementalCache"], "mappings": "AAEA,OAAOA,UAAU,OAAM;AACvB,SAASC,gBAAgB,QAAQ,qCAAoC;AACrE,SAASC,cAAc,QAAQ,0BAAyB;AACxD,SAASC,MAAM,QAAQ,mCAAkC;AAEzD,OAAO,SAASC,uBAAuB,EACrCC,2BAA2B,EAC3BC,kBAAkB,EAClBC,mBAAmB,EACnBC,OAAO,EACPC,GAAG,EACHC,kBAAkB,EAClBC,YAAY,EACZC,WAAW,EAUZ;IACC,kCAAkC;IAClC,IAAIC;IACJ,IAAIR,6BAA6B;QAC/BQ,eAAeC,QAAQd,KAAKe,UAAU,CAACV,+BACnCA,8BACAL,KAAKgB,IAAI,CAACP,KAAKJ;QACnBQ,eAAeA,aAAaI,OAAO,IAAIJ;IACzC;IAEA,MAAMK,mBAAmB,IAAIjB,iBAAiB;QAC5CkB,KAAK;QACLC,gBAAgB,CAAC;QACjBR;QACAS,YAAY;QACZC,oBAAoBhB;QACpBC;QACAgB,sBAAsB,IAAO,CAAA;gBAC3BC,SAAS;gBACTC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,SAAS;oBACPC,0BAA0B;oBAC1BC,eAAe;oBACfC,uBAAuB;gBACzB;gBACAC,gBAAgB,EAAE;YACpB,CAAA;QACAC,IAAI7B;QACJ8B,UAAUvB,mBAAmBwB,KAAK;QAClCC,QAAQzB,mBAAmB0B,GAAG;QAC9BC,eAAerC,KAAKgB,IAAI,CAACR,SAAS;QAClC8B,iBAAiBzB;QACjB0B,aAAarC;QACbS;IACF;IAEE6B,WAAmBC,kBAAkB,GAAGvB;IAE1C,OAAOA;AACT"}