{"version": 3, "sources": ["../../../../src/client/components/router-reducer/create-initial-router-state.ts"], "names": ["CacheStates", "createHrefFromUrl", "fillLazyItemsTillLeafWithHead", "extractPathFromFlightRouterState", "createInitialRouterState", "buildId", "initialTree", "initialSeedData", "initialCanonicalUrl", "initialParallelRoutes", "isServer", "location", "initialHead", "subTreeData", "cache", "status", "READY", "data", "parallelRoutes", "Map", "size", "undefined", "tree", "prefetchCache", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "onlyHashChange", "hashFragment", "segmentPaths", "canonicalUrl", "nextUrl", "pathname"], "mappings": "AAOA,SAASA,WAAW,QAAQ,wDAAuD;AACnF,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,6BAA6B,QAAQ,wCAAuC;AACrF,SAASC,gCAAgC,QAAQ,yBAAwB;AAazE,OAAO,SAASC,yBAAyB,KASV;IATU,IAAA,EACvCC,OAAO,EACPC,WAAW,EACXC,eAAe,EACfC,mBAAmB,EACnBC,qBAAqB,EACrBC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACkB,GATU;IAUvC,MAAMC,cAAcN,eAAe,CAAC,EAAE;IAEtC,MAAMO,QAAmB;QACvBC,QAAQf,YAAYgB,KAAK;QACzBC,MAAM;QACNJ,aAAaA;QACb,oJAAoJ;QACpJK,gBAAgBR,WAAW,IAAIS,QAAQV;IACzC;IAEA,yEAAyE;IACzE,IAAIA,0BAA0B,QAAQA,sBAAsBW,IAAI,KAAK,GAAG;QACtElB,8BACEY,OACAO,WACAf,aACAC,iBACAK;IAEJ;QA4BI,sEAAsE;IACrET;IA3BL,OAAO;QACLE;QACAiB,MAAMhB;QACNQ;QACAS,eAAe,IAAIJ;QACnBK,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,mEAAmE;YACnE,gFAAgF;YAChFC,4BAA4B;QAC9B;QACAC,mBAAmB;YACjBC,OAAO;YACPC,gBAAgB;YAChBC,cAAc;YACdC,cAAc,EAAE;QAClB;QACAC,cACE,6EAA6E;QAC7E,kJAAkJ;QAClJtB,WAEIV,kBAAkBU,YAClBH;QACN0B,SAEE,CAAC/B,OAAAA,iCAAiCG,iBAAgBK,4BAAAA,SAAUwB,QAAQ,aAAnEhC,OACD;IACJ;AACF"}