{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/fetch-cache.ts"], "names": ["L<PERSON><PERSON><PERSON>", "CACHE_ONE_YEAR", "NEXT_CACHE_SOFT_TAGS_HEADER", "rateLimitedUntil", "memoryCache", "CACHE_TAGS_HEADER", "CACHE_HEADERS_HEADER", "CACHE_STATE_HEADER", "CACHE_REVALIDATE_HEADER", "CACHE_FETCH_URL_HEADER", "CACHE_CONTROL_VALUE_HEADER", "<PERSON><PERSON><PERSON><PERSON>", "isAvailable", "ctx", "_requestHeaders", "process", "env", "SUSPENSE_CACHE_URL", "constructor", "debug", "NEXT_PRIVATE_DEBUG_CACHE", "headers", "newHeaders", "JSON", "parse", "k", "scHost", "sc<PERSON><PERSON><PERSON><PERSON>", "SUSPENSE_CACHE_BASEPATH", "SUSPENSE_CACHE_AUTH_TOKEN", "cacheEndpoint", "console", "log", "maxMemoryCacheSize", "max", "length", "value", "kind", "stringify", "props", "Error", "data", "body", "html", "pageData", "revalidateTag", "tag", "Date", "now", "res", "fetch", "method", "next", "internal", "status", "retryAfter", "get", "parseInt", "ok", "err", "warn", "args", "key", "tags", "softTags", "kindHint", "fetchIdx", "fetchUrl", "lastModified", "undefined", "start", "fetchParams", "fetchType", "join", "error", "text", "cached", "json", "cacheState", "age", "Object", "keys", "set", "fetchCache", "revalidate", "toString"], "mappings": "AAEA,OAAOA,cAAc,+BAA8B;AACnD,SACEC,cAAc,EACdC,2BAA2B,QACtB,yBAAwB;AAE/B,IAAIC,mBAAmB;AACvB,IAAIC;AASJ,MAAMC,oBAAoB;AAC1B,MAAMC,uBAAuB;AAC7B,MAAMC,qBAAqB;AAC3B,MAAMC,0BAA0B;AAChC,MAAMC,yBAAyB;AAC/B,MAAMC,6BAA6B;AAEnC,eAAe,MAAMC;IAKnB,OAAOC,YAAYC,GAElB,EAAE;QACD,OAAO,CAAC,CACNA,CAAAA,IAAIC,eAAe,CAAC,mBAAmB,IAAIC,QAAQC,GAAG,CAACC,kBAAkB,AAAD;IAE5E;IAEAC,YAAYL,GAAwB,CAAE;QACpC,IAAI,CAACM,KAAK,GAAG,CAAC,CAACJ,QAAQC,GAAG,CAACI,wBAAwB;QACnD,IAAI,CAACC,OAAO,GAAG,CAAC;QAChB,IAAI,CAACA,OAAO,CAAC,eAAe,GAAG;QAE/B,IAAIf,wBAAwBO,IAAIC,eAAe,EAAE;YAC/C,MAAMQ,aAAaC,KAAKC,KAAK,CAC3BX,IAAIC,eAAe,CAACR,qBAAqB;YAE3C,IAAK,MAAMmB,KAAKH,WAAY;gBAC1B,IAAI,CAACD,OAAO,CAACI,EAAE,GAAGH,UAAU,CAACG,EAAE;YACjC;YACA,OAAOZ,IAAIC,eAAe,CAACR,qBAAqB;QAClD;QACA,MAAMoB,SACJb,IAAIC,eAAe,CAAC,mBAAmB,IAAIC,QAAQC,GAAG,CAACC,kBAAkB;QAE3E,MAAMU,aACJd,IAAIC,eAAe,CAAC,uBAAuB,IAC3CC,QAAQC,GAAG,CAACY,uBAAuB;QAErC,IAAIb,QAAQC,GAAG,CAACa,yBAAyB,EAAE;YACzC,IAAI,CAACR,OAAO,CACV,gBACD,GAAG,CAAC,OAAO,EAAEN,QAAQC,GAAG,CAACa,yBAAyB,CAAC,CAAC;QACvD;QAEA,IAAIH,QAAQ;YACV,IAAI,CAACI,aAAa,GAAG,CAAC,QAAQ,EAAEJ,OAAO,EAAEC,cAAc,GAAG,CAAC;YAC3D,IAAI,IAAI,CAACR,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC,wBAAwB,IAAI,CAACF,aAAa;YACxD;QACF,OAAO,IAAI,IAAI,CAACX,KAAK,EAAE;YACrBY,QAAQC,GAAG,CAAC;QACd;QAEA,IAAInB,IAAIoB,kBAAkB,EAAE;YAC1B,IAAI,CAAC7B,aAAa;gBAChB,IAAI,IAAI,CAACe,KAAK,EAAE;oBACdY,QAAQC,GAAG,CAAC;gBACd;gBAEA5B,cAAc,IAAIJ,SAAS;oBACzBkC,KAAKrB,IAAIoB,kBAAkB;oBAC3BE,QAAO,EAAEC,KAAK,EAAE;4BAcSb;wBAbvB,IAAI,CAACa,OAAO;4BACV,OAAO;wBACT,OAAO,IAAIA,MAAMC,IAAI,KAAK,YAAY;4BACpC,OAAOd,KAAKe,SAAS,CAACF,MAAMG,KAAK,EAAEJ,MAAM;wBAC3C,OAAO,IAAIC,MAAMC,IAAI,KAAK,SAAS;4BACjC,MAAM,IAAIG,MAAM;wBAClB,OAAO,IAAIJ,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOd,KAAKe,SAAS,CAACF,MAAMK,IAAI,IAAI,IAAIN,MAAM;wBAChD,OAAO,IAAIC,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOD,MAAMM,IAAI,CAACP,MAAM;wBAC1B;wBACA,wCAAwC;wBACxC,OACEC,MAAMO,IAAI,CAACR,MAAM,GAAIZ,CAAAA,EAAAA,kBAAAA,KAAKe,SAAS,CAACF,MAAMQ,QAAQ,sBAA7BrB,gBAAgCY,MAAM,KAAI,CAAA;oBAEnE;gBACF;YACF;QACF,OAAO;YACL,IAAI,IAAI,CAAChB,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC;YACd;QACF;IACF;IAEA,MAAaa,cAAcC,GAAW,EAAE;QACtC,IAAI,IAAI,CAAC3B,KAAK,EAAE;YACdY,QAAQC,GAAG,CAAC,iBAAiBc;QAC/B;QAEA,IAAIC,KAAKC,GAAG,KAAK7C,kBAAkB;YACjC,IAAI,IAAI,CAACgB,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC,iBAAiB7B;YAC/B;YACA;QACF;QAEA,IAAI;YACF,MAAM8C,MAAM,MAAMC,MAChB,CAAC,EAAE,IAAI,CAACpB,aAAa,CAAC,mCAAmC,EAAEgB,IAAI,CAAC,EAChE;gBACEK,QAAQ;gBACR9B,SAAS,IAAI,CAACA,OAAO;gBACrB,sCAAsC;gBACtC+B,MAAM;oBAAEC,UAAU;gBAAK;YACzB;YAGF,IAAIJ,IAAIK,MAAM,KAAK,KAAK;gBACtB,MAAMC,aAAaN,IAAI5B,OAAO,CAACmC,GAAG,CAAC,kBAAkB;gBACrDrD,mBAAmB4C,KAAKC,GAAG,KAAKS,SAASF;YAC3C;YAEA,IAAI,CAACN,IAAIS,EAAE,EAAE;gBACX,MAAM,IAAIlB,MAAM,CAAC,2BAA2B,EAAES,IAAIK,MAAM,CAAC,CAAC,CAAC;YAC7D;QACF,EAAE,OAAOK,KAAK;YACZ5B,QAAQ6B,IAAI,CAAC,CAAC,yBAAyB,EAAEd,IAAI,CAAC,EAAEa;QAClD;IACF;IAEA,MAAaH,IAAI,GAAGK,IAAqC,EAAE;QACzD,MAAM,CAACC,KAAKjD,MAAM,CAAC,CAAC,CAAC,GAAGgD;QACxB,MAAM,EAAEE,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE,GAAGtD;QAEzD,IAAIoD,aAAa,SAAS;YACxB,OAAO;QACT;QAEA,IAAIlB,KAAKC,GAAG,KAAK7C,kBAAkB;YACjC,IAAI,IAAI,CAACgB,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC;YACd;YACA,OAAO;QACT;QAEA,IAAIS,OAAOrC,+BAAAA,YAAaoD,GAAG,CAACM;QAE5B,0DAA0D;QAC1D,wDAAwD;QACxD,IAAIf,KAAKC,GAAG,KAAMP,CAAAA,CAAAA,wBAAAA,KAAM2B,YAAY,KAAI,CAAA,IAAK,MAAM;YACjD3B,OAAO4B;QACT;QAEA,4BAA4B;QAC5B,IAAI,CAAC5B,QAAQ,IAAI,CAACX,aAAa,EAAE;YAC/B,IAAI;gBACF,MAAMwC,QAAQvB,KAAKC,GAAG;gBACtB,MAAMuB,cAAoC;oBACxClB,UAAU;oBACVmB,WAAW;oBACXL,UAAUA;oBACVD;gBACF;gBACA,MAAMjB,MAAM,MAAMC,MAChB,CAAC,EAAE,IAAI,CAACpB,aAAa,CAAC,mBAAmB,EAAEgC,IAAI,CAAC,EAChD;oBACEX,QAAQ;oBACR9B,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACZ,uBAAuB,EAAE0D;wBAC1B,CAAC9D,kBAAkB,EAAE0D,CAAAA,wBAAAA,KAAMU,IAAI,CAAC,SAAQ;wBACxC,CAACvE,4BAA4B,EAAE8D,CAAAA,4BAAAA,SAAUS,IAAI,CAAC,SAAQ;oBACxD;oBACArB,MAAMmB;gBACR;gBAGF,IAAItB,IAAIK,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaN,IAAI5B,OAAO,CAACmC,GAAG,CAAC,kBAAkB;oBACrDrD,mBAAmB4C,KAAKC,GAAG,KAAKS,SAASF;gBAC3C;gBAEA,IAAIN,IAAIK,MAAM,KAAK,KAAK;oBACtB,IAAI,IAAI,CAACnC,KAAK,EAAE;wBACdY,QAAQC,GAAG,CACT,CAAC,yBAAyB,EAAE8B,IAAI,YAAY,EAC1Cf,KAAKC,GAAG,KAAKsB,MACd,EAAE,CAAC;oBAER;oBACA,OAAO;gBACT;gBAEA,IAAI,CAACrB,IAAIS,EAAE,EAAE;oBACX3B,QAAQ2C,KAAK,CAAC,MAAMzB,IAAI0B,IAAI;oBAC5B,MAAM,IAAInC,MAAM,CAAC,4BAA4B,EAAES,IAAIK,MAAM,CAAC,CAAC;gBAC7D;gBAEA,MAAMsB,SAAS,MAAM3B,IAAI4B,IAAI;gBAE7B,IAAI,CAACD,UAAUA,OAAOvC,IAAI,KAAK,SAAS;oBACtC,IAAI,CAAClB,KAAK,IAAIY,QAAQC,GAAG,CAAC;wBAAE4C;oBAAO;oBACnC,MAAM,IAAIpC,MAAM,CAAC,mBAAmB,CAAC;gBACvC;gBAEA,MAAMsC,aAAa7B,IAAI5B,OAAO,CAACmC,GAAG,CAACjD;gBACnC,MAAMwE,MAAM9B,IAAI5B,OAAO,CAACmC,GAAG,CAAC;gBAE5Bf,OAAO;oBACLL,OAAOwC;oBACP,qDAAqD;oBACrD,uCAAuC;oBACvCR,cACEU,eAAe,UACX/B,KAAKC,GAAG,KAAK/C,iBACb8C,KAAKC,GAAG,KAAKS,SAASsB,OAAO,KAAK,MAAM;gBAChD;gBAEA,IAAI,IAAI,CAAC5D,KAAK,EAAE;oBACdY,QAAQC,GAAG,CACT,CAAC,0BAA0B,EAAE8B,IAAI,YAAY,EAC3Cf,KAAKC,GAAG,KAAKsB,MACd,UAAU,EACTU,OAAOC,IAAI,CAACL,QAAQzC,MAAM,CAC3B,eAAe,EAAE2C,WAAW,OAAO,EAAEf,wBAAAA,KAAMU,IAAI,CAC9C,KACA,WAAW,EAAET,4BAAAA,SAAUS,IAAI,CAAC,KAAK,CAAC;gBAExC;gBAEA,IAAIhC,MAAM;oBACRrC,+BAAAA,YAAa8E,GAAG,CAACpB,KAAKrB;gBACxB;YACF,EAAE,OAAOkB,KAAK;gBACZ,sCAAsC;gBACtC,IAAI,IAAI,CAACxC,KAAK,EAAE;oBACdY,QAAQ2C,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAEf;gBAClD;YACF;QACF;QAEA,OAAOlB,QAAQ;IACjB;IAEA,MAAayC,IAAI,GAAGrB,IAAqC,EAAE;QACzD,MAAM,CAACC,KAAKrB,MAAM5B,IAAI,GAAGgD;QACzB,MAAM,EAAEsB,UAAU,EAAEjB,QAAQ,EAAEC,QAAQ,EAAEJ,IAAI,EAAE,GAAGlD;QACjD,IAAI,CAACsE,YAAY;QAEjB,IAAIpC,KAAKC,GAAG,KAAK7C,kBAAkB;YACjC,IAAI,IAAI,CAACgB,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC;YACd;YACA;QACF;QAEA5B,+BAAAA,YAAa8E,GAAG,CAACpB,KAAK;YACpB1B,OAAOK;YACP2B,cAAcrB,KAAKC,GAAG;QACxB;QAEA,IAAI,IAAI,CAAClB,aAAa,EAAE;YACtB,IAAI;gBACF,MAAMwC,QAAQvB,KAAKC,GAAG;gBACtB,IAAIP,SAAS,QAAQ,gBAAgBA,MAAM;oBACzC,IAAI,CAACpB,OAAO,CAACb,wBAAwB,GAAGiC,KAAK2C,UAAU,CAACC,QAAQ;gBAClE;gBACA,IACE,CAAC,IAAI,CAAChE,OAAO,CAACb,wBAAwB,IACtCiC,SAAS,QACT,UAAUA,MACV;oBACA,IAAI,CAACpB,OAAO,CAACX,2BAA2B,GACtC+B,KAAKA,IAAI,CAACpB,OAAO,CAAC,gBAAgB;gBACtC;gBACA,MAAMqB,OAAOnB,KAAKe,SAAS,CAAC;oBAC1B,GAAGG,IAAI;oBACP,yCAAyC;oBACzC,sBAAsB;oBACtBsB,MAAMM;gBACR;gBAEA,IAAI,IAAI,CAAClD,KAAK,EAAE;oBACdY,QAAQC,GAAG,CAAC,aAAa8B;gBAC3B;gBACA,MAAMS,cAAoC;oBACxClB,UAAU;oBACVmB,WAAW;oBACXL;oBACAD;gBACF;gBACA,MAAMjB,MAAM,MAAMC,MAChB,CAAC,EAAE,IAAI,CAACpB,aAAa,CAAC,mBAAmB,EAAEgC,IAAI,CAAC,EAChD;oBACEX,QAAQ;oBACR9B,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACZ,uBAAuB,EAAE0D,YAAY;wBACtC,CAAC9D,kBAAkB,EAAE0D,CAAAA,wBAAAA,KAAMU,IAAI,CAAC,SAAQ;oBAC1C;oBACA/B,MAAMA;oBACNU,MAAMmB;gBACR;gBAGF,IAAItB,IAAIK,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaN,IAAI5B,OAAO,CAACmC,GAAG,CAAC,kBAAkB;oBACrDrD,mBAAmB4C,KAAKC,GAAG,KAAKS,SAASF;gBAC3C;gBAEA,IAAI,CAACN,IAAIS,EAAE,EAAE;oBACX,IAAI,CAACvC,KAAK,IAAIY,QAAQC,GAAG,CAAC,MAAMiB,IAAI0B,IAAI;oBACxC,MAAM,IAAInC,MAAM,CAAC,iBAAiB,EAAES,IAAIK,MAAM,CAAC,CAAC;gBAClD;gBAEA,IAAI,IAAI,CAACnC,KAAK,EAAE;oBACdY,QAAQC,GAAG,CACT,CAAC,oCAAoC,EAAE8B,IAAI,YAAY,EACrDf,KAAKC,GAAG,KAAKsB,MACd,UAAU,EAAE5B,KAAKP,MAAM,CAAC,CAAC;gBAE9B;YACF,EAAE,OAAOwB,KAAK;gBACZ,+BAA+B;gBAC/B,IAAI,IAAI,CAACxC,KAAK,EAAE;oBACdY,QAAQ2C,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAEf;gBAChD;YACF;QACF;QACA;IACF;AACF"}