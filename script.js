// DOM Elements
const loginForm = document.getElementById('login-form');
const usernameInput = document.getElementById('username');
const passwordInput = document.getElementById('password');
const rememberMeCheckbox = document.getElementById('remember-me');
const loginButton = document.getElementById('login-button');
const buttonText = document.getElementById('button-text');
const loadingSpinner = document.getElementById('loading-spinner');
const errorMessage = document.getElementById('error-message');
const errorText = document.getElementById('error-text');
const togglePasswordButton = document.getElementById('toggle-password');
const eyeOpenIcon = document.getElementById('eye-open');
const eyeClosedIcon = document.getElementById('eye-closed');

// Demo credentials
const DEMO_CREDENTIALS = {
    username: 'admin',
    password: '123456'
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Load saved credentials if "Remember Me" was checked
    loadSavedCredentials();
    
    // Add event listeners
    addEventListeners();
    
    // Add input animations
    addInputAnimations();
}

function addEventListeners() {
    // Form submission
    loginForm.addEventListener('submit', handleLogin);
    
    // Password toggle
    togglePasswordButton.addEventListener('click', togglePasswordVisibility);
    
    // Input validation on blur
    usernameInput.addEventListener('blur', validateUsername);
    passwordInput.addEventListener('blur', validatePassword);
    
    // Clear error on input
    usernameInput.addEventListener('input', clearError);
    passwordInput.addEventListener('input', clearError);
    
    // Enter key handling
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !loginButton.disabled) {
            handleLogin(e);
        }
    });
}

function addInputAnimations() {
    const inputs = [usernameInput, passwordInput];
    
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
}

async function handleLogin(e) {
    e.preventDefault();
    
    const username = usernameInput.value.trim();
    const password = passwordInput.value.trim();
    const rememberMe = rememberMeCheckbox.checked;
    
    // Clear any existing errors
    clearError();
    
    // Validate inputs
    if (!validateInputs(username, password)) {
        return;
    }
    
    // Show loading state
    setLoadingState(true);
    
    try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Check credentials
        if (username === DEMO_CREDENTIALS.username && password === DEMO_CREDENTIALS.password) {
            // Success
            handleLoginSuccess(username, rememberMe);
        } else {
            // Failed
            handleLoginError('اسم المستخدم أو كلمة المرور غير صحيحة');
        }
    } catch (error) {
        handleLoginError('حدث خطأ في الاتصال، يرجى المحاولة مرة أخرى');
    } finally {
        setLoadingState(false);
    }
}

function validateInputs(username, password) {
    if (!username) {
        showError('يرجى إدخال اسم المستخدم');
        usernameInput.focus();
        return false;
    }
    
    if (username.length < 3) {
        showError('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
        usernameInput.focus();
        return false;
    }
    
    if (!password) {
        showError('يرجى إدخال كلمة المرور');
        passwordInput.focus();
        return false;
    }
    
    if (password.length < 6) {
        showError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        passwordInput.focus();
        return false;
    }
    
    return true;
}

function validateUsername() {
    const username = usernameInput.value.trim();
    if (username && username.length < 3) {
        usernameInput.style.boxShadow = 'inset 8px 8px 16px #ff9999, inset -8px -8px 16px #ffffff';
    } else {
        usernameInput.style.boxShadow = '';
    }
}

function validatePassword() {
    const password = passwordInput.value.trim();
    if (password && password.length < 6) {
        passwordInput.style.boxShadow = 'inset 8px 8px 16px #ff9999, inset -8px -8px 16px #ffffff';
    } else {
        passwordInput.style.boxShadow = '';
    }
}

function handleLoginSuccess(username, rememberMe) {
    // Save credentials if remember me is checked
    if (rememberMe) {
        localStorage.setItem('rememberedUsername', username);
        localStorage.setItem('rememberMe', 'true');
    } else {
        localStorage.removeItem('rememberedUsername');
        localStorage.removeItem('rememberMe');
    }
    
    // Save login state
    localStorage.setItem('isLoggedIn', 'true');

    // Show success message
    showSuccessMessage('تم تسجيل الدخول بنجاح!');

    // Redirect to dashboard
    setTimeout(() => {
        window.location.href = 'dashboard.html';
    }, 1500);
}

function handleLoginError(message) {
    showError(message);
    
    // Add shake animation to the form
    loginForm.style.animation = 'shake 0.5s ease-in-out';
    setTimeout(() => {
        loginForm.style.animation = '';
    }, 500);
}

function showError(message) {
    errorText.textContent = message;
    errorMessage.classList.remove('hidden');
    errorMessage.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

function clearError() {
    errorMessage.classList.add('hidden');
    
    // Reset input styles
    usernameInput.style.boxShadow = '';
    passwordInput.style.boxShadow = '';
}

function showSuccessMessage(message) {
    // Create success message element
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.innerHTML = `
        <svg class="success-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
            <polyline points="22,4 12,14.01 9,11.01"/>
        </svg>
        <span>${message}</span>
    `;
    
    // Add success styles
    successDiv.style.cssText = `
        background: linear-gradient(135deg, #d4edda, #c3e6cb);
        color: #155724;
        padding: 12px 16px;
        border-radius: 12px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 0.9rem;
        font-weight: 500;
        border: 1px solid #28a745;
        box-shadow: 4px 4px 8px rgba(40, 167, 69, 0.2), -4px -4px 8px rgba(255, 255, 255, 0.8);
        animation: fadeInUp 0.5s ease-out;
    `;
    
    // Add success icon styles
    const successIcon = successDiv.querySelector('.success-icon');
    successIcon.style.cssText = `
        width: 18px;
        height: 18px;
        flex-shrink: 0;
    `;
    
    // Insert before form
    loginForm.parentNode.insertBefore(successDiv, loginForm);
    
    // Remove after 3 seconds
    setTimeout(() => {
        successDiv.remove();
    }, 3000);
}

function setLoadingState(isLoading) {
    loginButton.disabled = isLoading;
    
    if (isLoading) {
        buttonText.classList.add('hidden');
        loadingSpinner.classList.remove('hidden');
    } else {
        buttonText.classList.remove('hidden');
        loadingSpinner.classList.add('hidden');
    }
}

function togglePasswordVisibility() {
    const isPassword = passwordInput.type === 'password';
    
    passwordInput.type = isPassword ? 'text' : 'password';
    
    if (isPassword) {
        eyeOpenIcon.classList.add('hidden');
        eyeClosedIcon.classList.remove('hidden');
    } else {
        eyeOpenIcon.classList.remove('hidden');
        eyeClosedIcon.classList.add('hidden');
    }
    
    // Add button animation
    togglePasswordButton.style.transform = 'scale(0.95)';
    setTimeout(() => {
        togglePasswordButton.style.transform = 'scale(1)';
    }, 100);
}

function loadSavedCredentials() {
    const rememberedUsername = localStorage.getItem('rememberedUsername');
    const rememberMe = localStorage.getItem('rememberMe') === 'true';
    
    if (rememberMe && rememberedUsername) {
        usernameInput.value = rememberedUsername;
        rememberMeCheckbox.checked = true;
    }
}

function resetForm() {
    loginForm.reset();
    clearError();
    passwordInput.type = 'password';
    eyeOpenIcon.classList.remove('hidden');
    eyeClosedIcon.classList.add('hidden');
    
    // Reload saved credentials
    loadSavedCredentials();
}

// Add some interactive effects
document.addEventListener('mousemove', function(e) {
    // Subtle parallax effect for the background
    const x = e.clientX / window.innerWidth;
    const y = e.clientY / window.innerHeight;
    
    document.body.style.backgroundPosition = `${x * 20}px ${y * 20}px`;
});

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + K to focus username
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        usernameInput.focus();
    }
    
    // Escape to clear form
    if (e.key === 'Escape') {
        clearError();
        if (document.activeElement) {
            document.activeElement.blur();
        }
    }
});
