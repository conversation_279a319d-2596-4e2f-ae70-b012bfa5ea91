{"version": 3, "sources": ["../../../src/shared/lib/app-router-context.shared-runtime.ts"], "names": ["React", "CacheStates", "LAZY_INITIALIZED", "DATA_FETCH", "READY", "AppRouterContext", "createContext", "LayoutRouterContext", "GlobalLayoutRouterContext", "TemplateContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAAA;AAWA,OAAOA,WAAW,QAAO;WAKlB;UAAKC,WAAW;IAAXA,YACVC,sBAAmB;IADTD,YAEVE,gBAAa;IAFHF,YAGVG,WAAAA;GAHUH,gBAAAA;AA0FZ,OAAO,MAAMI,mBAAmBL,MAAMM,aAAa,CACjD,MACD;AACD,OAAO,MAAMC,sBAAsBP,MAAMM,aAAa,CAInD,MAAY;AACf,OAAO,MAAME,4BAA4BR,MAAMM,aAAa,CAUzD,MAAY;AAEf,OAAO,MAAMG,kBAAkBT,MAAMM,aAAa,CAAkB,MAAY;AAEhF,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCP,iBAAiBQ,WAAW,GAAG;IAC/BN,oBAAoBM,WAAW,GAAG;IAClCL,0BAA0BK,WAAW,GAAG;IACxCJ,gBAAgBI,WAAW,GAAG;AAChC"}