{"version": 3, "sources": ["../../../src/server/app-render/create-error-handler.tsx"], "names": ["stringHash", "formatServerError", "SpanStatusCode", "getTracer", "isAbortError", "isDynamicUsageError", "createErrorHandler", "_source", "dev", "isNextExport", "errorLogger", "capturedErrors", "allCapturedErrors", "silenceLogger", "err", "push", "digest", "message", "includes", "span", "getActiveScopeSpan", "recordException", "setStatus", "code", "ERROR", "catch", "process", "env", "NODE_ENV", "logAppDirError", "require", "console", "error", "stack", "toString"], "mappings": "AAAA,OAAOA,gBAAgB,iCAAgC;AACvD,SAASC,iBAAiB,QAAQ,gCAA+B;AACjE,SAASC,cAAc,EAAEC,SAAS,QAAQ,sBAAqB;AAC/D,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,mBAAmB,QAAQ,8CAA6C;AAIjF;;;;CAIC,GACD,OAAO,SAASC,mBAAmB,EACjC;;GAEC,GACDC,OAAO,EACPC,GAAG,EACHC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,iBAAiB,EACjBC,aAAa,EASd;IACC,OAAO,CAACC;YAoBFA;QAnBJ,IAAIF,mBAAmBA,kBAAkBG,IAAI,CAACD;QAE9C,IAAIT,oBAAoBS,MAAM;YAC5B,OAAOA,IAAIE,MAAM;QACnB;QAEA,8DAA8D;QAC9D,IAAIZ,aAAaU,MAAM;QAEvB,yEAAyE;QACzE,IAAIN,KAAK;YACPP,kBAAkBa;QACpB;QACA,kCAAkC;QAClC,8BAA8B;QAC9B,+CAA+C;QAC/C,IACE,CACEL,CAAAA,iBACAK,wBAAAA,eAAAA,IAAKG,OAAO,qBAAZH,aAAcI,QAAQ,CACpB,4FACF,GAEF;YACA,oDAAoD;YACpD,MAAMC,OAAOhB,YAAYiB,kBAAkB;YAC3C,IAAID,MAAM;gBACRA,KAAKE,eAAe,CAACP;gBACrBK,KAAKG,SAAS,CAAC;oBACbC,MAAMrB,eAAesB,KAAK;oBAC1BP,SAASH,IAAIG,OAAO;gBACtB;YACF;YAEA,IAAI,CAACJ,eAAe;gBAClB,IAAIH,aAAa;oBACfA,YAAYI,KAAKW,KAAK,CAAC,KAAO;gBAChC,OAAO;oBACL,kEAAkE;oBAClE,mCAAmC;oBACnC,mEAAmE;oBACnE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;wBACzC,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;wBACVD,eAAef;oBACjB;oBACA,IAAIY,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;wBACzCG,QAAQC,KAAK,CAAClB;oBAChB;gBACF;YACF;QACF;QAEAH,eAAeI,IAAI,CAACD;QACpB,+EAA+E;QAC/E,OAAOd,WAAWc,IAAIG,OAAO,GAAGH,IAAImB,KAAK,GAAInB,CAAAA,IAAIE,MAAM,IAAI,EAAC,GAAIkB,QAAQ;IAC1E;AACF"}