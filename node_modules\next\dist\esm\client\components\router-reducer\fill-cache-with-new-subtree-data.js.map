{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-cache-with-new-subtree-data.ts"], "names": ["CacheStates", "invalidateCacheByRouterState", "fillLazyItemsTillLeafWithHead", "createRouterCache<PERSON>ey", "fillCacheWithNewSubTreeData", "newCache", "existingCache", "flightDataPath", "wasPrefetched", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "existingChildCacheNode", "childCacheNode", "data", "seedData", "subTreeData", "status", "READY", "slice"], "mappings": "AAAA,SAASA,WAAW,QAAQ,wDAAuD;AAMnF,SAASC,4BAA4B,QAAQ,qCAAoC;AACjF,SAASC,6BAA6B,QAAQ,wCAAuC;AACrF,SAASC,oBAAoB,QAAQ,4BAA2B;AAEhE;;CAEC,GACD,OAAO,SAASC,4BACdC,QAAmB,EACnBC,aAAwB,EACxBC,cAA8B,EAC9BC,aAAuB;IAEvB,MAAMC,cAAcF,eAAeG,MAAM,IAAI;IAC7C,MAAM,CAACC,kBAAkBC,QAAQ,GAAGL;IAEpC,MAAMM,WAAWV,qBAAqBS;IAEtC,MAAME,0BACJR,cAAcS,cAAc,CAACC,GAAG,CAACL;IAEnC,IAAI,CAACG,yBAAyB;QAC5B,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIG,kBAAkBZ,SAASU,cAAc,CAACC,GAAG,CAACL;IAClD,IAAI,CAACM,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BT,SAASU,cAAc,CAACI,GAAG,CAACR,kBAAkBM;IAChD;IAEA,MAAMG,yBAAyBN,wBAAwBE,GAAG,CAACH;IAC3D,IAAIQ,iBAAiBJ,gBAAgBD,GAAG,CAACH;IAEzC,IAAIJ,aAAa;QACf,IACE,CAACY,kBACD,CAACA,eAAeC,IAAI,IACpBD,mBAAmBD,wBACnB;YACA,MAAMG,WAA8BhB,cAAc,CAAC,EAAE;YACrD,MAAMiB,cAAcD,QAAQ,CAAC,EAAE;YAC/BF,iBAAiB;gBACfI,QAAQzB,YAAY0B,KAAK;gBACzBJ,MAAM;gBACNE;gBACA,oEAAoE;gBACpET,gBAAgBK,yBACZ,IAAIF,IAAIE,uBAAuBL,cAAc,IAC7C,IAAIG;YACV;YAEA,IAAIE,wBAAwB;gBAC1BnB,6BACEoB,gBACAD,wBACAb,cAAc,CAAC,EAAE;YAErB;YAEAL,8BACEmB,gBACAD,wBACAb,cAAc,CAAC,EAAE,EACjBgB,UACAhB,cAAc,CAAC,EAAE,EACjBC;YAGFS,gBAAgBE,GAAG,CAACN,UAAUQ;QAChC;QACA;IACF;IAEA,IAAI,CAACA,kBAAkB,CAACD,wBAAwB;QAC9C,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIC,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfI,QAAQJ,eAAeI,MAAM;YAC7BH,MAAMD,eAAeC,IAAI;YACzBE,aAAaH,eAAeG,WAAW;YACvCT,gBAAgB,IAAIG,IAAIG,eAAeN,cAAc;QACvD;QACAE,gBAAgBE,GAAG,CAACN,UAAUQ;IAChC;IAEAjB,4BACEiB,gBACAD,wBACAb,eAAeoB,KAAK,CAAC,IACrBnB;AAEJ"}