<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شركة التوصيل السريع - تسجيل الدخول</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Logo and Company Name -->
        <div class="logo-section">
            <div class="logo-container">
                <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2"/>
                    <path d="M15 18H9"/>
                    <path d="M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14"/>
                    <circle cx="17" cy="18" r="2"/>
                    <circle cx="7" cy="18" r="2"/>
                </svg>
            </div>
            <h1 class="company-name">شركة التوصيل السريع</h1>
            <p class="company-subtitle">نظام إدارة التوصيل</p>
        </div>

        <!-- Login Form -->
        <div class="login-card">
            <h2 class="login-title">تسجيل الدخول</h2>
            
            <!-- Error Message -->
            <div id="error-message" class="error-message hidden">
                <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="15" y1="9" x2="9" y2="15"/>
                    <line x1="9" y1="9" x2="15" y2="15"/>
                </svg>
                <span id="error-text">رسالة الخطأ</span>
            </div>

            <form id="login-form" class="login-form">
                <!-- Username Field -->
                <div class="input-group">
                    <label for="username" class="input-label">اسم المستخدم</label>
                    <div class="input-container">
                        <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                        <input type="text" id="username" name="username" class="input-field" placeholder="أدخل اسم المستخدم" required>
                    </div>
                </div>

                <!-- Password Field -->
                <div class="input-group">
                    <label for="password" class="input-label">كلمة المرور</label>
                    <div class="input-container">
                        <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                            <circle cx="12" cy="16" r="1"/>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                        </svg>
                        <input type="password" id="password" name="password" class="input-field" placeholder="أدخل كلمة المرور" required>
                        <button type="button" id="toggle-password" class="password-toggle">
                            <svg id="eye-open" class="eye-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                <circle cx="12" cy="12" r="3"/>
                            </svg>
                            <svg id="eye-closed" class="eye-icon hidden" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                                <line x1="1" y1="1" x2="23" y2="23"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Remember Me Checkbox -->
                <div class="checkbox-group">
                    <input type="checkbox" id="remember-me" name="remember-me" class="checkbox">
                    <label for="remember-me" class="checkbox-label">تذكرني</label>
                </div>

                <!-- Login Button -->
                <button type="submit" id="login-button" class="login-button">
                    <span id="button-text">تسجيل الدخول</span>
                    <div id="loading-spinner" class="loading-spinner hidden">
                        <div class="spinner"></div>
                        <span>جاري تسجيل الدخول...</span>
                    </div>
                </button>
            </form>

            <!-- Demo Credentials -->
            <div class="demo-info">
                <p class="demo-title">بيانات تجريبية للاختبار:</p>
                <p class="demo-text">اسم المستخدم: <strong>admin</strong></p>
                <p class="demo-text">كلمة المرور: <strong>123456</strong></p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>© 2024 شركة التوصيل السريع. جميع الحقوق محفوظة.</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
